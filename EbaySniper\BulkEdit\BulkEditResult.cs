using System.Collections.Generic;

namespace uBuyFirst.BulkEdit
{
    /// <summary>
    /// Result class for bulk property editing operations
    /// </summary>
    public class BulkEditResult
    {
        /// <summary>
        /// Indicates if the bulk operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of keywords successfully processed
        /// </summary>
        public int ProcessedCount { get; set; }

        /// <summary>
        /// Total number of keywords that were attempted to be processed
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// List of error messages encountered during the operation
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Gets a summary message of the operation result
        /// </summary>
        /// <returns>Human-readable summary of the operation</returns>
        public string GetSummary()
        {
            if (Success)
            {
                return $"Successfully updated {ProcessedCount} search terms.";
            }
            else
            {
                var errorCount = TotalCount - ProcessedCount;
                return $"Updated {ProcessedCount} of {TotalCount} search terms. {errorCount} failed.";
            }
        }

        /// <summary>
        /// Gets all error messages as a single formatted string
        /// </summary>
        /// <returns>Formatted error messages</returns>
        public string GetErrorsText()
        {
            return string.Join("\n", Errors);
        }
    }
}
