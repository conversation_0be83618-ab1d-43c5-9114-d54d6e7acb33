using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Prefs;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class DailySpendServiceTests
    {
        private DailySpendService _service;
        private Dictionary<DateTime, decimal> _originalSpendHistory;
        private decimal _originalSpendLimit;

        [TestInitialize]
        public void Setup()
        {
            // Save original values to restore after tests
            _originalSpendHistory = UserSettings.DailySpendHistory;
            _originalSpendLimit = UserSettings.DailySpendLimit;

            // Initialize clean state for each test
            UserSettings.DailySpendHistory = new Dictionary<DateTime, decimal>();
            UserSettings.DailySpendLimit = 1000m; // $1000 default for tests

            _service = new DailySpendService();
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Restore original values
            UserSettings.DailySpendHistory = _originalSpendHistory;
            UserSettings.DailySpendLimit = _originalSpendLimit;
        }

        [TestMethod]
        public void GetTodaySpent_WhenNoSpendingToday_ReturnsZero()
        {
            // Act
            var result = _service.GetTodaySpent();

            // Assert
            Assert.AreEqual(0m, result);
        }

        [TestMethod]
        public void GetTodaySpent_WhenSpendingExists_ReturnsCorrectAmount()
        {
            // Arrange
            var today = DateTime.Today;
            UserSettings.DailySpendHistory[today] = 250.50m;

            // Act
            var result = _service.GetTodaySpent();

            // Assert
            Assert.AreEqual(250.50m, result);
        }

        [TestMethod]
        public void GetDailyLimit_ReturnsUserSettingsValue()
        {
            // Arrange
            UserSettings.DailySpendLimit = 2500m;

            // Act
            var result = _service.GetDailyLimit();

            // Assert
            Assert.AreEqual(2500m, result);
        }

        [TestMethod]
        public void SetDailyLimit_ValidAmount_UpdatesUserSettings()
        {
            // Act
            _service.SetDailyLimit(1500m);

            // Assert
            Assert.AreEqual(1500m, UserSettings.DailySpendLimit);
        }

        [TestMethod]
        public void SetDailyLimit_NegativeAmount_DoesNotUpdate()
        {
            // Arrange
            var originalLimit = UserSettings.DailySpendLimit;

            // Act
            _service.SetDailyLimit(-100m);

            // Assert
            Assert.AreEqual(originalLimit, UserSettings.DailySpendLimit);
        }

        [TestMethod]
        public void CanPurchase_WithinLimit_ReturnsTrue()
        {
            // Arrange
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory[DateTime.Today] = 500m;

            // Act
            var result = _service.CanPurchase(400m);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void CanPurchase_ExceedsLimit_ReturnsFalse()
        {
            // Arrange
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory[DateTime.Today] = 500m;

            // Act
            var result = _service.CanPurchase(600m);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void CanPurchase_ExactlyAtLimit_ReturnsFalse()
        {
            // Arrange
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory[DateTime.Today] = 500m;

            // Act
            var result = _service.CanPurchase(500m);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void CanPurchase_NegativeAmount_ReturnsFalse()
        {
            // Act
            var result = _service.CanPurchase(-100m);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void GetRemainingBudget_WithSpending_ReturnsCorrectAmount()
        {
            // Arrange
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory[DateTime.Today] = 300m;

            // Act
            var result = _service.GetRemainingBudget();

            // Assert
            Assert.AreEqual(700m, result);
        }

        [TestMethod]
        public void GetRemainingBudget_ExceedsLimit_ReturnsZero()
        {
            // Arrange
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory[DateTime.Today] = 1200m;

            // Act
            var result = _service.GetRemainingBudget();

            // Assert
            Assert.AreEqual(0m, result);
        }

        [TestMethod]
        public void RecordPurchase_ValidAmount_UpdatesTodaySpending()
        {
            // Arrange
            UserSettings.DailySpendHistory[DateTime.Today] = 100m;

            // Act
            _service.RecordPurchase(50m);

            // Assert
            Assert.AreEqual(150m, UserSettings.DailySpendHistory[DateTime.Today]);
        }

        [TestMethod]
        public void RecordPurchase_FirstPurchaseToday_CreatesNewEntry()
        {
            // Act
            _service.RecordPurchase(75m);

            // Assert
            Assert.AreEqual(75m, UserSettings.DailySpendHistory[DateTime.Today]);
        }

        [TestMethod]
        public void RecordPurchase_ZeroAmount_DoesNotUpdate()
        {
            // Arrange
            var originalCount = UserSettings.DailySpendHistory.Count;

            // Act
            _service.RecordPurchase(0m);

            // Assert
            Assert.AreEqual(originalCount, UserSettings.DailySpendHistory.Count);
        }

        [TestMethod]
        public void RecordPurchase_NegativeAmount_DoesNotUpdate()
        {
            // Arrange
            var originalCount = UserSettings.DailySpendHistory.Count;

            // Act
            _service.RecordPurchase(-50m);

            // Assert
            Assert.AreEqual(originalCount, UserSettings.DailySpendHistory.Count);
        }

        [TestMethod]
        public void GetSpentOnDate_ExistingDate_ReturnsCorrectAmount()
        {
            // Arrange
            var testDate = new DateTime(2024, 1, 15);
            UserSettings.DailySpendHistory[testDate] = 123.45m;

            // Act
            var result = _service.GetSpentOnDate(testDate);

            // Assert
            Assert.AreEqual(123.45m, result);
        }

        [TestMethod]
        public void GetSpentOnDate_NonExistingDate_ReturnsZero()
        {
            // Arrange
            var testDate = new DateTime(2024, 1, 15);

            // Act
            var result = _service.GetSpentOnDate(testDate);

            // Assert
            Assert.AreEqual(0m, result);
        }

        [TestMethod]
        public void CleanupOldRecords_RemovesOldEntries()
        {
            // Arrange
            var today = DateTime.Today;
            var oldDate = today.AddDays(-35); // Older than 30 days
            var recentDate = today.AddDays(-15); // Within 30 days

            UserSettings.DailySpendHistory[oldDate] = 100m;
            UserSettings.DailySpendHistory[recentDate] = 200m;
            UserSettings.DailySpendHistory[today] = 300m;

            // Act
            _service.CleanupOldRecords();

            // Assert
            Assert.IsFalse(UserSettings.DailySpendHistory.ContainsKey(oldDate));
            Assert.IsTrue(UserSettings.DailySpendHistory.ContainsKey(recentDate));
            Assert.IsTrue(UserSettings.DailySpendHistory.ContainsKey(today));
        }

        [TestMethod]
        public void CleanupOldRecords_NullHistory_DoesNotThrow()
        {
            // Arrange
            UserSettings.DailySpendHistory = null;

            // Act & Assert - Should not throw
            _service.CleanupOldRecords();
        }
    }
}
