﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using CefSharp;
using CefSharp.WinForms;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.Auth;
using uBuyFirst.CefBrowser;
using uBuyFirst.Data;
using uBuyFirst.ExternalData;
using uBuyFirst.GUI;
using uBuyFirst.Images;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Views
{
    [Obfuscation(Exclude = true)]
    public class GuiChanger
    {
        private readonly WebBrowser _webBrowser1;
        private readonly GalleryControl _galleryControl;
        private readonly Form1 _instanceForm1;
        private readonly CefBrowserManager _cefBrowserManager;
        private readonly PanelControl _panelCefBrowser;
        //Cancellation
        internal CancellationToken CancelTokenOnRowChange;
        internal CancellationTokenSource CancelTokenSourceOnRowChange = new CancellationTokenSource();
        public GuiChanger(Form1 instanceForm1, CefBrowserManager cefBrowserManager, PanelControl panelCefBrowser)
        {
            _panelCefBrowser = panelCefBrowser;
            _cefBrowserManager = cefBrowserManager;
            _instanceForm1 = instanceForm1;
            _webBrowser1 = instanceForm1.webBrowser1;
            _galleryControl = instanceForm1.galleryControl1;
        }

        public void UpdateBrowser(string itemID, DataRow row, CancellationToken cancelTokenOnRowChange)
        {
            try
            {
                //TODO: Faster replacer, stringbuilder?
                SetBrowserBody("");

                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                    return;

                var html = row["Description"].ToString().Trim();

                if (html.Length == 0)
                {
                    DownloadDescription(itemID, row, cancelTokenOnRowChange);

                    return;
                }

                var htmlEnding = $"<span id='hwv{UserSettings.HighlightsvaluesVersion}'></span>";
                if (html.EndsWith(htmlEnding))
                {
                    SetBrowserBody(html);

                    return;
                }

                PerformWordReplacement(cancelTokenOnRowChange, html, row);
            }
            catch (OperationCanceledException)
            {
            }
            catch (IOException ioex)
            {
                if (Debugger.IsAttached)
                {
                    MessageBox.Show($@"avatar load: {ioex.Message}
{ioex.TargetSite}");
                }
            }
            catch (COMException ex)
            {
                if (Debugger.IsAttached)
                {
                    MessageBox.Show($"UpdateBrowser: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("UpdateBrowser: ", ex);
            }
        }

        private void DownloadDescription(string itemID, DataRow row, CancellationToken cancelTokenOnRowChange)
        {
            SetBrowserBody("...");
            var domain = "vi.vipr.ebaydesc.com";
            if (Program.Sandbox)
                domain = "vi.sandbox.ebaydesc.com";
            var url = $"https://{domain}/itmdesc/{itemID}";
            try
            {
                var webClient = new WebClient();
                webClient.DownloadStringCompleted += delegate (object o, DownloadStringCompletedEventArgs args) { WebClient_DownloadStringCompleted(args, row, cancelTokenOnRowChange); };
                using (cancelTokenOnRowChange.Register(() => webClient.CancelAsync()))
                    webClient.DownloadStringAsync(new Uri(url, UriKind.Absolute));
            }
            catch (WebException ex) when (ex.Status == WebExceptionStatus.RequestCanceled)
            {
                // ignore this exception
            }
        }

        private void PerformWordReplacement(CancellationToken cancelTokenOnRowChange, string html, DataRow row)
        {
            Task.Run(() =>
            {
                html = "###" + html + "###";

                if (Regex.IsMatch(html, "<embed", RegexOptions.IgnoreCase))
                    html = Regex.Replace(html, "<embed", "<none", RegexOptions.IgnoreCase);

                if (Regex.IsMatch(html, "<object", RegexOptions.IgnoreCase))
                    html = Regex.Replace(html, "<object", "<none", RegexOptions.IgnoreCase);

                if (ReplaceWords(cancelTokenOnRowChange, ref html, UserSettings.Highlightsvalues.Words1, UserSettings.Highlightsvalues.HexColor1))
                    return "";

                if (ReplaceWords(cancelTokenOnRowChange, ref html, UserSettings.Highlightsvalues.Words2, UserSettings.Highlightsvalues.HexColor2))
                    return "";

                if (ReplaceWords(cancelTokenOnRowChange, ref html, UserSettings.Highlightsvalues.Words3, UserSettings.Highlightsvalues.HexColor3))
                    return "";

                html = html.Trim('#');

                if (cancelTokenOnRowChange.IsCancellationRequested)
                    return "";

                if (html.Contains("<body>"))
                {
                    //html = html.Replace("<style>", "<style>html,body,h1,h2,h3,p,div,table,td,tr,th,span {background-color: " + UserSettings.BrowserBg + "!important;}</style>");
                }

                var htmlEnding = $"<span id='hwv{UserSettings.HighlightsvaluesVersion}'></span>";
                if (!html.EndsWith(htmlEnding))
                    html += htmlEnding;

                return html;
            }, cancelTokenOnRowChange).ContinueWith(t =>
            {
                if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                    return;

                SetBrowserBody(html);
                row["Description"] = html;
            }, cancelTokenOnRowChange, TaskContinuationOptions.OnlyOnRanToCompletion, TaskScheduler.FromCurrentSynchronizationContext());
        }

        private static bool ReplaceWords(CancellationToken cancelTokenOnRowChange, ref string html, string[] highlightsValuesWords1, string highlightsValuesHexColor1)
        {
            foreach (string word in highlightsValuesWords1)
            {
                if (cancelTokenOnRowChange.IsCancellationRequested)
                    return true;

                if (word.StartsWith("\"") && word.EndsWith("\""))
                {
                    var pattern = $"(\\b{word.Replace("\"", "")}\\b)(?![^<]*>)";
                    var replacement = $"<font style=\"BACKGROUND-COLOR: {highlightsValuesHexColor1}\">$1</FONT>";
                    html = Regex.Replace(html, pattern, replacement, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
                else
                {
                    var pattern = $"(\\b{word}\\w*\\b)(?![^<]*>)";
                    var replacement = $"<font style=\"BACKGROUND-COLOR: {highlightsValuesHexColor1}\">$1</FONT>";
                    html = Regex.Replace(html, pattern, replacement, RegexOptions.IgnoreCase | RegexOptions.Singleline);
                }
            }

            return false;
        }

        private void WebClient_DownloadStringCompleted(DownloadStringCompletedEventArgs e, DataRow row, CancellationToken cancelTokenOnRowChange)
        {
            if (e.Cancelled || e.Error != null)
                return;

            var html = e.Result;
            if (row.RowState != DataRowState.Deleted && row.RowState != DataRowState.Detached)
                row["Description"] = html;

            PerformWordReplacement(cancelTokenOnRowChange, html, row);
        }

        public void SetBrowserDocumentText(string text)
        {
            _webBrowser1.DocumentText = @"<html><head><style>html,body,h1,h2,h3,p,div,table,td,tr,th,span {background-color: "
                                        + UserSettings.BrowserBg
                                        + "!important;}</style></head><body>"
                                        + text
                                        + "</body></html>";
        }

        public Task SetBrowserBody(string text)
        {
            var tcs = new TaskCompletionSource<bool>();
            try
            {

                if (_webBrowser1.IsDisposed || _webBrowser1.Disposing)
                    return tcs.Task;

                if (_webBrowser1.Document?.Body != null)
                {
                    Task.Run(() =>
                    {
                        // Perform long-running operations here
                        // ...

                        // After completing the operations, update the UI
                        try
                        {
                            _webBrowser1.Invoke((Action)(() =>
                            {
                                _webBrowser1.Document.Body.InnerHtml = text;
                            }));
                        }
                        catch (Exception e)
                        {
                            // Handle exceptions that occur during UI update
                            Console.WriteLine("Error updating UI: " + e.Message);
                        }

                    }).ContinueWith(task =>
                    {
                        if (task.Exception != null)
                        {
                            // Handle exceptions from the background task
                            Console.WriteLine("Background task error: " + task.Exception.InnerException.Message);
                        }
                    }, TaskScheduler.FromCurrentSynchronizationContext()); // Ensure continuation runs on UI thread
                }
                else
                    _webBrowser1.DocumentText = @"<html><head><style>html,body,h1,h2,h3,p,div,table,td,tr,th,span {background-color: "
                                                + UserSettings.BrowserBg
                                                + "!important;}</style></head><body>"
                                                + text
                                                + "</body></html>";

            }
            catch (Exception)
            {
                if (Debugger.IsAttached)
                    throw;
            }

            tcs.SetResult(true);
            return tcs.Task;
        }

        public void UpdatePicPanel(List<string> pictures, CancellationToken ctOnRowChange)
        {
            if (SetupPicProcessing())
            {
                return;
            }

            foreach (var picture in pictures)
            {
                if (ctOnRowChange is { IsCancellationRequested: true })
                    return;
                _galleryControl.Gallery.ShowItemText = true;
                var galleryItem = new GalleryItem(null, null, "", "", 0, 0, picture, "");
                _galleryControl.Gallery.Groups.Last().Items.Add(galleryItem);

                var downloadImageTask = LoadImageAsync(picture, ctOnRowChange);
                downloadImageTask.ContinueWith(downloadResult =>
                {

                    var (image, hoverImage, _, _) = downloadResult.Result;

                    if (image == null || ctOnRowChange is { IsCancellationRequested: true })
                    {
                        galleryItem.Hint = "Error";
                        galleryItem.ImageOptions.SvgImage = Properties.Resources.Reload;
                        Debug.WriteLine($"{DateTime.UtcNow.Second} ContinueWith :" + "Null");
                        return;
                    }

                    if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                        return;

                    Debug.WriteLine($"{DateTime.UtcNow.Second} ContinueWith :" + "Good");
                    galleryItem.Hint = "";
                    galleryItem.Caption = "";
                    galleryItem.Description = "";
                    galleryItem.ImageOptions.SvgImage = null;
                    galleryItem.Image = image;
                    galleryItem.HoverImage = hoverImage;

                }, TaskScheduler.FromCurrentSynchronizationContext());

                // The task that completes after 1 second to check if the image download is taking too long
                var delayTask = Task.Delay(200);

                Task
                    .WhenAny(downloadImageTask, delayTask)
                    .ContinueWith(t =>
                    {
                        if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                            return;

                        if (t.Result == delayTask)
                        {
                            galleryItem.ImageOptions.SvgImage = Properties.Resources.SquareEmpty;
                            galleryItem.ImageOptions.SvgImageSize = new Size(16, 16);
                            galleryItem.Hint = "";
                            galleryItem.Caption = "";
                            galleryItem.Description = "";

                        }
                    }, TaskScheduler.FromCurrentSynchronizationContext());
            }
        }

        private async Task<(Image Image, Image HoverImage, string Picture, bool TookTooLong)> LoadImageAsync(string picture, CancellationToken ct)
        {
            if (ct is { IsCancellationRequested: true })
            {
                return (null, null, picture, false);
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                var imagePath = await ImageTools.GetImageFromDiskOrInternet(picture);

                if (!File.Exists(imagePath) || ct is { IsCancellationRequested: true })
                {
                    return (null, null, picture, false);
                }

                var image = await ImageTools.ReadFileToBitmap(imagePath);

                if (image == null || ct is { IsCancellationRequested: true })
                {
                    return (null, null, picture, false);
                }

                if (image.Size.Height > 500 || image.Size.Width > 500)
                {
                    //if (!Form1.Instance.chkLargeImagesOnHover.Checked)
                    {
                        image = await Task.Run(() => ImageProcessor.ScaleImage(image, 500, 500));
                    }
                }

                stopwatch.Stop();

                return (image, image, picture, stopwatch.Elapsed.TotalSeconds > 1);
            }
            catch (Exception)
            {
                return (null, null, picture, false);
            }
        }

        private static async Task<List<GalleryItem>?> FetchGalleryItems(List<string> pictures, CancellationToken ctOnRowChange)
        {
            var itemList = new List<GalleryItem>();

            foreach (var picture in pictures)
            {
                if (ctOnRowChange.IsCancellationRequested)
                {
                    return null;
                }

                var imagePath = await ImageTools.GetImageFromDiskOrInternet(picture);

                if (!File.Exists(imagePath))
                {
                    continue;
                }

                if (ctOnRowChange.IsCancellationRequested)
                {
                    return null;
                }

                try
                {
                    var image = await ImageTools.ReadFileToBitmap(imagePath);
                    if (image != null)
                    {
                        if (image.Size.Height > 500 || image.Size.Width > 500)
                            //if (!Form1.Instance.chkLargeImagesOnHover.Checked)
                            image = ImageProcessor.ScaleImage(image, 500, 500);
                        var galleryItem = new GalleryItem(image, image, "", " ", 0, 0, picture, "");
                        itemList.Add(galleryItem);
                    }

                    if (ctOnRowChange.IsCancellationRequested)
                    {
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    // ignored
                }
            }

            return itemList;
        }

        private bool SetupPicProcessing()
        {
            if (((DockPanel)_galleryControl.Parent.Parent.Parent.Parent).Visibility != DockVisibility.Visible)
            {
                return true;
            }
            return false;
        }

        public void UpdateButtons(DataList d)
        {
            if (Program.Sandbox || d.CommitToBuy != true || d.Variation)
            {
                //if (Form1.EBayAccountsList.Count == 0)
                {
                    var f = _instanceForm1.barButtonBuy.Appearance.Font;
                    _instanceForm1.barButtonBuy.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Underline, f.Unit, f.GdiCharSet);
                    _instanceForm1.panelBuyButton.Appearance.Font = new Font(_instanceForm1.panelBuyButton.Appearance.Font.ToString(), _instanceForm1.panelBuyButton.Appearance.Font.Size,
                        FontStyle.Underline, _instanceForm1.panelBuyButton.Appearance.Font.Unit, _instanceForm1.Appearance.Font.GdiCharSet);
                }
            }
            else
            {
                //  if (Form1.EBayAccountsList.Count == 0)
                {
                    var f = _instanceForm1.barButtonBuy.Appearance.Font;
                    _instanceForm1.barButtonBuy.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Regular, f.Unit, f.GdiCharSet);
                    _instanceForm1.panelBuyButton.Appearance.Font = new Font(_instanceForm1.panelBuyButton.Appearance.Font.ToString(), _instanceForm1.panelBuyButton.Appearance.Font.Size,
                        FontStyle.Regular, _instanceForm1.panelBuyButton.Appearance.Font.Unit, _instanceForm1.Appearance.Font.GdiCharSet);
                }
            }

            if (d.BestOffer)
            {
                _instanceForm1.barButtonMakeOffer.Enabled = true;
                _instanceForm1.btnMakeOffer.Enabled = true;
            }
            else
            {
                _instanceForm1.barButtonMakeOffer.Enabled = false;
                _instanceForm1.btnMakeOffer.Enabled = false;
            }
        }

        public CancellationToken ResetCancelToken()
        {
            CancelTokenSourceOnRowChange.Cancel();
            CancelTokenSourceOnRowChange = new CancellationTokenSource();
            CancelTokenOnRowChange = CancelTokenSourceOnRowChange.Token;

            return CancelTokenOnRowChange;
        }

        public void UpdateItemDetailsPanel(DataList d)
        {
            _instanceForm1.linkReportItem.Tag = "http://www.ebay.com/itm/" + ProgramState.LastSelectedItemID;

            _instanceForm1.lcItemID.Text = "<href=" + d.GetAffiliateLink() + ">" + d.ItemID + "</href>";
            _instanceForm1.lcItemID.Tag = d;
            _instanceForm1.lcTerm.Text = d.Term;
            _instanceForm1.lcAlias.Text = d.Term;
            _instanceForm1.lcTitle.Text = d.Title;
            _instanceForm1.lcTotalPrice.Text = d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();
            _instanceForm1.lcCondition.Text = d.Condition;
            _instanceForm1.lcReturns.Text = d.Returns;
            _instanceForm1.lcBestOffer.Text = d.BestOffer.ToString();
            _instanceForm1.lcFoundTime.Text = d.FoundTime?.LocalTime.ToString(CultureInfo.InvariantCulture);
            _instanceForm1.lcLocation.Text = d.Location;
            _instanceForm1.lcFromCountry.Text = d.FromCountry.ToString();
            _instanceForm1.lcToCountry.Text = d.ToCountry;
            _instanceForm1.lcAutoPay.Text = d.CommitToBuy.ToString();
            _instanceForm1.lcCategoryID.Text = d.CategoryID;
            _instanceForm1.lcCategoryName.Text = d.CategoryName;
            _instanceForm1.lcConditionDescription.Text = d.ConditionDescription;
            _instanceForm1.lcFeedbackRating.Text = d.FeedbackRating.ToString(CultureInfo.InvariantCulture);
            _instanceForm1.lcFeedbackScore.Text = d.FeedbackScore.ToString(CultureInfo.InvariantCulture);
            _instanceForm1.lcFeedbackRating.Tag = $"https://www.{d.EBaySite.Domain}/fdbk/feedback_profile/{d.SellerName}?filter=feedback_page:RECEIVED_AS_SELLER";
            _instanceForm1.lcFeedbackScore.Tag = $"https://www.{d.EBaySite.Domain}/fdbk/feedback_profile/{d.SellerName}?filter=feedback_page:RECEIVED_AS_SELLER";
            if (d.ItemPricing?.ItemPrice != null)
                _instanceForm1.lcItemPrice.Text = d.ItemPricing.ItemPrice.FormatPrice();

            _instanceForm1.lcPostedTime.Text = d.StartTimeLocal?.ToString(CultureInfo.InvariantCulture);
            _instanceForm1.lcQuantity.Text = d.QuantityTotal.ToString();
            _instanceForm1.lcPayment.Text = d.Payment;
            _instanceForm1.lcSellerName.Text = d.SellerName;
            _instanceForm1.lcSellerName.Tag = $"https://www.{d.EBaySite.Domain}/contact/sendmsg?item_id={d.ItemID}&recipient={d.SellerName}&message_type_id=14";
            if (Form1.Instance.lciSellerName?.AppearanceItemCaption?.BackColor != null)
                if (_instanceForm1.lcSellerName != null)
                    _instanceForm1.lcSellerName.BackColor = Form1.Instance.lciSellerName.AppearanceItemCaption.BackColor;

            if (d.ItemShipping?.FullSingleShippingPrice != null)
                _instanceForm1.lcShipping.Text = d.ItemShipping.FullSingleShippingPrice.FormatPrice();

            if (d.ItemShipping?.ShipAdditionalItem != null)
                _instanceForm1.lcShipAdditionalItem.Text = d.ItemShipping.ShipAdditionalItem.FormatPrice();

            _instanceForm1.lcShippingType.Text = d.ShippingType.ToString();
            if (d.SoldTime != null)
                _instanceForm1.lcSoldTime.Text = d.SoldTime.ToString();

            _instanceForm1.lcEbayWebsite.Text = d.Site.ToString();
            _instanceForm1.lcPageViews.Text = d.PageViews.ToString();
            _instanceForm1.lcUPC.Text = d.UPC;
            _instanceForm1.lcVariation.Text = d.Variation.ToString();
            if (d.ItemPricing.AuctionPrice != null)
                _instanceForm1.lcAuctionPrice.Text = d.ItemPricing.AuctionPrice.FormatPrice();

            _instanceForm1.lcListingType.Text = d.ListingTypeToString();
            _instanceForm1.lcBids.Text = d.Bids.ToString();
            _instanceForm1.lcShippingDays.Text = d.ShippingDays.ToString();
            _instanceForm1.lcEbayAccount.Properties.Items?.Clear();
            var ebayUsernames = Form1.EBayAccountsList.Select(a =>
            {
                var ebayUsername = a.UserName;

                return ebayUsername;
            });

            _instanceForm1.lcEbayAccount.Properties.Items?.AddRange(ebayUsernames.ToList());
            EbayAccount first = null;
            foreach (var account in Form1.EBayAccountsList)
            {
                if (account.UserName == d.EbayAccount?.UserName)
                {
                    first = account;

                    break;
                }
            }

            _instanceForm1.lcEbayAccount.SelectedItem = first?.UserName;
        }

        public void ClearItemInfoOnZeroRows()
        {
            if (FocusRouter.FocusedGridView != null)
                FocusRouter.FocusedGridView.FocusedRowHandle = GridControl.InvalidRowHandle;

            SetBrowserDocumentText("");
            ExternalPanelClear();
            Form1.Instance.galleryControl1.Gallery?.Groups.Last().Items?.Clear();
            foreach (LabelControl label in Form1.Instance.layoutControl1.Controls.OfType<LabelControl>())
            {
                label.Text = "";
            }
        }

        internal void ClearFoundItems()
        {
            Stat.TotalItemsProcessed = 0;

            while (ImageTools.ImageQueueAvatars.Count > 0)
                ImageTools.ImageQueueAvatars.TryDequeue(out _);

            while (ImageTools.ImageQueueGallery.Count > 0)
                ImageTools.ImageQueueGallery.TryDequeue(out _);

            foreach (var grView in ResultsView.ViewsDict.Values)
            {
                ((AdvBandedGridView)grView.MainView).FocusedRowHandle = GridControl.InvalidRowHandle;
                ((DataTable)grView.DataSource)?.Rows.Clear();
            }

            ClearItemInfoOnZeroRows();
            GC.Collect();
        }

        public void ClearGalleryControl()
        {
            try
            {
                _galleryControl.Gallery.Groups.Add(new GalleryItemGroup());
                _galleryControl.Gallery.Groups.RemoveAt(0);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public void ClearImageCache()
        {
            ImageCache.Inst.ClearCache();
        }

        public void UpdateExternalData(string urlTemplate, DataRow row)
        {
            if (string.IsNullOrEmpty(urlTemplate))
                return;

            var url = ExternalDataManager.BuildExternalDataUrl(urlTemplate, row);
            if (string.IsNullOrEmpty(url))
                return;

            var existingBrowser = ExternalPanelClear();

            if (!UserSettings.ExternalDataEnabled)
                return;

            if (existingBrowser != null)
            {
                ExternalDataManager.SendRequestWithData(existingBrowser, url, row);
                return;
            }

            if (Cef.IsInitialized != true) // Simplified Cef initialization check
                return;

            var newBrowser = _cefBrowserManager.CreateBrowser();
            _panelCefBrowser.Controls.Add(newBrowser);
            newBrowser.Dock = DockStyle.Fill;

            ExternalDataManager.SendRequestWithData(newBrowser, url, row);
        }

        private ChromiumWebBrowser? ExternalPanelClear()
        {
            var existingBrowser = _panelCefBrowser.Controls
                .OfType<ChromiumWebBrowser>()
                .FirstOrDefault();
            if (existingBrowser?.Address != "about:blank")
                existingBrowser?.Load("about:blank");
            return existingBrowser;
        }
    }
}
