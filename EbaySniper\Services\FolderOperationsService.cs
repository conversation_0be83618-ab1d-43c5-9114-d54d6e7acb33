using System;
using System.Collections.Generic;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Services
{
    /// <summary>
    /// Service for handling folder operations in the TreeList, including creation, refresh, and UI management
    /// </summary>
    public class FolderOperationsService
    {
        #region Dependencies

        private readonly TreeList _treeList;
        private readonly QueryList _queryList;
        private readonly Func<object, TreeListNode> _findNodeByDataRecord;
        private readonly Action _refreshAllNodeStates;
        private readonly Action<KeywordFolder> _ensureFolderPathExpanded;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the FolderOperationsService
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="queryList">The QueryList data source</param>
        /// <param name="findNodeByDataRecord">Function to find TreeList node by data record</param>
        /// <param name="refreshAllNodeStates">Action to refresh all node states</param>
        /// <param name="ensureFolderPathExpanded">Action to ensure folder path is expanded</param>
        public FolderOperationsService(
            TreeList treeList,
            QueryList queryList,
            Func<object, TreeListNode> findNodeByDataRecord,
            Action refreshAllNodeStates,
            Action<KeywordFolder> ensureFolderPathExpanded)
        {
            _treeList = treeList ?? throw new ArgumentNullException(nameof(treeList));
            _queryList = queryList ?? throw new ArgumentNullException(nameof(queryList));
            _findNodeByDataRecord = findNodeByDataRecord ?? throw new ArgumentNullException(nameof(findNodeByDataRecord));
            _refreshAllNodeStates = refreshAllNodeStates ?? throw new ArgumentNullException(nameof(refreshAllNodeStates));
            _ensureFolderPathExpanded = ensureFolderPathExpanded ?? throw new ArgumentNullException(nameof(ensureFolderPathExpanded));
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Creates a new folder as a child of the currently focused item
        /// </summary>
        public KeywordFolder CreateSubFolder()
        {
            var parentFolder = DetermineParentFolderForSubFolder();
            return CreateFolderInternal(parentFolder, needsExpansion: true);
        }

        /// <summary>
        /// Creates a new folder at the same level as the currently focused keyword
        /// </summary>
        public KeywordFolder CreateSameLevelFolder()
        {
            var parentFolder = DetermineParentFolderForSameLevel();
            return CreateFolderInternal(parentFolder, needsExpansion: true);
        }

        /// <summary>
        /// Creates a new folder at the root level
        /// </summary>
        public KeywordFolder CreateRootFolder()
        {
            return CreateFolderInternal(parentFolder: null, needsExpansion: false);
        }

        /// <summary>
        /// Ensures that any tree item is visible by expanding its parent folder path
        /// </summary>
        /// <param name="item">The tree item to make visible (KeywordFolder, Keyword2Find, or ChildTerm)</param>
        public void EnsureItemVisible(object item)
        {
            switch (item)
            {
                case KeywordFolder folder:
                    // For folders, expand their parent path
                    _ensureFolderPathExpanded(folder);
                    break;

                case Keyword2Find keyword when keyword.ParentFolder != null:
                    // For keywords, expand the folder containing the keyword to make it visible
                    var folderNode = _findNodeByDataRecord(keyword.ParentFolder);
                    if (folderNode != null && !folderNode.Expanded)
                    {
                        folderNode.Expanded = true;
                    }
                    break;

                case ChildTerm childTerm:
                    // For child terms, expand the keyword containing the child term to make it visible
                    var parentKeyword = childTerm.GetParent();
                    if (parentKeyword != null)
                    {
                        var keywordNode = _findNodeByDataRecord(parentKeyword);
                        if (keywordNode != null && !keywordNode.Expanded)
                        {
                            keywordNode.Expanded = true;
                        }
                    }
                    break;
            }
        }

        #endregion

        #region Private Methods - Parent Determination

        /// <summary>
        /// Determines the parent folder for a subfolder creation
        /// </summary>
        private KeywordFolder DetermineParentFolderForSubFolder()
        {
            var focusedNode = _treeList.FocusedNode;
            if (focusedNode == null) return null;

            var dataRecord = _treeList.GetDataRecordByNode(focusedNode);
            return dataRecord switch
            {
                KeywordFolder folder => folder,
                Keyword2Find keyword => keyword.ParentFolder,
                _ => null
            };
        }

        /// <summary>
        /// Determines the parent folder for a same-level folder creation
        /// </summary>
        private KeywordFolder DetermineParentFolderForSameLevel()
        {
            var focusedNode = _treeList.FocusedNode;
            if (focusedNode == null) return null;

            var dataRecord = _treeList.GetDataRecordByNode(focusedNode);
            if (dataRecord is Keyword2Find keyword)
            {
                return keyword.ParentFolder;
            }

            return null;
        }

        #endregion

        #region Private Methods - Core Folder Creation

        /// <summary>
        /// Core folder creation logic shared by all creation methods
        /// </summary>
        private KeywordFolder CreateFolderInternal(KeywordFolder parentFolder, bool needsExpansion)
        {
            // Generate unique name
            var uniqueName = GenerateUniqueFolderName(parentFolder);

            // Create folder
            var newFolder = CreateFolderObject(uniqueName, parentFolder);

            // Add to appropriate parent collection
            AddFolderToParent(newFolder, parentFolder);

            // Refresh TreeList
            RefreshTreeList();

            // Handle expansion if needed
            if (needsExpansion)
            {
                _ensureFolderPathExpanded(newFolder);
            }

            // Focus and start editing
            StartEditingFolder(newFolder);

            return newFolder;
        }

        /// <summary>
        /// Generates a unique folder name based on the parent folder
        /// </summary>
        private string GenerateUniqueFolderName(KeywordFolder parentFolder)
        {
            const string baseName = "New Folder";

            if (parentFolder != null)
            {
                return KeywordFolder.GenerateUniqueName(baseName, parentFolder.Children);
            }
            else
            {
                return _queryList.GenerateUniqueRootFolderName(baseName);
            }
        }

        /// <summary>
        /// Creates a new KeywordFolder object with the specified properties
        /// </summary>
        private static KeywordFolder CreateFolderObject(string name, KeywordFolder parentFolder)
        {
            return new KeywordFolder
            {
                Name = name,
                Id = Guid.NewGuid().ToString(),
                ParentFolder = parentFolder
            };
        }

        /// <summary>
        /// Adds the folder to the appropriate parent collection
        /// </summary>
        private void AddFolderToParent(KeywordFolder folder, KeywordFolder parentFolder)
        {
            if (parentFolder != null)
            {
                parentFolder.Children.Add(folder);
            }
            else
            {
                _queryList.Folders.Add(folder);
            }
        }

        #endregion

        #region Private Methods - TreeList Operations

        /// <summary>
        /// Refreshes the TreeList after folder creation
        /// </summary>
        private void RefreshTreeList()
        {
            _treeList.RefreshDataSource();
            _refreshAllNodeStates();
        }

        /// <summary>
        /// Focuses the new folder and starts editing its name
        /// </summary>
        private void StartEditingFolder(KeywordFolder folder)
        {
            var newNode = _findNodeByDataRecord(folder);
            if (newNode != null)
            {
                _treeList.FocusedNode = newNode;
                _treeList.FocusedColumn = _treeList.Columns["Alias"];
                _treeList.ShowEditor();
            }
        }

        #endregion
    }
}
