﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using Newtonsoft.Json;

namespace uBuyFirst.Purchasing
{
    /// <summary>
    /// Represents the result of an HTTP request made by HttpServiceHelper.
    /// </summary>
    public class HttpResult
    {
        public bool IsSuccess { get; set; }
        public string Content { get; set; } = string.Empty; // Default to empty string
        public HttpStatusCode? StatusCode { get; set; }
    }

    /// <summary>
    /// Provides helper methods for making common HTTP requests with shared configuration.
    /// </summary>
    public static class HttpServiceHelper
    {
        private static readonly string s_defaultUserAgent = ProgramState.ChromeUA;

        private static HttpClient CreateClient(CookieContainer cookieContainer, bool allowAutoRedirect = false)
        {
            var handler = new HttpClientHandler
            {
                CookieContainer = cookieContainer,
                UseCookies = true,
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                AllowAutoRedirect = allowAutoRedirect
            };
            return new HttpClient(handler);
        }

        private static void SetCommonHeaders(HttpClient client, Dictionary<string, string>? headers, Uri? referrer, string? origin, string? acceptLanguage = null)
        {
            // Dynamically set User-Agent and related headers like in NetTools.FetchUrlUsingCookiesAsync
            var chromeVersionShort = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9]+)");
            var chromeVersionLong = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9\\.]+)");
            var sec_ch_ua = $"\".Not/A)Brand\";v=\"24\", \"Google Chrome\";v=\"{chromeVersionShort}\", \"Chromium\";v=\"{chromeVersionShort}\"";
            var dynamicUserAgent = ProgramState.ChromeUA.Replace(chromeVersionLong, chromeVersionShort + ".0.0.0");

            client.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent", dynamicUserAgent);
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua", sec_ch_ua);
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua-mobile", "?0");
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua-platform", "\"Windows\"");
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua-platform-version", "\"10.0.0\""); // Assuming Windows 10, adjust if needed
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua-model", "\"\"");
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-ch-ua-full-version", $"\"{chromeVersionLong}\"");

            client.DefaultRequestHeaders.TryAddWithoutValidation("upgrade-insecure-requests", "1");
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-fetch-site", "none"); // Adjust based on context if needed
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-fetch-mode", "navigate"); // Adjust based on context if needed
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-fetch-user", "?1"); // Adjust based on context if needed
            client.DefaultRequestHeaders.TryAddWithoutValidation("sec-fetch-dest", "document"); // Adjust based on context if needed

            // Use provided language or default from NetTools
            client.DefaultRequestHeaders.AcceptLanguage.ParseAdd(acceptLanguage ?? "en-us,en;q=0.5");
            // Use Accept header from NetTools
            client.DefaultRequestHeaders.TryAddWithoutValidation("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.98");
            // Accept-Encoding and Accept-Charset are often handled automatically by HttpClientHandler/HttpClient
            // client.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Encoding", "gzip,deflate"); // Usually handled by AutomaticDecompression
            // client.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Charset", "utf-8;q=0.7,*;q=0.7"); // Less common now

            // Add custom headers provided by the caller *after* setting defaults
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    // Use TryAddWithoutValidation for headers like Accept that might have complex values
                    if (!client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value))
                    {
                        // Consider logging a warning if a header can't be added
                    }
                }
            }

            if (referrer != null)
            {
                client.DefaultRequestHeaders.Referrer = referrer;
            }

            if (!string.IsNullOrEmpty(origin))
            {
                if (!client.DefaultRequestHeaders.TryAddWithoutValidation("Origin", origin))
                {
                    // Consider logging
                }
            }
        }

        /// <summary>
        /// Performs an asynchronous GET request.
        /// </summary>
        public static async Task<HttpResult> GetAsync(string url, CookieContainer cookieContainer, Dictionary<string, string>? headers = null, Uri? referrer = null, string? acceptLanguage = null)
        {
            using var client = CreateClient(cookieContainer);
            SetCommonHeaders(client, headers, referrer, null, acceptLanguage); // Pass acceptLanguage

            try
            {
                using var response = await client.GetAsync(url);
                var content = string.Empty;
                try
                {
                    content = await response.Content.ReadAsStringAsync();
                }
                catch { /* Ignore content reading errors, return empty */ }

                return new HttpResult
                {
                    IsSuccess = response.IsSuccessStatusCode,
                    Content = content,
                    StatusCode = response.StatusCode
                };
            }
            catch (HttpRequestException ex)
            {
                // Log the exception if needed
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
            catch (Exception ex) // Catch other potential errors
            {
                // Log the exception if needed
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
        }

        /// <summary>
        /// Performs an asynchronous POST request with a JSON payload.
        /// </summary>
        public static async Task<HttpResult> PostJsonAsync(string url, CookieContainer cookieContainer, object payload, Dictionary<string, string>? headers = null, Uri? referrer = null, string? origin = null, string? acceptLanguage = null)
        {
            using var client = CreateClient(cookieContainer);
            SetCommonHeaders(client, headers, referrer, origin, acceptLanguage); // Pass acceptLanguage

            var jsonPayload = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            using var httpContent = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            try
            {
                using var response = await client.PostAsync(url, httpContent);
                var content = string.Empty;
                try
                {
                    content = await response.Content.ReadAsStringAsync();
                }
                catch { /* Ignore content reading errors, return empty */ }

                return new HttpResult
                {
                    IsSuccess = response.IsSuccessStatusCode,
                    Content = content,
                    StatusCode = response.StatusCode
                };
            }
            catch (HttpRequestException ex)
            {
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
            catch (Exception ex) // Catch other potential errors
            {
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
        }

        /// <summary>
        /// Performs an asynchronous POST request with Form URL Encoded data.
        /// </summary>
        public static async Task<HttpResult> PostFormUrlEncodedAsync(string url, CookieContainer cookieContainer, Dictionary<string, string> formData, Dictionary<string, string>? headers = null, Uri? referrer = null, string? origin = null, bool allowAutoRedirect = false, string? acceptLanguage = null)
        {
            using var client = CreateClient(cookieContainer, allowAutoRedirect);
            SetCommonHeaders(client, headers, referrer, origin, acceptLanguage); // Pass acceptLanguage

            using var httpContent = new FormUrlEncodedContent(formData);

            try
            {
                using var response = await client.PostAsync(url, httpContent);
                var content = string.Empty;
                try
                {
                    content = await response.Content.ReadAsStringAsync();
                }
                catch { /* Ignore content reading errors, return empty */ }

                return new HttpResult
                {
                    IsSuccess = response.IsSuccessStatusCode,
                    Content = content,
                    StatusCode = response.StatusCode
                };
            }
            catch (HttpRequestException ex)
            {
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
            catch (Exception ex) // Catch other potential errors
            {
                return new HttpResult { IsSuccess = false, Content = ex.Message, StatusCode = null };
            }
        }
    }
}
