﻿using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using eBay.Service.Call;
using eBay.Service.Core.Soap;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Purchasing;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormBid
    {
        private void SetOfferMessage()
        {
            if (UserSettings.MakeOfferSelectedMessage != null)
                boxMakeOfferMessages.EditValue = UserSettings.MakeOfferSelectedMessage;
            else
                boxMakeOfferMessages.EditValue = UserSettings.MakeOfferMessages.FirstOrDefault().Key;
        }

        private void MakeOfferMessages_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            // boxMakeOfferMessages.Properties.
            if (e.Button.Tag?.ToString() == "Add")
            {
                var args = new XtraInputBoxArgs();
                var viewNameEdit = new TextEdit();
                args.Editor = viewNameEdit;
                args.Caption = "Create new make offer message.";
                args.Prompt = "Message alias";
                args.DefaultButtonIndex = 0;
                args.DefaultResponse = "Message " + (UserSettings.MakeOfferMessages.Count + 1);

                var dialogResult = XtraInputBox.Show(args);
                if (dialogResult != null)
                {
                    var messageAlias = dialogResult.ToString();
                    if (UserSettings.MakeOfferMessages.ContainsKey(messageAlias))
                    {
                        XtraMessageBox.Show(this, $"Message alias '{messageAlias}' already exists.");

                        return;
                    }

                    UserSettings.MakeOfferMessages.Add(messageAlias, "");

                    boxMakeOfferMessages.EditValue = messageAlias;
                }

                Form1.Instance.SaveSettings();
            }

            if (e.Button.Tag?.ToString() == "Remove")
            {
                var dialogResult = XtraMessageBox.Show(this, $"Remove '{boxMakeOfferMessages.Text}'?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2);
                if (dialogResult == DialogResult.Yes)
                {
                    var removeItem = boxMakeOfferMessages.Text;
                    var otherMessage = UserSettings.MakeOfferMessages.FirstOrDefault(m => m.Key != removeItem);

                    if (otherMessage.Key != null)
                        boxMakeOfferMessages.EditValue = otherMessage.Key;

                    UserSettings.MakeOfferMessages.Remove(removeItem);
                }

                Form1.Instance.SaveSettings();
            }
        }

        private void BoxMakeOfferMessages_EditValueChanged(object sender, EventArgs e)
        {
            if (UserSettings.MakeOfferMessages.TryGetValue(boxMakeOfferMessages.Text, out var message))
                memoEditBestOfferMessage.Text = message;
        }

        private void MemoEditBestOfferMessage_EditValueChanged(object sender, EventArgs e)
        {
            if (!UserSettings.MakeOfferMessages.ContainsKey(boxMakeOfferMessages.Text))
                return;

            UserSettings.MakeOfferMessages[boxMakeOfferMessages.Text] = memoEditBestOfferMessage.Text;
            UserSettings.MakeOfferSelectedMessage = boxMakeOfferMessages.Text;
        }

        private void MemoEditBestOfferMessage_Leave(object sender, EventArgs e)
        {
            UserSettings.MakeOfferMessages[boxMakeOfferMessages.Text] = memoEditBestOfferMessage.Text;
            Form1.Instance.SaveSettings();
        }

        private void MemoEditBestOfferMessage_MouseLeave(object sender, EventArgs e)
        {
            UserSettings.MakeOfferMessages[boxMakeOfferMessages.Text] = memoEditBestOfferMessage.Text;
            Form1.Instance.SaveSettings();
        }

        private void ChkSubtractShipping_CheckedChanged(object sender, EventArgs e)
        {
            Placeoffer.BestOfferSubtractShipping = ((CheckEdit)sender).Checked;
            UpdateBestOfferPricePerItemValue();
        }

        #region BestOffer

        private async void simpleButtonBestOffer_Click(object sender, EventArgs e)
        {
            simpleButtonBestOffer.Enabled = false;

            var offerQuantity = (int)spinEditBestOfferItemQuantity.Value;
            var offerTotalPrice = (double)spinEditBestOfferPricePerItem.Value * offerQuantity;
            if (Placeoffer.BestOfferSubtractShipping)
            {
                var adjustAmountPerItem = (offerTotalPrice - D.ItemShipping.GetQuantityShipPrice(offerQuantity, D.ShippingType).Value) / offerQuantity;
                offerTotalPrice = adjustAmountPerItem;
            }
            if (CreditCardService.CreditCardPaymentEnabled)
            {
                this.Close();
                await BestOfferCheckout.ExecuteBestOfferSubmissionAsync(D, offerQuantity, (double)spinEditBestOfferPricePerItem.Value, D.ItemPricing.ItemPrice.Currency, memoEditBestOfferMessage.Text);
            }
            else
            {
                var affiliateAction = D.Term == "Watchlist" ? "Watch" : "BestOffer";
                MakeOfferUsingAPI(offerQuantity, (double)spinEditBestOfferPricePerItem.Value, affiliateAction);
            }


            simpleButtonBestOffer.Enabled = true;
        }

        private void spinEditBestOfferItemQuantity_EditValueChanged(object sender, EventArgs e)
        {
            spinEditBuyNowQuantity.EditValue = spinEditBestOfferItemQuantity.EditValue;
        }

        private void spinEditBestOfferPricePerItem_EditValueChanged(object sender, EventArgs e)
        {
            UpdateBestOfferPricePerItemValue();
        }

        private void UpdateBestOfferPricePerItemValue()
        {
            spinEditOfferPercentage.EditValueChanged -= spinEditOfferPercentage_EditValueChanged;
            if (spinEditBestOfferPricePerItem.Value == 0)
            {
                spinEditBestOfferPricePerItem.EditValueChanged -= spinEditBestOfferPricePerItem_EditValueChanged;
                var priceStr = Regex.Replace(spinEditBestOfferPricePerItem.Text, ".*\\s", "");
                if (decimal.TryParse(priceStr, out var price))
                    spinEditBestOfferPricePerItem.Value = price;

                spinEditBestOfferPricePerItem.EditValueChanged += spinEditBestOfferPricePerItem_EditValueChanged;
            }

            if (D.ItemPricing.ItemPrice.Value < 1)
                return;

            spinEditOfferPercentage.Value = Math.Round(spinEditBestOfferPricePerItem.Value / (decimal)D.ItemPricing.ItemPrice.Value * 100, 2);
            spinEditOfferPercentage.EditValueChanged += spinEditOfferPercentage_EditValueChanged;

            UpdateOfferTotalPrice();
        }

        private void UpdateOfferTotalPrice()
        {
            var totalOfferPrice = new CurrencyAmount((double)spinEditBestOfferPricePerItem.Value * (double)spinEditBestOfferItemQuantity.Value, D.ItemPricing.ItemPrice.Currency);

            if (Placeoffer.BestOfferSubtractShipping)
            {
                var quantityShipPrice = D.ItemShipping.GetQuantityShipPrice((int)spinEditBestOfferItemQuantity.Value, D.ShippingType);
                if (totalOfferPrice.Value - quantityShipPrice.Value > 0)
                    totalOfferPrice.Value -= quantityShipPrice.Value;
            }

            lcOfferTotalPrice.Text = totalOfferPrice.FormatPrice();
        }

        private void spinEditOfferPercentage_EditValueChanged(object sender, EventArgs e)
        {
            spinEditBestOfferPricePerItem.EditValueChanged -= spinEditBestOfferPricePerItem_EditValueChanged;
            spinEditBestOfferPricePerItem.Value = Math.Round((decimal)D.ItemPricing.ItemPrice.Value * spinEditOfferPercentage.Value / 100, 2);
            var totalOfferPrice = new CurrencyAmount((double)spinEditBestOfferPricePerItem.Value * (double)spinEditBestOfferItemQuantity.Value, D.ItemPricing.ItemPrice.Currency);

            lcOfferTotalPrice.Text = totalOfferPrice.FormatPrice();
            spinEditBestOfferPricePerItem.EditValueChanged += spinEditBestOfferPricePerItem_EditValueChanged;
        }

        private void spinEditBestOfferPricePerItem_EditValueChanging(object sender, ChangingEventArgs e)
        {
            spinEditBestOfferPricePerItem.IsModified = true;
        }

        #endregion
    }
}
