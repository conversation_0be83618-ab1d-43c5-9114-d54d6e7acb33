using System;
using System.Threading.Tasks;
using uBuyFirst.RestockReporting.Models;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// Service interface for logging item processing history
    /// </summary>
    public interface IItemHistoryLogger : IDisposable
    {
        /// <summary>
        /// Logs the complete context of an item processing operation
        /// This method should be async and not block the calling thread
        /// </summary>
        /// <param name="context">Complete context of the item processing operation</param>
        /// <returns>Task that completes when logging is finished (or fails silently)</returns>
        Task LogItemProcessingAsync(ItemProcessingContext context);
    }
}
