using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.RestockReporting.Services;

using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.RestockReporting.Integration
{
    [TestClass]
    public class RestockFilterActionIntegrationTests
    {
        private RestockFilterAction _action;
        private string _testBasePath;
        [TestInitialize]
        public void Setup()
        {
            // Create temporary test directories
            _testBasePath = Path.Combine(Path.GetTempPath(), "RestockFilterActionIntegrationTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testBasePath);

            // Create the action (no longer needs repository dependency)
            _action = new RestockFilterAction();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _action?.Dispose();

            // Clean up test directories
            if (Directory.Exists(_testBasePath))
            {
                Directory.Delete(_testBasePath, true);
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithMatchingItems_LogsItemProcessingHistory()
        {
            // Arrange
            var context = CreateTestFilterActionContext();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed");

            // Verify that JSON files were created
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                Assert.IsTrue(jsonFiles.Length > 0, "Should create JSON history files");
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithNonMatchingItems_LogsFilteredOutItems()
        {
            // Arrange
            var context = CreateTestFilterActionContextWithNonMatchingItems();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed even with no matches");

            // Verify that JSON files were created for filtered out items
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                Assert.IsTrue(jsonFiles.Length > 0, "Should create JSON history files for filtered out items");
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithPurchaseDisabled_LogsSkippedPurchases()
        {
            // Arrange
            _action.EnablePurchasing = false;
            var context = CreateTestFilterActionContext();

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, "Filter action should succeed");

            // Verify that items were logged as skipped
            var todayFolder = Path.Combine(_testBasePath, DateTime.Now.ToString("yyyy-MM-dd"));
            if (Directory.Exists(todayFolder))
            {
                var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                if (jsonFiles.Length > 0)
                {
                    var jsonContent = File.ReadAllText(jsonFiles[0]);
                    Assert.IsTrue(jsonContent.Contains("\"outcome\": \"skipped\"") ||
                                jsonContent.Contains("Purchase execution is disabled"),
                                "Should log purchase as skipped when purchasing is disabled");
                }
            }
        }

        [TestMethod]
        public async Task ExecuteAsync_WithCurrentRowSpecified_ProcessesOnlySingleRow()
        {
            // Arrange
            _action.EnablePurchasing = false;
            var dataTable = CreateTestDataTable();

            // Add multiple rows to the table
            var row1 = CreateTestDataRow(dataTable, "123456789", "Test Item 1");
            var row2 = CreateTestDataRow(dataTable, "987654321", "Test Item 2");
            dataTable.Rows.Add(row1);
            dataTable.Rows.Add(row2);

            // Create context with CurrentRow specified (single-item processing)
            var context = new FilterActionContext
            {
                FilterRule = CreateTestFilter(),
                SourceDataTable = dataTable,
                CurrentRow = row1  // Only process this specific row
            };

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, $"Action should succeed. Error: {result.Message}");
            // The result should indicate only 1 item was processed, not 2
            Assert.IsTrue(result.Message.Contains("processed 1 matching items") || result.Message.Contains("processed 0 matching items"),
                "Should process only the specified row, not all rows in the table");
        }

        [TestMethod]
        public async Task ExecuteAsync_WithoutCurrentRow_ProcessesAllRows()
        {
            // Arrange
            _action.EnablePurchasing = false;
            var dataTable = CreateTestDataTable();

            // Add multiple rows to the table
            var row1 = CreateTestDataRow(dataTable, "123456789", "Test Item 1");
            var row2 = CreateTestDataRow(dataTable, "987654321", "Test Item 2");
            dataTable.Rows.Add(row1);
            dataTable.Rows.Add(row2);

            // Create context without CurrentRow specified (batch processing)
            var context = new FilterActionContext
            {
                FilterRule = CreateTestFilter(),
                SourceDataTable = dataTable,
                CurrentRow = null  // Process all rows
            };

            // Act
            var result = await _action.ExecuteAsync(context);

            // Assert
            Assert.IsTrue(result.Success, $"Action should succeed. Error: {result.Message}");
            // The result should indicate processing of multiple items
            Assert.IsTrue(result.Message.Contains("processed"), "Should process all rows in batch mode");
        }

        private IFilterActionContext CreateTestFilterActionContext()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Blob", typeof(DataList));
            dataTable.Columns.Add("ItemID", typeof(string));
            dataTable.Columns.Add("Title", typeof(string));
            dataTable.Columns.Add("Price", typeof(decimal));

            // Add test data
            var testDataList = CreateTestDataList();
            var row = dataTable.NewRow();
            row["Blob"] = testDataList;
            row["ItemID"] = testDataList.ItemID;
            row["Title"] = testDataList.Title;
            row["Price"] = testDataList.ItemPrice;
            dataTable.Rows.Add(row);

            var filter = new XFilterClass
            {
                Alias = "Test Restock Filter",
                Expression = "Price <= 100", // This should match our test data
                Enabled = true
            };

            return new FilterActionContext
            {
                FilterRule = filter,
                SourceDataTable = dataTable
            };
        }

        private IFilterActionContext CreateTestFilterActionContextWithNonMatchingItems()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Blob", typeof(DataList));
            dataTable.Columns.Add("ItemID", typeof(string));
            dataTable.Columns.Add("Title", typeof(string));
            dataTable.Columns.Add("Price", typeof(decimal));

            // Add test data
            var testDataList = CreateTestDataList();
            var row = dataTable.NewRow();
            row["Blob"] = testDataList;
            row["ItemID"] = testDataList.ItemID;
            row["Title"] = testDataList.Title;
            row["Price"] = testDataList.ItemPrice;
            dataTable.Rows.Add(row);

            var filter = new XFilterClass
            {
                Alias = "Test Restock Filter",
                Expression = "Price <= 10", // This should NOT match our test data (price is 25.99)
                Enabled = true
            };

            return new FilterActionContext
            {
                FilterRule = filter,
                SourceDataTable = dataTable
            };
        }

        private DataList CreateTestDataList()
        {
            return new DataList
            {
                ItemID = "123456789",
                Title = "Test iPhone Case",
                ItemPrice = 25.99,
                Condition = "New",
                SellerName = "test-seller",
                Shipping = 5.99,
                Location = "United States",
                QuantityAvailable = 5,
                Term = "Test Keyword Alias", // This should match a keyword
                // ViewItemURL is not a property, use GetAffiliateLink() method instead
                GalleryUrl = "https://i.ebayimg.com/test.jpg"
            };
        }

        private DataTable CreateTestDataTable()
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("Blob", typeof(DataList));
            dataTable.Columns.Add("ItemID", typeof(string));
            dataTable.Columns.Add("Title", typeof(string));
            dataTable.Columns.Add("Price", typeof(decimal));
            return dataTable;
        }

        private DataRow CreateTestDataRow(DataTable dataTable, string itemId, string title)
        {
            var testDataList = new DataList
            {
                ItemID = itemId,
                Title = title,
                ItemPrice = 25.99,
                Condition = "New",
                SellerName = "test-seller",
                Shipping = 5.99,
                Location = "United States",
                QuantityAvailable = 5,
                Term = "Test Keyword Alias",
                GalleryUrl = "https://i.ebayimg.com/test.jpg"
            };

            var row = dataTable.NewRow();
            row["Blob"] = testDataList;
            row["ItemID"] = testDataList.ItemID;
            row["Title"] = testDataList.Title;
            row["Price"] = testDataList.ItemPrice;
            return row;
        }

        private XFilterClass CreateTestFilter()
        {
            return new XFilterClass
            {
                Alias = "Test Restock Filter",
                Expression = "Price <= 100", // This should match our test data
                Enabled = true
            };
        }
    }

    /// <summary>
    /// Mock implementation of FilterActionContext for testing
    /// </summary>
    public class FilterActionContext : IFilterActionContext
    {
        public XFilterClass FilterRule { get; set; }
        public DevExpress.XtraGrid.Views.Grid.GridView GridView { get; set; }
        public DataTable SourceDataTable { get; set; }
        public DataRow CurrentRow { get; set; }
    }
}
