using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst;
using uBuyFirst.BulkEdit;
using uBuyFirst.Search;

namespace uBuyFirst.Tests.BulkEdit
{
    [TestClass]
    public class BulkPropertyEditorTests
    {
        private BulkPropertyEditor _bulkEditor;
        private KeywordFolder _testFolder;
        private List<Keyword2Find> _testKeywords;

        [TestInitialize]
        public void Setup()
        {
            _bulkEditor = new BulkPropertyEditor();
            _testFolder = CreateTestFolder();
            _testKeywords = CreateTestKeywords();
        }

        private KeywordFolder CreateTestFolder()
        {
            var rootFolder = new KeywordFolder
            {
                Name = "Root",
                Id = "root-folder"
            };

            var subFolder = new KeywordFolder
            {
                Name = "SubFolder",
                Id = "sub-folder",
                ParentFolder = rootFolder
            };

            rootFolder.Children.Add(subFolder);

            // Add keywords to folders
            var keyword1 = new Keyword2Find
            {
                Id = "kw1",
                Alias = "Keyword1",
                Kws = "test keywords 1",
                PriceMin = 10,
                PriceMax = 100,
                Threads = 1,
                SearchInDescription = false
            };

            var keyword2 = new Keyword2Find
            {
                Id = "kw2",
                Alias = "Keyword2",
                Kws = "test keywords 2",
                PriceMin = 20,
                PriceMax = 200,
                Threads = 2,
                SearchInDescription = true
            };

            var keyword3 = new Keyword2Find
            {
                Id = "kw3",
                Alias = "Keyword3",
                Kws = "test keywords 3",
                PriceMin = 30,
                PriceMax = 300,
                Threads = 1,
                SearchInDescription = false
            };

            rootFolder.Keywords.Add(keyword1);
            rootFolder.Keywords.Add(keyword2);
            subFolder.Keywords.Add(keyword3);

            return rootFolder;
        }

        private List<Keyword2Find> CreateTestKeywords()
        {
            return new List<Keyword2Find>
            {
                new Keyword2Find
                {
                    Id = "test1",
                    Alias = "Test1",
                    Kws = "test",
                    PriceMin = 1,
                    PriceMax = 100,
                    Threads = 1,
                    SearchInDescription = false
                },
                new Keyword2Find
                {
                    Id = "test2",
                    Alias = "Test2",
                    Kws = "test2",
                    PriceMin = 2,
                    PriceMax = 200,
                    Threads = 2,
                    SearchInDescription = true
                }
            };
        }

        [TestMethod]
        public void GetAllKeywordsRecursive_ShouldReturnAllKeywords()
        {
            // Act
            var result = _bulkEditor.GetAllKeywordsRecursive(_testFolder);

            // Assert
            Assert.AreEqual(3, result.Count, "Should return all keywords from folder and subfolders");
            Assert.IsTrue(result.Any(k => k.Alias == "Keyword1"), "Should include keyword from root folder");
            Assert.IsTrue(result.Any(k => k.Alias == "Keyword2"), "Should include second keyword from root folder");
            Assert.IsTrue(result.Any(k => k.Alias == "Keyword3"), "Should include keyword from subfolder");
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void GetAllKeywordsRecursive_WithNullFolder_ShouldThrowException()
        {
            // Act
            _bulkEditor.GetAllKeywordsRecursive(null);
        }

        [TestMethod]
        public void IsPropertyBulkEditable_WithProtectedProperty_ShouldReturnFalse()
        {
            // Act & Assert
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("Alias"), "Alias should be protected");
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("Kws"), "Keywords should be protected");
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("KeywordEnabled"), "Enabled should be protected");
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("PurchasedQuantity"), "PurchasedQuantity should be protected");
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("JobId"), "JobId should be protected");
            Assert.IsFalse(_bulkEditor.IsPropertyBulkEditable("RequiredQuantity"), "RequiredQuantity should be protected");
        }

        [TestMethod]
        public void IsPropertyBulkEditable_WithEditableProperty_ShouldReturnTrue()
        {
            // Act & Assert
            Assert.IsTrue(_bulkEditor.IsPropertyBulkEditable("PriceMin"), "PriceMin should be editable");
            Assert.IsTrue(_bulkEditor.IsPropertyBulkEditable("PriceMax"), "PriceMax should be editable");
            Assert.IsTrue(_bulkEditor.IsPropertyBulkEditable("Threads"), "Threads should be editable");
            Assert.IsTrue(_bulkEditor.IsPropertyBulkEditable("SearchInDescription"), "SearchInDescription should be editable");
        }

        [TestMethod]
        public void ValidatePropertyValues_WithValidValues_ShouldReturnNoErrors()
        {
            // Arrange
            var propertyChanges = new Dictionary<string, object>
            {
                ["PriceMin"] = 10.0,
                ["PriceMax"] = 100.0,
                ["Threads"] = 3,
                ["SearchInDescription"] = true
            };

            // Act
            var errors = _bulkEditor.ValidatePropertyValues(propertyChanges);

            // Assert
            Assert.AreEqual(0, errors.Count, "Should have no validation errors");
        }

        [TestMethod]
        public void ValidatePropertyValues_WithInvalidValues_ShouldReturnErrors()
        {
            // Arrange
            var propertyChanges = new Dictionary<string, object>
            {
                ["PriceMin"] = -10.0, // Invalid: negative
                ["PriceMax"] = -100.0, // Invalid: negative
                ["Threads"] = 15, // Invalid: too high
                ["SearchInDescription"] = "not a boolean" // Invalid: wrong type
            };

            // Act
            var errors = _bulkEditor.ValidatePropertyValues(propertyChanges);

            // Assert
            Assert.IsTrue(errors.Count > 0, "Should have validation errors");
            Assert.IsTrue(errors.Any(e => e.Contains("Price Min")), "Should have PriceMin error");
            Assert.IsTrue(errors.Any(e => e.Contains("Price Max")), "Should have PriceMax error");
            Assert.IsTrue(errors.Any(e => e.Contains("Threads")), "Should have Threads error");
            Assert.IsTrue(errors.Any(e => e.Contains("Search In Description")), "Should have SearchInDescription error");
        }

        [TestMethod]
        public void ValidatePropertyValues_WithPriceMinGreaterThanMax_ShouldReturnError()
        {
            // Arrange
            var propertyChanges = new Dictionary<string, object>
            {
                ["PriceMin"] = 100.0,
                ["PriceMax"] = 50.0 // Min > Max
            };

            // Act
            var errors = _bulkEditor.ValidatePropertyValues(propertyChanges);

            // Assert
            Assert.IsTrue(errors.Any(e => e.Contains("Price Min cannot be greater than Price Max")), 
                "Should validate price range");
        }

        [TestMethod]
        public void ApplyBulkChanges_WithValidChanges_ShouldSucceed()
        {
            // Arrange
            var propertyChanges = new Dictionary<string, object>
            {
                ["PriceMin"] = 50.0,
                ["Threads"] = 3,
                ["SearchInDescription"] = true
            };

            // Act
            var result = _bulkEditor.ApplyBulkChanges(_testKeywords, propertyChanges);

            // Assert
            Assert.IsTrue(result.Success, "Bulk operation should succeed");
            Assert.AreEqual(2, result.ProcessedCount, "Should process all keywords");
            Assert.AreEqual(2, result.TotalCount, "Should have correct total count");

            // Verify changes were applied
            Assert.AreEqual(50.0, _testKeywords[0].PriceMin, "PriceMin should be updated");
            Assert.AreEqual(3, _testKeywords[0].Threads, "Threads should be updated");
            Assert.IsTrue(_testKeywords[0].SearchInDescription, "SearchInDescription should be updated");
        }

        [TestMethod]
        public void ApplyBulkChanges_WithProtectedProperty_ShouldFail()
        {
            // Arrange
            var propertyChanges = new Dictionary<string, object>
            {
                ["Alias"] = "NewAlias", // Protected property
                ["PriceMin"] = 50.0
            };

            // Act
            var result = _bulkEditor.ApplyBulkChanges(_testKeywords, propertyChanges);

            // Assert
            Assert.IsFalse(result.Success, "Should fail with protected property");
            Assert.IsTrue(result.Errors.Any(e => e.Contains("protected")), "Should have protected property error");
        }

        [TestMethod]
        public void CreateBackup_ShouldCaptureCurrentState()
        {
            // Act
            var backup = _bulkEditor.CreateBackup(_testKeywords);

            // Assert
            Assert.AreEqual(2, backup.Count, "Should backup all keywords");
            Assert.IsTrue(backup.ContainsKey("test1"), "Should contain first keyword backup");
            Assert.IsTrue(backup.ContainsKey("test2"), "Should contain second keyword backup");

            var keyword1Backup = backup["test1"];
            Assert.AreEqual(1.0, keyword1Backup["PriceMin"], "Should backup PriceMin");
            Assert.AreEqual(1, keyword1Backup["Threads"], "Should backup Threads");
        }

        [TestMethod]
        public void RestoreFromBackup_ShouldRestoreOriginalValues()
        {
            // Arrange
            var originalPriceMin = _testKeywords[0].PriceMin;
            var originalThreads = _testKeywords[0].Threads;
            
            var backup = _bulkEditor.CreateBackup(_testKeywords);
            
            // Modify values
            _testKeywords[0].PriceMin = 999;
            _testKeywords[0].Threads = 10;

            // Act
            _bulkEditor.RestoreFromBackup(_testKeywords, backup);

            // Assert
            Assert.AreEqual(originalPriceMin, _testKeywords[0].PriceMin, "PriceMin should be restored");
            Assert.AreEqual(originalThreads, _testKeywords[0].Threads, "Threads should be restored");
        }
    }
}
