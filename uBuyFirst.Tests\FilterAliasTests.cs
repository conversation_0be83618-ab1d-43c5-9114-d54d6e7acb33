using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using System.Threading.Tasks;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class FilterAliasTests
    {
        [TestMethod]
        public void DataList_TriggeringFilterAlias_DefaultsToEmpty()
        {
            // Arrange & Act
            var dataList = new DataList();

            // Assert
            Assert.AreEqual("", dataList.TriggeringFilterAlias);
        }

        [TestMethod]
        public void DataList_TriggeringFilterAlias_CanBeSetAndRetrieved()
        {
            // Arrange
            var dataList = new DataList();
            var expectedAlias = "TestFilterAlias";

            // Act
            dataList.TriggeringFilterAlias = expectedAlias;

            // Assert
            Assert.AreEqual(expectedAlias, dataList.TriggeringFilterAlias);
        }

        [TestMethod]
        public void DataList_TriggeringFilterAlias_HandlesNullValue()
        {
            // Arrange
            var dataList = new DataList();

            // Act
            dataList.TriggeringFilterAlias = null;

            // Assert
            Assert.IsNull(dataList.TriggeringFilterAlias);
        }

        [TestMethod]
        public void DataList_TriggeringFilterAlias_HandlesEmptyString()
        {
            // Arrange
            var dataList = new DataList();

            // Act
            dataList.TriggeringFilterAlias = "";

            // Assert
            Assert.AreEqual("", dataList.TriggeringFilterAlias);
        }

        [TestMethod]
        public void DataList_TriggeringFilterAlias_PreservesWhitespace()
        {
            // Arrange
            var dataList = new DataList();
            var aliasWithSpaces = "  Filter With Spaces  ";

            // Act
            dataList.TriggeringFilterAlias = aliasWithSpaces;

            // Assert
            Assert.AreEqual(aliasWithSpaces, dataList.TriggeringFilterAlias);
        }
    }
}
