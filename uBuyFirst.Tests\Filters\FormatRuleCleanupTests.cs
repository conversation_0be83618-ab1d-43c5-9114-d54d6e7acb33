using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using uBuyFirst.Filters;

namespace uBuyFirst.Tests.Filters
{
    [TestClass]
    [TestCategory("FormatRuleCleanup")]
    public class FormatRuleCleanupTests
    {
        private Mock<GridView> _mockGridView;
        private Mock<GridControl> _mockGridControl;
        private Mock<GridColumn> _mockGridColumn;
        private Mock<GridColumnCollection> _mockColumnCollection;
        private Mock<GridFormatRuleCollection> _mockFormatRuleCollection;
        private DataTable _testDataTable;
        private XFilterClass _testFilter;

        [TestInitialize]
        public void Setup()
        {
            // Create mock GridView and related objects
            _mockGridView = new Mock<GridView>();
            _mockGridControl = new Mock<GridControl>();
            _mockGridColumn = new Mock<GridColumn>();
            _mockColumnCollection = new Mock<GridColumnCollection>(_mockGridView.Object);
            _mockFormatRuleCollection = new Mock<GridFormatRuleCollection>(_mockGridView.Object);

            // Setup GridView mock
            _mockGridView.Setup(gv => gv.GridControl).Returns(_mockGridControl.Object);
            _mockGridView.Setup(gv => gv.Columns).Returns(_mockColumnCollection.Object);
            _mockGridView.Setup(gv => gv.Columns[It.IsAny<string>()]).Returns(_mockGridColumn.Object);
            _mockGridView.Setup(gv => gv.FormatRules).Returns(_mockFormatRuleCollection.Object);

            // Create test DataTable
            _testDataTable = new DataTable();
            _testDataTable.Columns.Add("Title", typeof(string));
            _testDataTable.Columns.Add("Price", typeof(decimal));
            var row = _testDataTable.NewRow();
            row["Title"] = "Test Item";
            row["Price"] = 10.99m;
            _testDataTable.Rows.Add(row);

            // Create test filter
            _testFilter = new XFilterClass
            {
                Id = Guid.NewGuid(),
                Alias = "Test Filter",
                Enabled = true,
                FormatColumn = "Title",
                Action = "Format rows"
            };
        }

        [TestMethod]
        public void AddOrReplaceFilter_WhenChangingFromFormatToNonFormat_RemovesOldFormatRule()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);

            // Add an existing format rule with the filter's ID as Tag
            var existingRule = new GridFormatRule();
            existingRule.Tag = _testFilter.Id;
            formatRules.Add(existingRule);

            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Change filter action to non-formatting
            _testFilter.Action = "Remove rows";

            // Act
            XFilterManager.AddOrReplaceFilter(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(0, formatRules.Count, "Format rule should have been removed when changing to non-formatting action");
        }

        [TestMethod]
        public void AddOrReplaceFilter_WhenChangingFromNonFormatToFormat_AddsFormatRule()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);
            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Set filter action to formatting
            _testFilter.Action = "Format rows";

            // Act
            XFilterManager.AddOrReplaceFilter(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(1, formatRules.Count, "Format rule should have been added for formatting action");
            Assert.AreEqual(_testFilter.Id, formatRules[0].Tag, "Format rule should have filter ID as Tag");
        }

        [TestMethod]
        public void AddOrReplaceFilter_WhenReplacingFormatRule_MaintainsSamePosition()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);

            // Add some existing rules
            var rule1 = new GridFormatRule { Tag = Guid.NewGuid() };
            var rule2 = new GridFormatRule { Tag = _testFilter.Id }; // Our filter's rule
            var rule3 = new GridFormatRule { Tag = Guid.NewGuid() };

            formatRules.Add(rule1);
            formatRules.Add(rule2);
            formatRules.Add(rule3);

            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Set filter action to formatting (updating existing rule)
            _testFilter.Action = "Format cells";

            // Act
            XFilterManager.AddOrReplaceFilter(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(3, formatRules.Count, "Should maintain same number of rules");
            Assert.AreEqual(_testFilter.Id, formatRules[1].Tag, "Updated rule should be at same position (index 1)");
        }

        [TestMethod]
        public void AddOrReplaceFilter_WithNonFormattingAction_DoesNotAddFormatRule()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);
            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Set filter action to non-formatting
            _testFilter.Action = "Remove rows";

            // Act
            XFilterManager.AddOrReplaceFilter(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(0, formatRules.Count, "No format rule should be added for non-formatting action");
        }

        [TestMethod]
        public async Task ApplyFilterAsync_WhenChangingFromFormatToNonFormat_RemovesOldFormatRule()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);

            // Add an existing format rule with the filter's ID as Tag
            var existingRule = new GridFormatRule();
            existingRule.Tag = _testFilter.Id;
            formatRules.Add(existingRule);

            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Set filter to use new action system with non-formatting action
            _testFilter.ActionHandler = new RemoveRowsAction();

            // Act
            await XFilterManager.ApplyFilterAsync(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(0, formatRules.Count, "Format rule should have been removed when changing to non-formatting action in new action system");
        }

        [TestMethod]
        public async Task ApplyFilterAsync_WithFormattingAction_DoesNotRemoveFormatRule()
        {
            // Arrange
            var formatRules = new GridFormatRuleCollection(_mockGridView.Object);

            // Add an existing format rule with the filter's ID as Tag
            var existingRule = new GridFormatRule();
            existingRule.Tag = _testFilter.Id;
            formatRules.Add(existingRule);

            _mockGridView.Setup(gv => gv.FormatRules).Returns(formatRules);

            // Set filter to use new action system with formatting action
            _testFilter.ActionHandler = new FormatRowsAction();

            // Act
            await XFilterManager.ApplyFilterAsync(_mockGridView.Object, _testFilter, _testDataTable);

            // Assert
            Assert.AreEqual(1, formatRules.Count, "Format rule should not be removed when action is still formatting");
            Assert.AreEqual(_testFilter.Id, formatRules[0].Tag, "Format rule should still have filter ID as Tag");
        }
    }
}
