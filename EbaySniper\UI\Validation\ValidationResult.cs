using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Represents the result of a validation operation
    /// </summary>
    public class ValidationResult
    {
        private readonly List<ValidationWarning> _warnings;

        /// <summary>
        /// Initializes a new instance of ValidationResult
        /// </summary>
        public ValidationResult()
        {
            _warnings = new List<ValidationWarning>();
        }

        /// <summary>
        /// Gets all validation warnings
        /// </summary>
        public IReadOnlyList<ValidationWarning> Warnings => _warnings.AsReadOnly();

        /// <summary>
        /// Gets a value indicating whether the validation passed (no warnings)
        /// </summary>
        public bool IsValid => _warnings.Count == 0;

        /// <summary>
        /// Gets a value indicating whether there are any warnings
        /// </summary>
        public bool HasWarnings => _warnings.Count > 0;

        /// <summary>
        /// Gets the total number of warnings
        /// </summary>
        public int WarningCount => _warnings.Count;

        /// <summary>
        /// Adds a validation warning
        /// </summary>
        /// <param name="columnName">The column/field name that has the warning</param>
        /// <param name="message">The warning message</param>
        public void AddWarning(string columnName, string message)
        {
            if (string.IsNullOrEmpty(columnName) || string.IsNullOrEmpty(message))
                return;

            _warnings.Add(new ValidationWarning(columnName, message));
        }

        /// <summary>
        /// Adds multiple validation warnings
        /// </summary>
        /// <param name="warnings">The warnings to add</param>
        public void AddWarnings(IEnumerable<ValidationWarning> warnings)
        {
            if (warnings != null)
            {
                _warnings.AddRange(warnings);
            }
        }

        /// <summary>
        /// Gets warnings for a specific column
        /// </summary>
        /// <param name="columnName">The column name</param>
        /// <returns>Warnings for the specified column</returns>
        public IEnumerable<ValidationWarning> GetWarningsForColumn(string columnName)
        {
            return _warnings.Where(w => w.ColumnName.Equals(columnName, System.StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Checks if there are warnings for a specific column
        /// </summary>
        /// <param name="columnName">The column name</param>
        /// <returns>True if there are warnings for the column</returns>
        public bool HasWarningsForColumn(string columnName)
        {
            return _warnings.Any(w => w.ColumnName.Equals(columnName, System.StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Clears all warnings
        /// </summary>
        public void Clear()
        {
            _warnings.Clear();
        }

        /// <summary>
        /// Creates a successful validation result (no warnings)
        /// </summary>
        /// <returns>A validation result with no warnings</returns>
        public static ValidationResult Success()
        {
            return new ValidationResult();
        }

        /// <summary>
        /// Creates a validation result with a single warning
        /// </summary>
        /// <param name="columnName">The column name</param>
        /// <param name="message">The warning message</param>
        /// <returns>A validation result with the specified warning</returns>
        public static ValidationResult WithWarning(string columnName, string message)
        {
            var result = new ValidationResult();
            result.AddWarning(columnName, message);
            return result;
        }
    }

    /// <summary>
    /// Represents a single validation warning
    /// </summary>
    public class ValidationWarning
    {
        /// <summary>
        /// Initializes a new instance of ValidationWarning
        /// </summary>
        /// <param name="columnName">The column/field name</param>
        /// <param name="message">The warning message</param>
        public ValidationWarning(string columnName, string message)
        {
            ColumnName = columnName ?? string.Empty;
            Message = message ?? string.Empty;
        }

        /// <summary>
        /// Gets the column/field name that has the warning
        /// </summary>
        public string ColumnName { get; }

        /// <summary>
        /// Gets the warning message
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// Returns a string representation of the warning
        /// </summary>
        public override string ToString()
        {
            return $"{ColumnName}: {Message}";
        }
    }
}
