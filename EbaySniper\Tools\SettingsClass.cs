﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Threading;
using uBuyFirst.Auth;
using uBuyFirst.CustomClasses;
using uBuyFirst.Item;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Telegram;

namespace uBuyFirst.Tools
{
    [Obfuscation(Exclude = false,
        Feature = "preset(maximum);"
                  + "+anti debug;"
                  + "+anti dump;"
                  + "+anti ildasm;"
                  + "+anti tamper(key=dynamic);"
                  + "+constants;"
                  + "+ctrl flow;"
                  + "+invalid metadata;"
                  + "+ref proxy;"
                  + "-rename;")]
    [Obfuscation(Exclude = false,
        Feature = "preset(maximum);"
                  + "+anti debug;"
                  + "+anti dump;"
                  + "+anti ildasm;"
                  + "+anti tamper(key=dynamic);"
                  + "+constants;"
                  + "+ctrl flow;"
                  + "+invalid metadata;"
                  + "+ref proxy;"
                  + "-rename;", ApplyToMembers = true)]
    public class Settings
    {
        public int ZoomBarPics;
        public int ZoomBarPicsLarge;
        public int ZoomBarPicsSmall;
        public long UBuyFirstRedirectTimestamp { get; set; }

        public BindingList<CategorySpecific> CategorySpecifics { get; set; }
        public CustomBindingList<string> AiColumnsList { get; set; }

        public int Fontsize { get; set; }

        public int HighlightColor1 { get; set; }

        public int HighlightColor2 { get; set; }

        public int HighlightColor3 { get; set; }

        public bool IdleChkBoxChecked { get; set; }

        public decimal IdleTime { get; set; }

        public byte[] ListboxKeywords { get; set; }

        /// <summary>
        /// Folder structure for keyword organization (new format)
        /// </summary>
        public byte[] FolderStructure { get; set; }

        public bool Maximize { get; set; }

        public string[] PMain { get; set; }

        public bool SoundAlert { get; set; }

        public string SoundLocation { get; set; }

        public string[] WordsHighlight1 { get; set; }

        public string[] WordsHighlight2 { get; set; }

        public string[] WordsHighlight3 { get; set; }

        public decimal RowHeight { get; set; }

        public string BrowserBg { get; set; }

        // ReSharper disable once InconsistentNaming
        public List<EbayAccount> TList { get; set; }

        public string XFiltersList { get; set; }

        public bool IsUseProxyChecked { get; set; }

        public decimal FocusTopRowTimeout { get; set; }

        public string Skin { get; set; }

        public bool AutoStartSearch { get; set; }

        public string TimeZoneID { get; set; }

        public PushbulletSender Pushbullet { get; set; }
        public TelegramSender TelegramSender { get; set; }
        public AlertOptionsClass AlertSettings { get; set; }
        public Helpers.AdvancedSearchConfig RequestsConfig { get; set; }
        public bool LoadLargeImagesOnHover { get; set; }
        public bool QuickPurchaseAllQuantity { get; set; }
        public string GridViewFont { get; set; }
        public List<string> ViewsList { get; set; }
        public int ShortcutBuyModifier { get; set; }
        public int ShortcutBuyKey { get; set; }
        public int ShortcutMakeOfferModifier { get; set; }
        public int ShortcutMakeOfferKey { get; set; }
        public int ShortcutQuickBuyKey { get; set; }
        public int ShortcutQuickBuyModifier { get; set; }
        public int ShortcutGoToCheckoutModifier { get; set; }
        public int ShortcutGoToCheckoutKey { get; set; }
        public UserSettings.OpenInBrowserEnum OpenInBrowser { get; set; }
        public bool BestOfferSubtractShipping { get; set; }
        public SerializableDictionary<string, string> MakeOfferMessages { get; set; }
        public string MakeOfferSelectedMessage { get; set; }
        public int WindowMaximum { get; set; }
        public int WindowMinimum { get; set; }
        public int ShortcutImmediateBuyKey { get; set; }
        public int ShortcutImmediateBuyModifier { get; set; }
        public CookieProfile WindowProfile { get; set; }
        public int? InitialResultsLimit { get; set; }
        public int MaxResultsCount { get; set; }
        public bool ClickOnPriceOpensProductPage { get; set; }
        public bool WindowMaximized { get; set; }
        public string[] IgnoredSellers { get; set; }
        public string[] BlockedSellers { get; set; }
        public bool TotalPriceAsMaxPrice { get; set; }
        public bool SyncSearchTermsEnabled { get; set; }
        public string SyncSearchTermsUrl { get; set; }
        public int SyncSearchTermsInterval { get; set; }
        public bool ExternalDataEnabled { get; set; }
        public string? ExternalEndpointUrl { get; set; }
        public string? AiExternalEndpointUrl { get; set; }
        public string? InternalEndpointUrl { get; set; }
        public bool IsInternalEndpoint { get; set; }
        public bool IsAIEndpoint { get; set; }
        public int AiProviderIndex { get; set; }
        public int AiProviderModelIndex { get; set; }
        public string SyncSearchTermsFileHash { get; set; }
        public bool SendDescriptionAndPictures { get; set; }
        public bool WatchlistAutoRefreshEnabled { get; set; }
        public decimal DailyLimit { get; set; } = 10000m;
        public string DailyHistoryJson { get; set; } = "{}";
        public int WatchlistRefreshInterval { get; set; }
        public int PanelMaximum { get; set; }

        public static readonly ReaderWriterLockSlim SettingsReadWriteLock = new();
    }
}
