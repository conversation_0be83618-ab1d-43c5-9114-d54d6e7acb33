﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using DevExpress.XtraGrid;
using eBay.Service.Core.Soap;
using uBuyFirst.Auth;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Purchasing;
using uBuyFirst.Search;
using uBuyFirst.Time;

namespace uBuyFirst.Data
{
    public class DataList
    {
        private double _auctionPrice;
        private int _bids;
        private decimal _feedbackRating;
        private decimal _feedbackScore;
        private double _itemPrice;
        private DateTimeWithDiff _lastStatusCheck;
        private int _quantityAvailable;
        private double _shipAdditionalItem;
        private double _shipping;
        private string _shippingType;
        private DateTimeOffset? _startTimeLocal;
        private ItemStatus _itemStatus;
        private double _totalPrice;
        public EbayAccount EbayAccount;
        public DateTimeOffset? EndTime;
        public string GalleryUrl;
        public List<string> Pictures;
        public DataRow? Row;

        public DataList()
        {
            Pictures = new List<string>();
            CommitToBuy = false;
            AutoPay = true;
        }

        public double AuctionPrice
        {
            get => _auctionPrice;
            set
            {
                if (!_auctionPrice.Equals(value))
                    _auctionPrice = value;
            }
        }

        public bool AutoPay { get; set; }

        public bool BestOffer { get; set; }

        public int BestOfferCount { get; set; }

        public int Bids
        {
            get => _bids;
            set
            {
                if (_bids != value || value == 0)
                    _bids = value;
            }
        }

        public bool BuyItNowAvailable { get; set; }

        public string CategoryID { get; set; }

        public string CategoryName { get; set; }

        public bool CommitToBuy { get; set; }

        public string Condition { get; set; } = "";

        public string ConditionDescription { get; set; }

        public string ConditionID { get; set; } = "";
        public string Description { get; set; }
        public List<string>? ExcludeShipToLocation { get; set; }

        public decimal FeedbackRating
        {
            get => _feedbackRating;
            set
            {
                if (_feedbackRating != value || value == 0)
                    _feedbackRating = value;
            }
        }

        public decimal FeedbackScore
        {
            get => _feedbackScore;
            set
            {
                if (_feedbackScore != value || value == 0)
                    _feedbackScore = value;
            }
        }

        public DateTimeWithDiff FoundTime { get; set; }

        public string FromCountry { get; set; }
        public string? GroupItemID { get; set; }

        public string HighBidder { get; set; }

        public string ItemID { get; set; }

        public double ItemPrice
        {
            get => _itemPrice;
            set
            {
                if (!_itemPrice.Equals(value))
                    _itemPrice = value;
            }
        }

        public ItemPricing ItemPricing { get; set; }
        public ItemShipping ItemShipping { get; set; }
        public NameValueListTypeCollection ItemSpecifics { get; set; } = new();

        public DateTimeWithDiff LastStatusCheck
        {
            get => _lastStatusCheck;
            set
            {
                if (_lastStatusCheck == value)
                    return;

                _lastStatusCheck = value;
                //if (Debugger.IsAttached)
                //    Form1._synchronizationContext.Post(delegate
                //    {
                //        if (Row != null)
                //            Row["Status Time"] = _lastStatusCheck.LocalTime;
                //    }, value);
            }
        }

        public string[] ListingCodeType { get; set; }

        public string ListingType { get; set; }

        public string Location { get; set; }

        // Order property for backward compatibility
        private BuyingService.Order _order;
        public BuyingService.Order Order
        {
            get => _order;
            set
            {
                _order = value;

                // Update the specific order type properties based on the order action
                if (value != null)
                {
                    if (value.OrderAction == Placeoffer.OrderAction.BestOffer && value is BuyingService.BestOfferOrder bestOfferOrder)
                    {
                        BestOfferOrder = bestOfferOrder;
                    }
                    else if (value is BuyingService.BuyOrder buyOrder)
                    {
                        BuyOrder = buyOrder;
                    }
                }
            }
        }

        // Specific order type properties
        public BuyingService.BuyOrder BuyOrder { get; internal set; }
        public BuyingService.BestOfferOrder BestOfferOrder { get; internal set; }

        public long PageViews { get; set; }

        public string Payment { get; set; }

        public int QuantityAvailable
        {
            get => _quantityAvailable;
            set
            {
                if (_quantityAvailable != value || value == 0)
                    _quantityAvailable = value;
            }
        }

        public int QuantityTotal { get; set; }

        public string RelistParentID { get; set; }

        public string Returns { get; set; }

        public int? SecondsLeft
        {
            get
            {
                if (EndTime.HasValue)
                {
                    var timeSpan = (EndTime - DateTimeOffset.UtcNow);
                    return (int)timeSpan.Value.TotalSeconds;
                }

                return null;
            }
        }

        public string SellerName { get; set; } = "";
        public DateTimeOffset SellerRegistration { get; set; }
        public string SellerCountry { get; set; } = "";

        public double ShipAdditionalItem
        {
            get => _shipAdditionalItem;
            set
            {
                if (!_shipAdditionalItem.Equals(value))
                    _shipAdditionalItem = value;
            }
        }

        public double Shipping
        {
            get => _shipping;
            set
            {
                if (!_shipping.Equals(value))
                    _shipping = value;
            }
        }

        public int ShippingDays { get; set; }
        public int DispatchDays { get; set; }
        public string Authenticity { get; set; }
        public string ShippingDelivery { get; set; } = "";
        public string ShippingType => _shippingType;
        public List<string>? ShipToLocations { get; set; }

        public SiteCodeType Site { get; set; }

        public DateTimeWithDiff SoldTime { get; set; }

        public SearchSource Source { get; set; }

        public DateTimeOffset? StartTimeLocal
        {
            get => _startTimeLocal;
            set
            {
                if (value.HasValue)
                    _startTimeLocal = value;
            }
        }

        public ItemStatus ItemStatus => _itemStatus;

        public double StatusPriority { get; set; }

        public string StoreName { get; set; }
        public string SubSearch { get; set; } = "";
        public string Term { get; set; }

        /// <summary>
        /// The alias of the filter that triggered this item to be queued for checkout.
        /// Used to preserve filter identity when items have CheckoutPending status.
        /// </summary>
        public string TriggeringFilterAlias { get; set; } = "";

        public TimeSpan TimeLeft { get; set; }

        public int TimeSinceChecked => (int)(DateTime.UtcNow - _lastStatusCheck.Utc).TotalSeconds;

        public int? TimeSinceEnded
        {
            get
            {
                if (EndTime.HasValue)
                    return (int)(DateTime.UtcNow - EndTime.Value).TotalSeconds;

                return null;
            }
        }

        public int TimeSinceFound => (int)(DateTime.UtcNow - FoundTime.Utc).TotalSeconds;

        public string Title { get; set; }

        public string ToCountry { get; set; }

        public double TotalPrice
        {
            get => _totalPrice;
            set
            {
                if (!_totalPrice.Equals(value))
                {
                    _totalPrice = value;
                }

            }
        }

        public string Brand { get; set; } = "";
        public string Model { get; set; } = "";
        public string UPC { get; set; } = "";
        public string MPN { get; set; } = "";
        public string ProductReferenceID { get; set; } = "";
        public bool Variation { get; set; }

        public string VATNumber { get; set; }
        public string SellerStore { get; set; }
        public bool SellerIsBusiness { get; set; }
        public Source2 SourceB { get; set; }
        public EBaySite EBaySite { get; set; }
        public string Zip { get; set; }
        public string AvailableTo { get; set; }
        public GridControl GridControl { get; set; }

        public enum Source2
        {
            SS,
            SB
        }
        public void SetStatus(ItemStatus value)
        {
            try
            {
                if (_itemStatus != value)
                {
                    _itemStatus = value;
                }
                else if (_itemStatus == ItemStatus.Active)
                {
                    _itemStatus = value;
                }

                if (Row != null)
                    if (Row["Status"].ToString() != value.ToString())
                    {
                        Row["Status"] = value;
                    }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

        }

        public void SetShippingType(string shippingType)
        {
            _shippingType = shippingType;
        }

        public string ListingTypeToString()
        {
            return string.Join(", ", ListingCodeType);
        }

        public override string ToString()
        {
            return "";
        }

        public string GetAffiliateLink()
        {
            var affiliateAction = Term == "Watchlist" ? "Watch" : "Browser";
            var url = $"https://www.{EBaySite.Domain}/itm/{ItemID}/";

            var affiliateParams = AffiliateTool.GetAffiliateParams(ItemID, EBaySite, affiliateAction);
            if (!string.IsNullOrEmpty(affiliateParams))
                url += "?" + affiliateParams;

            return url;
        }

        public string GetAffiliateLinkFor_uBuyFirstRedirect()
        {
            var url = Config.AffiliateRedirectUrl;
            var affiliateAction = Term == "Watchlist" ? "Watch" : "Browser";
            var affiliateParams = AffiliateTool.GetAffiliateParams(ItemID, EBaySite, affiliateAction);
            if (string.IsNullOrEmpty(affiliateParams))
            {
                url += "?";
            }
            else
                url += $"?{affiliateParams}&";

            var itemID = "itemID=" + ItemID;
            var site = "&site=" + EBaySite.Domain;
            url += itemID;
            url += site;

            return url;
        }
        internal string GetCheckoutLink()
        {
            int quantity;
            if (Placeoffer.PurchaseAllQuantity)
                quantity = QuantityAvailable;
            else
                quantity = 1;

            var url = $"https://pay.{EBaySite.Domain}/rxo?item={ItemID}&action=create&transactionid=-1&quantity={quantity}";

            var affiliateAction = Term == "Watchlist" ? "Watch" : "Checkout";
            var affiliateParams = AffiliateTool.GetAffiliateParams(ItemID, EBaySite, affiliateAction);
            if (!string.IsNullOrEmpty(affiliateParams))
                url += "&" + affiliateParams;

            return url;
        }

        public static void ParseItem(DataList datalist, ItemType theItem)
        {
            datalist.QuantityTotal = theItem.Quantity;
            datalist.QuantityAvailable = theItem.Quantity - theItem.SellingStatus.QuantitySold;
            //datalist.q = theItem.SellingStatus.QuantitySold;
            //TODO #FormBid
            //ItemParser.ParseSimpleFields(datalist, theItem);
            //ItemParser.ParseAuxFields(datalist, theItem);
        }
    }

    public enum ItemStatus
    {
        Unknown,
        Active,
        Updated,
        CreatingSession,
        PaymentInProgress,
        Sold,
        Ended,
        Incorrect,
        LostOrBroken,
        NotAvailable,
        OtherListingError,
        SellToHighBidder,
        BestOfferInProgress,
        BestOfferSubmitted,
        TestPurchase,
        CheckoutPending
    }
}
