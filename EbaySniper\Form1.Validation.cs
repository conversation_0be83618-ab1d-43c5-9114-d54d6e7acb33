﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;
using uBuyFirst.UI.Validation;

namespace uBuyFirst
{
    /// <summary>
    /// Form1 partial class containing validation-related UI controller methods
    /// </summary>
    public partial class Form1
    {
        #region Search Start Validation

        /// <summary>
        /// Validates all enabled keywords before search start and automatically disables any with validation issues.
        /// This prevents runtime errors during search execution by catching validation problems proactively.
        /// </summary>
        internal void ValidateAndDisableInvalidKeywordsOnSearchStart()
        {
            if (_validationCoordinator == null)
                return;

            var keywordsWithIssues = new List<string>();
            var disabledKeywords = new List<string>();

            // First, validate all nodes to ensure we have current validation state
            _validationCoordinator.ValidateAllNodes(treeList1, _ebaySearches);

            // Check all enabled keywords for validation issues
            foreach (TreeListNode node in treeList1.Nodes)
            {
                CheckNodeAndChildrenForValidationIssues(node, keywordsWithIssues, disabledKeywords);
            }

            // Show notification if any keywords were disabled
            if (keywordsWithIssues.Any())
            {
                var warningMessage = "The following keywords have validation issues and have been automatically disabled:\n\n";
                warningMessage += string.Join("\n", keywordsWithIssues.Select(alias => $"• {alias}"));
                warningMessage += "\n\nPlease fix the validation issues and re-enable these keywords if you want to include them in the search.";

                if (disabledKeywords.Any())
                {
                    warningMessage += $"\n\nTotal disabled keywords: {disabledKeywords.Count}";

                    // Refresh the TreeList to show the updated checkbox states
                    treeList1.RefreshDataSource();
                }

                XtraMessageBox.Show(this, warningMessage, "Keywords Auto-Disabled Due to Validation Issues",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Recursively checks a node and its children for validation issues and disables them if found.
        /// Used specifically during search start validation to ensure only valid keywords are enabled.
        /// </summary>
        private void CheckNodeAndChildrenForValidationIssues(TreeListNode node, List<string> keywordsWithIssues, List<string> disabledKeywords)
        {
            if (node == null) return;

            var dataRecord = treeList1.GetDataRecordByNode(node);
            
            // Check if this node has validation warnings
            if (_validationCoordinator.HasWarnings(treeList1, node))
            {
                switch (dataRecord)
                {
                    case Keyword2Find kw when kw.KeywordEnabled == CheckState.Checked:
                        // Disable the keyword and add to lists
                        kw.KeywordEnabled = CheckState.Unchecked;
                        keywordsWithIssues.Add(kw.Alias);
                        disabledKeywords.Add(kw.Alias);
                        break;
                    case ChildTerm ct when ct.Enabled:
                        // Disable the child term and add to lists
                        ct.Enabled = false;
                        keywordsWithIssues.Add($"{ct.Alias} (Child Term)");
                        disabledKeywords.Add(ct.Alias);
                        break;
                    case KeywordFolder folder:
                        // For folders, just add to the issues list but don't disable
                        // (folders don't have an enabled/disabled state)
                        keywordsWithIssues.Add($"{folder.Name} (Folder)");
                        break;
                }
            }

            // Recursively check child nodes
            foreach (TreeListNode childNode in node.Nodes)
            {
                CheckNodeAndChildrenForValidationIssues(childNode, keywordsWithIssues, disabledKeywords);
            }
        }

        #endregion

        #region Manual Validation Operations

        /// <summary>
        /// Validates all keywords manually (for user-triggered validation)
        /// </summary>
        public void ValidateAllKeywordsManually()
        {
            if (_validationCoordinator == null)
                return;

            // Validate all nodes
            _validationCoordinator.ValidateAllNodes(treeList1, _ebaySearches);

            // Refresh the TreeList to show updated validation styling
            treeList1.RefreshDataSource();

            // Show validation summary
            ShowValidationSummary();
        }

        /// <summary>
        /// Validates only the selected keywords in the TreeList
        /// </summary>
        public void ValidateSelectedKeywords()
        {
            if (_validationCoordinator == null || treeList1.Selection.Count == 0)
                return;

            var selectedNodes = treeList1.Selection.ToList();
            
            // Validate selected nodes
            _validationCoordinator.ValidateNodes(treeList1, selectedNodes, _ebaySearches);

            // Refresh the TreeList to show updated validation styling
            treeList1.RefreshDataSource();

            // Show summary for selected nodes only
            ShowValidationSummaryForNodes(selectedNodes);
        }

        #endregion

        #region Validation Helpers

        /// <summary>
        /// Gets the parent keyword alias for a child term node
        /// </summary>
        private string GetParentKeywordAlias(TreeListNode childNode)
        {
            if (childNode.ParentNode != null)
            {
                var parentRecord = treeList1.GetDataRecordByNode(childNode.ParentNode);
                if (parentRecord is Keyword2Find parentKw)
                {
                    return parentKw.Alias;
                }
            }
            return "Unknown";
        }

        /// <summary>
        /// Shows a comprehensive validation summary dialog
        /// </summary>
        private void ShowValidationSummary()
        {
            if (_validationCoordinator?.WarningManager == null)
                return;

            var totalWarnings = _validationCoordinator.WarningManager.GetWarningCount();
            
            if (totalWarnings == 0)
            {
                XtraMessageBox.Show(this, "All keywords passed validation successfully!", 
                    "Validation Summary", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var summaryMessage = $"Validation completed with {totalWarnings} warning(s) found.\n\n";
            summaryMessage += "Keywords with validation issues are highlighted in the tree.\n";
            summaryMessage += "Hover over highlighted cells to see specific validation messages.";

            XtraMessageBox.Show(this, summaryMessage, "Validation Summary", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// Shows validation summary for specific nodes
        /// </summary>
        private void ShowValidationSummaryForNodes(List<TreeListNode> nodes)
        {
            if (_validationCoordinator?.WarningManager == null || !nodes.Any())
                return;

            var warningCount = 0;
            var nodeNames = new List<string>();

            foreach (var node in nodes)
            {
                var nodeId = NodeIdentifierService.GetNodeId(treeList1, node);
                var nodeWarnings = _validationCoordinator.WarningManager.GetNodeWarnings(nodeId);
                
                if (nodeWarnings.Any())
                {
                    warningCount += nodeWarnings.Count;
                    var dataRecord = treeList1.GetDataRecordByNode(node);
                    
                    switch (dataRecord)
                    {
                        case Keyword2Find kw:
                            nodeNames.Add(kw.Alias);
                            break;
                        case ChildTerm ct:
                            nodeNames.Add($"{ct.Alias} (Child Term)");
                            break;
                        case KeywordFolder folder:
                            nodeNames.Add($"{folder.Name} (Folder)");
                            break;
                    }
                }
            }

            if (warningCount == 0)
            {
                XtraMessageBox.Show(this, "Selected keywords passed validation successfully!", 
                    "Validation Summary", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var summaryMessage = $"Validation completed for {nodes.Count} selected item(s).\n\n";
            summaryMessage += $"Found {warningCount} warning(s) in the following items:\n";
            summaryMessage += string.Join("\n", nodeNames.Select(name => $"• {name}"));

            XtraMessageBox.Show(this, summaryMessage, "Validation Summary", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        #endregion
    }
}
