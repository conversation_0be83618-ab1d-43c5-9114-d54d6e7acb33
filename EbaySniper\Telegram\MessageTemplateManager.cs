﻿using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using uBuyFirst.Views;


namespace uBuyFirst.Telegram
{
    public class MessageTemplateManager
    {
        TelegramSender _telegramSender;
        public MessageTemplateManager(TelegramSender telegramSender)
        {
            _telegramSender = telegramSender;
        }

        public bool SetBody(string bodyTemplateStr)
        {
            var bodyColumns = Helpers.RegexValues(bodyTemplateStr, "{(.*?)}");

            if (!ColumnsManager.CheckExistingColumns(bodyColumns))
                return false;

            _telegramSender.BodyColumns = bodyColumns;
            _telegramSender.BodyTemplate = bodyTemplateStr;

            return true;
        }

        public string GetFormattedMessage(DataRow row, DataList d)
        {
            // Early validation to prevent processing deleted/detached rows
            if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached || row.Table == null)
            {
                return "";
            }

            var body = _telegramSender.BodyTemplate;
            if (string.IsNullOrWhiteSpace(body))
            {
                return "";
            }

            foreach (var columnName in _telegramSender.BodyColumns)
            {
                if (columnName == "ViewUrl" || columnName == "CheckoutUrl" || columnName == "ContactUrl")
                    continue;

                body = body.Replace("{" + columnName + "}", GetRowValue(row, columnName, d));
            }

            return body;
        }

        private static string GetRowValue(DataRow? row, string columnName, DataList d)
        {
            // Check if row is valid and still in table before accessing any column data
            if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached || row.Table == null)
            {
                return "";
            }

            switch (columnName)
            {
                case "Total Price":
                    return d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();

                case "Item Price":
                    return d.ItemPricing.ItemPrice.FormatPrice();

                case "Auction Price":
                    if (d.ItemPricing.AuctionPrice != null)
                    {
                        return d.ItemPricing.AuctionPrice.FormatPrice();
                    }
                    return "0";

                case "Shipping":
                    return d.ItemShipping.FullSingleShippingPrice.FormatPrice();

                case "Ship Additional Item":
                    return d.ItemShipping.ShipAdditionalItem.FormatPrice();

                case "Found Time":
                    return d.FoundTime.ToString();

                case "Description":
                    try
                    {
                        // Additional safety check before accessing column data
                        if (!row.Table.Columns.Contains("Description") || row.IsNull("Description"))
                            return "";

                        var description = row["Description"].ToString();

                        if (description.Length > 3000)
                            return "";

                        description = System.Net.WebUtility.HtmlDecode(description);
                        description = Regex.Replace(description, "(<style.+?</style>)|(<script.+?</script>)", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                        var plainDescription = Regex.Replace(description, @"<[^>]*>", string.Empty, RegexOptions.Compiled);

                        return plainDescription;
                    }
                    catch (RowNotInTableException)
                    {
                        // Row was removed from table while we were processing it
                        return "";
                    }

                case "ItemID":
                    if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("1BE9-4A48"))
                    {
                        try
                        {
                            if (!row.Table.Columns.Contains("ItemID") || row.IsNull("ItemID"))
                                return "";

                            return row["ItemID"]?.ToString() ?? "";
                        }
                        catch (RowNotInTableException)
                        {
                            // Row was removed from table while we were processing it
                            return "";
                        }
                    }

                    return "";

                default:
                    try
                    {
                        if (row?.Table?.Columns != null &&
                            row.Table.Columns.Contains(columnName) &&
                            !row.IsNull(columnName) &&
                            row[columnName] != null)
                        {
                            return row[columnName].ToString();
                        }

                        return "";
                    }
                    catch (RowNotInTableException)
                    {
                        // Row was removed from table while we were processing it
                        return "";
                    }
            }
        }
    }
}
