﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using MQTTnet;
using Newtonsoft.Json.Converters;
using uBuyFirst.License;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Search;
using uBuyFirst.SkuManager;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class Form1
    {
        private void barButtonStart_ItemClick(object sender, ItemClickEventArgs e)
        {
            BtnMainStartClick();
        }

        internal async void BtnMainStartClick()
        {
            while (true)
            {
                if (LicenseUtility.LicenseCheckCompleted && EBayAccountInitCompleted)
                    break;

                await Task.Delay(400);
            }

            if (_searchService is { Running: true })
                await StopWorking();
            else
                StartWorking();
        }

        public void StartWorking()
        {
            if (LicenseUtility.CurrentSubscriptionType == LicenseUtility.SubscriptionType.NotActivated)
            {
                ShowActivationWindow();
                return;
            }

            SaveSettings();
            treeList1.FocusedNode = null;

            // Validate all enabled keywords before starting search and disable any with issues
            ValidateAndDisableInvalidKeywordsOnSearchStart();


            var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
            foreach (TreeListNode node in treeList1.Nodes)
            {
                if (treeList1.GetDataRecordByNode(node) is Keyword2Find kw)
                {
                    // Always try to parse Lucene query for valid keywords
                    if (!string.IsNullOrWhiteSpace(kw.Kws))
                    {
                        try
                        {
                            kw.LuceneQuery = KeywordHelpers.ParseKeyword2LuceneQuery(kw.Kws, false);
                        }
                        catch (Exception ex)
                        {
                            // If Lucene parsing fails, log it but don't block execution
                            System.Diagnostics.Debug.WriteLine($"Lucene query parsing failed for keyword '{kw.Alias}': {ex.Message}");
                        }
                    }
                }
            }

            SearchService.IdleTimeMaximum = barEnableIdleTimeout.Checked ? Convert.ToDecimal(barIdleTimeoutMinutes.EditValue.ToString()) : 0;

            if (_ebaySearches.ChildrenCore.Count == 0)
            {
                XtraMessageBox.Show(En_US.Form1_BtnMainStartClick_Please__add_a_keyword);
                MakeRunButtonStart();

                return;
            }

            var isAllKwDisabled = _ebaySearches.ChildrenCore.All(find => find.KeywordEnabled != CheckState.Checked);
            if (isAllKwDisabled)
            {
                XtraMessageBox.Show(En_US.Form1_BtnMainStartClick_Please__enable_at_least_one_keyword);
                MakeRunButtonStart();

                return;
            }

            if (!ConnectionConfig.BrowseAPIEnabled)
                if (NoEbayAccountWarningOnStart())
                    return;

            MakeRunButtonStop();

            // Stop and start LocalSku manager server if applicable.
            PythonProcessManager.StopInternalSkuManagerScript();
            StartInternalSkuManagerScript();


            MainStart(_ebaySearches.ChildrenCore);
        }

        private void StartPushClients()
        {
            if (Program.Sandbox)
                return;
            if (Config.SafeEnabled)
                return;

            var user = SeerUser.CreateSeerUser(_ebaySearches.ChildrenCore);
            if (ConnectionConfig.MQTTEnabled)
            {
                _mqttManager.Connect(user);
            }
            else
            {
                if (_ablyClient.GetConnectionState() != IO.Ably.Realtime.ConnectionState.Connected || _ablyClient.GetConnectionState() != IO.Ably.Realtime.ConnectionState.Connecting)
                    _ablyClient.BeConnected(user);
            }
        }

        private void PostMQQTItemToResultsGrid(MqttApplicationMessage message)
        {
            var data = string.Empty;
            if (message.PayloadSegment is { Count: > 0, Array: not null })
            {
                data = Encoding.UTF8.GetString(message.PayloadSegment.Array, message.PayloadSegment.Offset, message.PayloadSegment.Count);
            }

            if (!data.StartsWith("object:"))
            {
                return;
            }

            var compressedText = data.Substring(7);
            var itemJson = Serializator.DecompressString(compressedText);
            var itemDeserialized = Newtonsoft.Json.JsonConvert.DeserializeObject<ItemDeserialized>(itemJson, new IsoDateTimeConverter());
            if (itemDeserialized == null)
                return;

            var searchSource = SearchSource.RSS3;

            if (Enum.TryParse(itemDeserialized.S, true, out SearchSource searchSourceParsed))
            {
                searchSource = searchSourceParsed;
            }
            var keywordAlias = itemDeserialized.A;
            API.ShoppingAPI.ShoppingAPIJson.SimpleItemType simpleItem = null;
            global::BrowseAPI.Item browseItem = null;
            string sellerInfoJson = null;
            var kw2Find = _ebaySearches.ChildrenCore.FirstOrDefault(a => a.Alias == keywordAlias);
            if (kw2Find == null)
                return;

            var itemID = "";
            var sellerName = "";
            var multivariation = false;
            var categoryPath = new string[] { };
            if (itemDeserialized.I != null)
            {
                simpleItem = itemDeserialized.I;
                itemID = simpleItem.ItemID;
                categoryPath = simpleItem.PrimaryCategoryIDPath?.Split(':').ToArray();
            }

            if (itemDeserialized.BrowseItem != null)
            {
                try
                {
                    browseItem = itemDeserialized.BrowseItem;
                    categoryPath = browseItem.CategoryIdPath?.Split('|').ToArray();
                }
                catch (Exception e)
                {
                    Debug.WriteLine(e);
                }
            }

            if (itemDeserialized.SellerJson != null)
            {
                sellerInfoJson = itemDeserialized.SellerJson;
            }

            if (itemDeserialized.ID != null)
            {
                itemID = itemDeserialized.ID;
            }

            if (itemDeserialized.Multivariation != null)
            {
                multivariation = itemDeserialized.Multivariation;
            }

            if (itemDeserialized.Seller != null)
            {
                sellerName = itemDeserialized.Seller;
            }

            var addedItems = new List<string> { itemID };
            if (kw2Find.ChildrenCore.Count == 0)
            {
                if (searchSource != SearchSource.OOS)
                    addedItems = SearchService.AddItemsToStorage(addedItems);
            }
            else
            {
                addedItems = SearchService.AddItemsToStorage(kw2Find, addedItems);
            }

            if (addedItems.Count == 0)
                return;

            Interlocked.Increment(ref Stat.ItemsFoundCounter);
            var startTime = simpleItem?.StartTime ?? DateTime.SpecifyKind(DateTime.MinValue, DateTimeKind.Utc);
            var foundItem = new FoundItem(simpleItem, kw2Find, itemID, startTime, searchSource, multivariation, false, sellerName, browseItem, sellerInfoJson, categoryPath);

            _synchronizationContext.Post(_ => HandleNewItem(foundItem), null);
        }

        public class ItemDeserialized
        {
            public string S { get; set; } // SearchSource
            public string A { get; set; } // keywordAlias
            public API.ShoppingAPI.ShoppingAPIJson.SimpleItemType I { get; set; } // simpleItem
            public global::BrowseAPI.Item BrowseItem { get; set; } // browseItem
            public string SellerJson { get; set; } // sellerInfo
            public string ID { get; set; } // itemID
            public bool Multivariation { get; set; } // multivariation
            public string Seller { get; set; } // sellerName
        }

        private bool NoEbayAccountWarningOnStart()
        {
            if (EBayAccountsList.Count == 0)
            {
                if (barCheckItemAutoStartSearch.Checked)
                {
                    notifyIcon1.BalloonTipTitle = @"You have not authenticated your eBay account with uBuyFirst.";
                    notifyIcon1.BalloonTipText = @"You won't be able to purchase 'commit to buy' items directly through the application.";
                    notifyIcon1.ShowBalloonTip(15000);
                }
                else
                {
                    var dialogResult = XtraMessageBox.Show(@"You have not authenticated your eBay account with uBuyFirst.
You won't be able to purchase 'commit to buy' items directly through the application.
Authenticate now?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1);
                    if (dialogResult == DialogResult.Yes)
                    {
                        AddEbayAccount();
                        MakeRunButtonStart();

                        return true;
                    }
                }
            }

            return false;
        }

        private void StartInternalSkuManagerScript()
        {
            // Only start Internal Sku Manager if the feature is on.
            if ((UserSettings.IsInternalEndpoint || UserSettings.IsAIEndpoint) &&
                UserSettings.ExternalDataEnabled)
            {
                PythonProcessManager.StartInternalSkuManagerScript();
            }
        }





        public async Task StopWorking()
        {
            ProgramState.SearchingStopwatchGA4.Reset();
            SaveSettings();
            // Shut down Script process.
            GoogleAnalytics?.ReportCounters();
            _ablyClient.BeDisconnected();
            await _mqttManager.Disconnect();
            _telegramSender?.ClearQueue();
            barButtonStart.Caption = En_US.Form1_BtnMainStartClick_Stopping;
            barButtonStart.Enabled = false;
            if (_searchService != null)
                await _searchService.StopSearch().ContinueWith(a => MakeRunButtonStart()).ConfigureAwait(false);
        }

        private void MakeRunButtonStart()
        {
            barButtonStart.Enabled = true;
            barButtonStart.Caption = En_US.Form1_BackgroundWorker1RunWorkerCompleted_Start;
            barButtonStart.ImageOptions.SvgImage = Resources.Start;
            barButtonStart.Appearance.BackColor = Color.LightGreen;
        }

        private void MakeRunButtonStop()
        {
            barButtonStart.Caption = En_US.Form1_BtnMainStartClick_Stop;
            barButtonStart.ImageOptions.SvgImage = Resources.Stop;
            barButtonStart.Appearance.BackColor = Color.Salmon;
        }




    }
}
