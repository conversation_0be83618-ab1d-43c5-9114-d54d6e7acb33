using System.Collections.Generic;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Defines the contract for validating TreeList node data
    /// </summary>
    /// <typeparam name="T">The type of data to validate</typeparam>
    public interface ITreeListValidator<in T>
    {
        /// <summary>
        /// Validates the specified item and returns validation results
        /// </summary>
        /// <param name="item">The item to validate</param>
        /// <param name="context">Validation context containing additional information</param>
        /// <returns>Validation result containing any warnings or errors</returns>
        ValidationResult Validate(T item, ValidationContext context);

        /// <summary>
        /// Determines if this validator can handle the specified item type
        /// </summary>
        /// <param name="item">The item to check</param>
        /// <returns>True if this validator can handle the item</returns>
        bool CanValidate(object item);
    }

    /// <summary>
    /// Non-generic interface for validator discovery and factory patterns
    /// </summary>
    public interface ITreeListValidator
    {
        /// <summary>
        /// Validates the specified item and returns validation results
        /// </summary>
        /// <param name="item">The item to validate</param>
        /// <param name="context">Validation context containing additional information</param>
        /// <returns>Validation result containing any warnings or errors</returns>
        ValidationResult Validate(object item, ValidationContext context);

        /// <summary>
        /// Determines if this validator can handle the specified item type
        /// </summary>
        /// <param name="item">The item to check</param>
        /// <returns>True if this validator can handle the item</returns>
        bool CanValidate(object item);
    }
}
