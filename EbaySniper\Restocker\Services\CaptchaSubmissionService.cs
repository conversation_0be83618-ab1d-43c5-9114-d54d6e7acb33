﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using uBuyFirst.Purchasing;
using uBuyFirst.Purchasing.Cookies;
using System.Diagnostics;
using Newtonsoft.Json;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for submitting solved captcha responses to eBay's captcha submission endpoint
    /// </summary>
    public class CaptchaSubmissionService
    {
        private const string CAPTCHA_SUBMIT_URL = "https://www.ebay.com/splashui/captcha_submit";

        /// <summary>
        /// Gets the GUID from the captcha/init endpoint
        /// </summary>
        /// <param name="cookieContainer">Cookie container containing eBay cookies</param>
        /// <param name="iid">The iid parameter from the captcha page</param>
        /// <returns>GUID from captcha/init endpoint, or a new GUID if call fails</returns>
        private async Task<string> GetGuidFromCaptchaInitAsync(CookieContainer cookieContainer, string iid = null)
        {
            try
            {
                Debug.WriteLine("Calling captcha/init endpoint to get GUID...");

                // Prepare the request payload
                var payload = new
                {
                    appName = "orch",
                    provider = (string)null,
                    wisbProvider = "",
                    iid = iid ?? Guid.NewGuid().ToString()
                };

                Debug.WriteLine($"Captcha init payload: {JsonConvert.SerializeObject(payload)}");

                // Prepare headers for the captcha/init request
                var headers = new Dictionary<string, string>
                {
                    ["accept"] = "*/*",
                    ["accept-language"] = "en-US,en;q=0.5",
                    ["accept-encoding"] = "gzip, deflate, br, zstd",
                    ["referer"] = "https://www.ebay.com/splashui/captcha?ap=1&appName=orch&ru=https%3A%2F%2Fpay.ebay.com",
                    ["content-type"] = "application/json",
                    ["x-ebay-redirect-referer"] = "https://pay.ebay.com/rxo",
                    ["x-ebay-c-correlation-session"] = "si=1ab89b751980ac723e02bb6affef9e31,c=124,operationId=2551517,trk-gflgs=",
                    ["origin"] = "https://www.ebay.com",
                    ["sec-fetch-dest"] = "empty",
                    ["sec-fetch-mode"] = "cors",
                    ["sec-fetch-site"] = "same-origin",
                    ["priority"] = "u=4",
                    ["te"] = "trailers"
                };

                var referrer = new Uri("https://www.ebay.com/splashui/captcha?ap=1&appName=orch&ru=https%3A%2F%2Fpay.ebay.com");
                var origin = "https://www.ebay.com";

                // Call the captcha/init endpoint
                var result = await HttpServiceHelper.PostJsonAsync(
                    "https://www.ebay.com/captcha/init",
                    cookieContainer,
                    payload,
                    headers,
                    referrer,
                    origin,
                    acceptLanguage: "en-US,en;q=0.5"
                );

                if (result.IsSuccess)
                {
                    Debug.WriteLine($"Captcha init response: {result.Content}");

                    // Parse the JSON response to extract the GUID
                    var response = JsonConvert.DeserializeObject<dynamic>(result.Content);
                    if (response?.guid != null)
                    {
                        var guid = response.guid.ToString();
                        Debug.WriteLine($"Successfully extracted GUID from captcha/init: {guid}");
                        return guid;
                    }
                    else
                    {
                        Debug.WriteLine("No GUID found in captcha/init response, falling back to new GUID");
                        return Guid.NewGuid().ToString();
                    }
                }
                else
                {
                    Debug.WriteLine($"Captcha init failed with status {result.StatusCode}: {result.Content}, falling back to new GUID");
                    return Guid.NewGuid().ToString();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception while calling captcha/init: {ex.Message}, falling back to new GUID");
                return Guid.NewGuid().ToString();
            }
        }

        /// <summary>
        /// Result of a captcha submission attempt
        /// </summary>
        public class CaptchaSubmissionResult
        {
            public bool IsSuccess { get; set; }
            public string? ErrorMessage { get; set; }
            public string? ResponseContent { get; set; }
            public HttpStatusCode? StatusCode { get; set; }

            public static CaptchaSubmissionResult Success(string responseContent)
            {
                return new CaptchaSubmissionResult
                {
                    IsSuccess = true,
                    ResponseContent = responseContent
                };
            }

            public static CaptchaSubmissionResult Failure(string errorMessage, HttpStatusCode? statusCode = null)
            {
                return new CaptchaSubmissionResult
                {
                    IsSuccess = false,
                    ErrorMessage = errorMessage,
                    StatusCode = statusCode
                };
            }
        }

        /// <summary>
        /// Submits a solved captcha response to eBay's captcha submission endpoint
        /// </summary>
        /// <param name="srtParameter">The srt parameter extracted from the Location header</param>
        /// <param name="ruParameter">The ru (return URL) parameter</param>
        /// <param name="hCaptchaResponse">The solved hCaptcha response token</param>
        /// <param name="cookieContainer">Cookie container with eBay session cookies</param>
        /// <returns>Result of the captcha submission</returns>
        public async Task<CaptchaSubmissionResult> SubmitCaptchaAsync(
            string srtParameter,
            string ruParameter,
            string hCaptchaResponse,
            CookieContainer cookieContainer)
        {
            if (string.IsNullOrEmpty(srtParameter))
                return CaptchaSubmissionResult.Failure("SRT parameter is required");

            if (string.IsNullOrEmpty(ruParameter))
                return CaptchaSubmissionResult.Failure("RU parameter is required");

            if (string.IsNullOrEmpty(hCaptchaResponse))
                return CaptchaSubmissionResult.Failure("hCaptcha response is required");

            if (cookieContainer == null)
                return CaptchaSubmissionResult.Failure("Cookie container is required");

            try
            {
                Debug.WriteLine($"Submitting captcha with SRT: {srtParameter?.Substring(0, Math.Min(50, srtParameter.Length))}...");

                // Get GUID from captcha/init endpoint
                var extractedGuid = await GetGuidFromCaptchaInitAsync(cookieContainer);

                // Generate captchaTokenInput JSON
                var currentTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                var captchaTokenInput = WebUtility.UrlEncode($"{{\"guid\":\"{extractedGuid}\",\"provider\":\"hcaptcha\",\"appName\":\"orch\",\"iid\":\"{Guid.NewGuid()}\",\"pvt\":{currentTimestamp},\"cvt\":{currentTimestamp + 36},\"crt\":{currentTimestamp + 5380},\"token\":\"{hCaptchaResponse}\"}}");

                Debug.WriteLine($"Generated captchaTokenInput: {captchaTokenInput}");

                // Prepare form data as specified by the user
                var formData = new Dictionary<string, string>
                {
                    ["srt"] = srtParameter,
                    ["ru"] = ruParameter,
                    ["h-captcha-response"] = hCaptchaResponse,
                    ["captchaTokenInput"] = captchaTokenInput
                };

                // Prepare headers as specified by the user
                var headers = new Dictionary<string, string>
                {
                    ["accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    ["accept-language"] = "en-US,en;q=0.5",
                    ["accept-encoding"] = "gzip, deflate, br, zstd",
                    ["origin"] = "https://www.ebay.com",
                    ["referer"] = "https://www.ebay.com/splashui/captcha?ap=1&appName=orch&ru=https%3A%2F%2Fpay.ebay.com",
                    ["upgrade-insecure-requests"] = "1",
                    ["sec-fetch-dest"] = "document",
                    ["sec-fetch-mode"] = "navigate",
                    ["sec-fetch-site"] = "same-origin",
                    ["sec-fetch-user"] = "?1",
                    ["priority"] = "u=0, i",
                    ["te"] = "trailers"
                };

                var referrer = new Uri("https://www.ebay.com/splashui/captcha?ap=1&appName=orch&ru=https%3A%2F%2Fpay.ebay.com");
                var origin = "https://www.ebay.com";

                // Submit the captcha using HttpServiceHelper
                var result = await HttpServiceHelper.PostFormUrlEncodedAsync(
                    CAPTCHA_SUBMIT_URL,
                    cookieContainer,
                    formData,
                    headers,
                    referrer,
                    origin,
                    allowAutoRedirect: true, // Allow redirects for successful captcha submission
                    acceptLanguage: "en-US,en;q=0.5"
                );

                if (result.IsSuccess)
                {
                    Debug.WriteLine("Captcha submission successful");
                    return CaptchaSubmissionResult.Success(result.Content);
                }
                else
                {
                    var errorMessage = $"Captcha submission failed with status {result.StatusCode}: {result.Content}";
                    Debug.WriteLine(errorMessage);
                    return CaptchaSubmissionResult.Failure(errorMessage, result.StatusCode);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Exception during captcha submission: {ex.Message}";
                Debug.WriteLine(errorMessage);
                return CaptchaSubmissionResult.Failure(errorMessage);
            }
        }
    }
}
