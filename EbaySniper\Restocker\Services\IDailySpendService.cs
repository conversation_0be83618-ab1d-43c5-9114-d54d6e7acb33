using System;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for tracking and managing daily spending limits for restock purchases
    /// </summary>
    public interface IDailySpendService
    {
        /// <summary>
        /// Gets the total amount spent today in USD
        /// </summary>
        /// <returns>Amount spent today in USD</returns>
        decimal GetTodaySpent();

        /// <summary>
        /// Gets the current daily spending limit in USD
        /// </summary>
        /// <returns>Daily spending limit in USD</returns>
        decimal GetDailyLimit();

        /// <summary>
        /// Sets the daily spending limit in USD
        /// </summary>
        /// <param name="limit">Daily spending limit in USD</param>
        void SetDailyLimit(decimal limit);

        /// <summary>
        /// Checks if a purchase amount can be made without exceeding the daily limit
        /// </summary>
        /// <param name="amountUsd">Purchase amount in USD</param>
        /// <returns>True if purchase can be made, false if it would exceed the limit</returns>
        bool CanPurchase(decimal amountUsd);

        /// <summary>
        /// Gets the remaining budget for today in USD
        /// </summary>
        /// <returns>Remaining budget in USD (limit - spent)</returns>
        decimal GetRemainingBudget();

        /// <summary>
        /// Records a purchase amount for today
        /// </summary>
        /// <param name="amountUsd">Purchase amount in USD</param>
        void RecordPurchase(decimal amountUsd);

        /// <summary>
        /// Cleans up old spend records to prevent unlimited data growth
        /// </summary>
        void CleanupOldRecords();

        /// <summary>
        /// Gets the spend amount for a specific date
        /// </summary>
        /// <param name="date">Date to get spend amount for</param>
        /// <returns>Amount spent on the specified date in USD</returns>
        decimal GetSpentOnDate(DateTime date);
    }
}
