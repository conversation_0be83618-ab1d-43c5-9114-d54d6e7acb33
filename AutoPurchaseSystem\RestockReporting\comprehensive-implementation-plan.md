# Restock Item History Logging - Comprehensive Implementation Plan

## Executive Summary

Implement a comprehensive historical logging system that captures complete context for every item processed by restock filters. Store individual JSON files for each item with full keyword state, item data, filter evaluation results, and transaction outcomes. Enable CSV export for manual analysis.

## Requirements & Scope

### Core Requirements
- **Capture Every Item**: Log all items processed when any restock filter is enabled
- **Complete Context**: Store item data, keyword properties, filter rules, and outcomes
- **Historical Accuracy**: Preserve exact state at time of processing
- **Error Tracking**: Log all types of processing errors
- **CSV Export**: Export historical data for manual analysis
- **Performance**: Async processing, continue on errors
- **Storage**: Individual JSON files, daily folders, no cleanup

### Volume Expectations
- **Daily**: ~3,000 items processed
- **Storage**: ~6-15MB per day, ~2-5GB per year
- **Files**: ~1M files per year in daily folder structure

## Architecture Overview

### Components
1. **IItemHistoryLogger** - Service interface for logging
2. **FileItemHistoryLogger** - JSON file implementation
3. **ItemProcessingContext** - Complete context model
4. **ItemHistoryExporter** - CSV export functionality
5. **Integration Points** - Hooks in RestockFilterAction

### Data Flow
```
Item Processing → Context Capture → Async JSON Write → Error Logging
                                ↓
CSV Export ← JSON File Reading ← Daily Folder Structure
```

## Detailed Design

### Core Interfaces

```csharp
namespace uBuyFirst.Restocker.Services
{
    public interface IItemHistoryLogger
    {
        Task LogItemProcessingAsync(ItemProcessingContext context);
    }

    public interface IItemHistoryExporter
    {
        Task<string> ExportToCsvAsync(DateTime startDate, DateTime endDate, string outputPath);
        Task<IEnumerable<ItemProcessingContext>> LoadHistoryAsync(DateTime startDate, DateTime endDate);
    }
}
```

### Data Models

```csharp
namespace uBuyFirst.Restocker.Models
{
    public class ItemProcessingContext
    {
        public DateTime Timestamp { get; set; }
        public string Outcome { get; set; } // "purchased", "filtered_out", "error", "no_action"
        public string Reason { get; set; } // Free text explanation
        public ItemHistoryData ItemData { get; set; }
        public KeywordSnapshot KeywordState { get; set; }
        public FilterRuleContext FilterRule { get; set; }
        public TransactionResult TransactionResult { get; set; }
    }

    public class ItemHistoryData
    {
        public string ItemId { get; set; }
        public string Title { get; set; }
        public decimal CurrentPrice { get; set; }
        public string Condition { get; set; }
        public string Seller { get; set; }
        public decimal ShippingCost { get; set; }
        public string Location { get; set; }
        public DateTime? EndTime { get; set; }
        public decimal? BuyItNowPrice { get; set; }
        public decimal? AuctionPrice { get; set; }
        public int QuantityAvailable { get; set; }
        public string ListingType { get; set; }
        public string ItemUrl { get; set; }
        public string ImageUrl { get; set; }
        // Add other relevant DataList properties
    }

    public class FilterRuleContext
    {
        public string FilterAlias { get; set; }
        public string Expression { get; set; }
        public bool Matched { get; set; }
        public string EvaluationResult { get; set; }
        public DateTime EvaluatedAt { get; set; }
    }

    public class TransactionResult
    {
        public bool Attempted { get; set; }
        public bool Success { get; set; }
        public string TransactionId { get; set; }
        public string ErrorMessage { get; set; }
        public decimal? PurchasePrice { get; set; }
        public int? Quantity { get; set; }
        public DateTime? CompletedAt { get; set; }
    }
}
```

## File System Design

### Directory Structure
```
[ApplicationFolder]/ItemHistory/
├── 2024-12-19/
│   ├── Item_394857392_kw123_143022.json
│   ├── Item_394857392_kw123_143022_1.json  (conflict resolution)
│   ├── Item_394857393_kw124_143025.json
│   └── ...
├── 2024-12-20/
│   └── ...
└── errors/
    ├── 2024-12-19_errors.log
    └── 2024-12-20_errors.log
```

### File Naming Convention
**Pattern**: `Item_{ItemId}_{KeywordId}_{HHMMSS}[_{sequence}].json`
**Examples**:
- `Item_394857392_kw123_143022.json`
- `Item_394857392_kw123_143022_1.json` (if conflict)

### JSON Schema
```json
{
  "timestamp": "2024-12-19T14:30:22.123Z",
  "outcome": "purchased",
  "reason": "Successfully purchased 2 items for $25.99 each",
  "itemData": {
    "itemId": "394857392",
    "title": "iPhone 15 Case Protective Clear",
    "currentPrice": 25.99,
    "condition": "New",
    "seller": "seller123",
    "shippingCost": 5.99,
    "location": "United States",
    "endTime": "2024-12-20T10:00:00Z",
    "buyItNowPrice": 25.99,
    "auctionPrice": null,
    "quantityAvailable": 5,
    "listingType": "FixedPrice",
    "itemUrl": "https://ebay.com/itm/394857392",
    "imageUrl": "https://i.ebayimg.com/..."
  },
  "keywordState": {
    "keywordId": "kw123",
    "alias": "iPhone Cases",
    "keywords": "iphone,case,protective",
    "requiredQuantity": 10,
    "purchasedQuantity": 6,
    "priceMin": 15.00,
    "priceMax": 40.00,
    "condition": ["New", "Used"],
    "sellers": ["seller1", "seller2"],
    "sellerType": "Include",
    "capturedAt": "2024-12-19T14:30:22.123Z"
  },
  "filterRule": {
    "filterAlias": "Standard Restock Filter",
    "expression": "Price <= 40 AND Condition IN ['New', 'Used']",
    "matched": true,
    "evaluationResult": "All conditions met - proceeding with purchase",
    "evaluatedAt": "2024-12-19T14:30:22.120Z"
  },
  "transactionResult": {
    "attempted": true,
    "success": true,
    "transactionId": "txn_123456",
    "errorMessage": null,
    "purchasePrice": 25.99,
    "quantity": 2,
    "completedAt": "2024-12-19T14:30:45.567Z"
  }
}
```

## Integration Strategy

### Primary Integration Point: RestockFilterAction.ExecuteAsync

```csharp
public class RestockFilterAction : FilterAction
{
    private readonly IItemHistoryLogger _itemHistoryLogger;

    public RestockFilterAction(IItemHistoryLogger itemHistoryLogger)
    {
        _itemHistoryLogger = itemHistoryLogger;
    }

    public override async Task<bool> ExecuteAsync(DataList datalist, XFilterClass filter)
    {
        var context = new ItemProcessingContext();

        try
        {
            // Extract item data from datalist
            context.ItemData = ExtractItemData(datalist);
            context.Timestamp = DateTime.UtcNow;

            // Get keyword information
            context.KeywordState = await GetKeywordSnapshot(datalist);

            // Capture filter rule information
            context.FilterRule = new FilterRuleContext
            {
                FilterAlias = filter.Alias,
                Expression = filter.Expression,
                EvaluatedAt = DateTime.UtcNow
            };

            // Execute existing filter logic
            bool filterMatched = await base.ExecuteAsync(datalist, filter);

            context.FilterRule.Matched = filterMatched;
            context.FilterRule.EvaluationResult = filterMatched ?
                "Filter matched - proceeding with restock action" :
                "Filter did not match - no action taken";

            if (filterMatched)
            {
                // Attempt purchase
                var transactionResult = await AttemptPurchase(datalist, context.KeywordState);
                context.TransactionResult = transactionResult;
                context.Outcome = transactionResult.Success ? "purchased" : "purchase_failed";
                context.Reason = transactionResult.Success ?
                    $"Successfully purchased {transactionResult.Quantity} items" :
                    $"Purchase failed: {transactionResult.ErrorMessage}";
            }
            else
            {
                context.Outcome = "filtered_out";
                context.Reason = "Item did not match restock filter criteria";
                context.TransactionResult = new TransactionResult { Attempted = false };
            }

            return filterMatched;
        }
        catch (Exception ex)
        {
            context.Outcome = "error";
            context.Reason = $"Processing error: {ex.Message}";
            context.TransactionResult = new TransactionResult
            {
                Attempted = true,
                Success = false,
                ErrorMessage = ex.Message
            };

            throw; // Re-throw to maintain existing error handling
        }
        finally
        {
            // Always log the context, regardless of outcome
            await _itemHistoryLogger.LogItemProcessingAsync(context);
        }
    }

    private ItemHistoryData ExtractItemData(DataList datalist)
    {
        return new ItemHistoryData
        {
            ItemId = datalist["ItemID"]?.ToString(),
            Title = datalist["Title"]?.ToString(),
            CurrentPrice = decimal.TryParse(datalist["CurrentPrice"]?.ToString(), out var price) ? price : 0,
            Condition = datalist["Condition"]?.ToString(),
            Seller = datalist["Seller"]?.ToString(),
            ShippingCost = decimal.TryParse(datalist["ShippingCost"]?.ToString(), out var shipping) ? shipping : 0,
            Location = datalist["Location"]?.ToString(),
            EndTime = DateTime.TryParse(datalist["EndTime"]?.ToString(), out var endTime) ? endTime : null,
            BuyItNowPrice = decimal.TryParse(datalist["BuyItNowPrice"]?.ToString(), out var binPrice) ? binPrice : null,
            QuantityAvailable = int.TryParse(datalist["Quantity"]?.ToString(), out var qty) ? qty : 0,
            ListingType = datalist["ListingType"]?.ToString(),
            ItemUrl = datalist["ViewItemURL"]?.ToString(),
            ImageUrl = datalist["GalleryURL"]?.ToString()
            // Add other relevant DataList properties as needed
        };
    }

    private async Task<KeywordSnapshot> GetKeywordSnapshot(DataList datalist)
    {
        // TODO: Research how to access Keyword2Find from datalist context
        // Possible approaches:
        // 1. datalist["KeywordId"] or similar property
        // 2. Thread-local storage of current keyword
        // 3. Pass keyword through filter context
        // 4. Look up by search criteria

        // Placeholder implementation:
        var keywordId = datalist["KeywordId"]?.ToString();
        if (!string.IsNullOrEmpty(keywordId))
        {
            var keyword = await FindKeywordById(keywordId);
            return KeywordSnapshot.FromKeyword2Find(keyword);
        }

        return null;
    }
}
```

### Dependency Injection Setup

```csharp
// In startup/configuration
services.AddSingleton<IItemHistoryLogger, FileItemHistoryLogger>();
services.AddTransient<IItemHistoryExporter, ItemHistoryExporter>();

// Configure file paths
services.Configure<ItemHistoryOptions>(options =>
{
    options.BasePath = Path.Combine(Application.StartupPath, "ItemHistory");
    options.ErrorLogPath = Path.Combine(Application.StartupPath, "ItemHistory", "errors");
});
```

## Error Handling Strategy

### Error Categories
1. **JSON Write Failures** - Disk full, permissions, file locks
2. **Directory Creation Failures** - Permissions, path issues
3. **Filter Evaluation Errors** - Exception in filter logic
4. **Purchase Attempt Failures** - Network, eBay API, payment issues
5. **System Exceptions** - Unexpected errors during processing

### Error Handling Implementation

```csharp
public class FileItemHistoryLogger : IItemHistoryLogger
{
    private readonly ILogger _errorLogger;
    private readonly string _basePath;
    private readonly string _errorLogPath;

    public async Task LogItemProcessingAsync(ItemProcessingContext context)
    {
        try
        {
            var filePath = GenerateFilePath(context);
            var json = JsonConvert.SerializeObject(context, Formatting.Indented);

            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));

            // Handle file name conflicts
            filePath = ResolveFileNameConflict(filePath);

            // Write asynchronously
            await File.WriteAllTextAsync(filePath, json);
        }
        catch (Exception ex)
        {
            // Log error but don't throw - continue processing other items
            await LogError($"Failed to write item history for {context.ItemData?.ItemId}: {ex.Message}", ex);
        }
    }

    private async Task LogError(string message, Exception ex)
    {
        try
        {
            var errorLogFile = Path.Combine(_errorLogPath, $"{DateTime.Now:yyyy-MM-dd}_errors.log");
            var errorEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {message}\n{ex}\n\n";

            Directory.CreateDirectory(_errorLogPath);
            await File.AppendAllTextAsync(errorLogFile, errorEntry);
        }
        catch
        {
            // If we can't even log the error, just ignore it
            // Don't let error logging break the main processing
        }
    }

    private string ResolveFileNameConflict(string originalPath)
    {
        if (!File.Exists(originalPath))
            return originalPath;

        var directory = Path.GetDirectoryName(originalPath);
        var fileNameWithoutExt = Path.GetFileNameWithoutExtension(originalPath);
        var extension = Path.GetExtension(originalPath);

        int counter = 1;
        string newPath;

        do
        {
            newPath = Path.Combine(directory, $"{fileNameWithoutExt}_{counter}{extension}");
            counter++;
        }
        while (File.Exists(newPath) && counter < 1000); // Prevent infinite loop

        return newPath;
    }
}
```

### Error Log Format
```
[2024-12-19 14:30:22] Failed to write item history for 394857392: Access to the path 'ItemHistory\2024-12-19\Item_394857392_kw123_143022.json' is denied.
System.UnauthorizedAccessException: Access to the path 'ItemHistory\2024-12-19\Item_394857392_kw123_143022.json' is denied.
   at System.IO.FileStream.ValidateFileHandle(SafeFileHandle fileHandle)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)

[2024-12-19 14:31:15] Failed to write item history for 394857393: The process cannot access the file 'ItemHistory\2024-12-19\Item_394857393_kw124_143115.json' because it is being used by another process.
System.IO.IOException: The process cannot access the file 'ItemHistory\2024-12-19\Item_394857393_kw124_143115.json' because it is being used by another process.

## CSV Export Implementation

### Export Service Interface

```csharp
public class ItemHistoryExporter : IItemHistoryExporter
{
    private readonly string _basePath;

    public async Task<string> ExportToCsvAsync(DateTime startDate, DateTime endDate, string outputPath)
    {
        var items = await LoadHistoryAsync(startDate, endDate);
        var csv = GenerateCsv(items);

        await File.WriteAllTextAsync(outputPath, csv);
        return outputPath;
    }

    public async Task<IEnumerable<ItemProcessingContext>> LoadHistoryAsync(DateTime startDate, DateTime endDate)
    {
        var items = new List<ItemProcessingContext>();

        for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
        {
            var dayFolder = Path.Combine(_basePath, date.ToString("yyyy-MM-dd"));
            if (!Directory.Exists(dayFolder)) continue;

            var jsonFiles = Directory.GetFiles(dayFolder, "*.json");

            foreach (var file in jsonFiles)
            {
                try
                {
                    var json = await File.ReadAllTextAsync(file);
                    var item = JsonConvert.DeserializeObject<ItemProcessingContext>(json);
                    if (item != null && item.Timestamp >= startDate && item.Timestamp <= endDate)
                    {
                        items.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    // Log corrupted file but continue processing
                    Console.WriteLine($"Failed to read {file}: {ex.Message}");
                }
            }
        }

        return items.OrderBy(x => x.Timestamp);
    }

    private string GenerateCsv(IEnumerable<ItemProcessingContext> items)
    {
        var csv = new StringBuilder();

        // CSV Headers - All available properties flattened
        csv.AppendLine(string.Join(",", new[]
        {
            "Timestamp", "Outcome", "Reason",
            "ItemId", "Title", "CurrentPrice", "Condition", "Seller", "ShippingCost",
            "Location", "EndTime", "BuyItNowPrice", "AuctionPrice", "QuantityAvailable",
            "ListingType", "ItemUrl", "ImageUrl",
            "KeywordId", "Alias", "Keywords", "RequiredQuantity", "PurchasedQuantity",
            "PriceMin", "PriceMax", "Condition_Filter", "Sellers", "SellerType",
            "FilterAlias", "Expression", "Matched", "EvaluationResult", "EvaluatedAt",
            "TransactionAttempted", "TransactionSuccess", "TransactionId", "TransactionError",
            "PurchasePrice", "PurchaseQuantity", "CompletedAt"
        }));

        // CSV Data Rows
        foreach (var item in items)
        {
            csv.AppendLine(string.Join(",", new[]
            {
                EscapeCsv(item.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")),
                EscapeCsv(item.Outcome),
                EscapeCsv(item.Reason),
                EscapeCsv(item.ItemData?.ItemId),
                EscapeCsv(item.ItemData?.Title),
                item.ItemData?.CurrentPrice?.ToString() ?? "",
                EscapeCsv(item.ItemData?.Condition),
                EscapeCsv(item.ItemData?.Seller),
                item.ItemData?.ShippingCost?.ToString() ?? "",
                EscapeCsv(item.ItemData?.Location),
                item.ItemData?.EndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                item.ItemData?.BuyItNowPrice?.ToString() ?? "",
                item.ItemData?.AuctionPrice?.ToString() ?? "",
                item.ItemData?.QuantityAvailable?.ToString() ?? "",
                EscapeCsv(item.ItemData?.ListingType),
                EscapeCsv(item.ItemData?.ItemUrl),
                EscapeCsv(item.ItemData?.ImageUrl),
                EscapeCsv(item.KeywordState?.KeywordId),
                EscapeCsv(item.KeywordState?.Alias),
                EscapeCsv(item.KeywordState?.Keywords),
                item.KeywordState?.RequiredQuantity?.ToString() ?? "",
                item.KeywordState?.PurchasedQuantity?.ToString() ?? "",
                item.KeywordState?.PriceMin?.ToString() ?? "",
                item.KeywordState?.PriceMax?.ToString() ?? "",
                EscapeCsv(string.Join(";", item.KeywordState?.Condition ?? new string[0])),
                EscapeCsv(string.Join(";", item.KeywordState?.Sellers ?? new string[0])),
                EscapeCsv(item.KeywordState?.SellerType),
                EscapeCsv(item.FilterRule?.FilterAlias),
                EscapeCsv(item.FilterRule?.Expression),
                item.FilterRule?.Matched?.ToString() ?? "",
                EscapeCsv(item.FilterRule?.EvaluationResult),
                item.FilterRule?.EvaluatedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? "",
                item.TransactionResult?.Attempted?.ToString() ?? "",
                item.TransactionResult?.Success?.ToString() ?? "",
                EscapeCsv(item.TransactionResult?.TransactionId),
                EscapeCsv(item.TransactionResult?.ErrorMessage),
                item.TransactionResult?.PurchasePrice?.ToString() ?? "",
                item.TransactionResult?.Quantity?.ToString() ?? "",
                item.TransactionResult?.CompletedAt?.ToString("yyyy-MM-dd HH:mm:ss") ?? ""
            }));
        }

        return csv.ToString();
    }

    private string EscapeCsv(string value)
    {
        if (string.IsNullOrEmpty(value)) return "";

        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        if (value.Contains(",") || value.Contains("\"") || value.Contains("\n") || value.Contains("\r"))
        {
            return "\"" + value.Replace("\"", "\"\"") + "\"";
        }

        return value;
    }
}
```

## Implementation Steps

### Phase 1: Core Infrastructure (Week 1)
1. **Create Models** - ItemProcessingContext, ItemHistoryData, FilterRuleContext, TransactionResult
2. **Create Interfaces** - IItemHistoryLogger, IItemHistoryExporter
3. **Create Base Implementation** - FileItemHistoryLogger with basic JSON writing
4. **Setup Dependency Injection** - Configure services and options
5. **Create Error Logging** - Basic error file logging functionality

### Phase 2: Integration (Week 2)
6. **Research Keyword2Find Access** - Determine how to get keyword from datalist context
7. **Modify RestockFilterAction** - Add logging calls to ExecuteAsync method
8. **Implement Data Extraction** - ExtractItemData method from datalist properties
9. **Test Basic Logging** - Verify JSON files are created correctly
10. **Handle File Conflicts** - Implement file name conflict resolution

### Phase 3: Transaction Integration (Week 3)
11. **Integrate Purchase Results** - Capture transaction outcomes
12. **Error Scenario Handling** - Test all error conditions
13. **Performance Testing** - Verify async processing doesn't impact performance
14. **Volume Testing** - Test with 3000+ items per day

### Phase 4: Export Functionality (Week 4)
15. **Implement CSV Export** - ItemHistoryExporter with full CSV generation
16. **Create Export UI** - Add export functionality to existing forms
17. **Test Export Performance** - Verify large date range exports work
18. **Documentation** - Create user documentation for export features

### Phase 5: Production Deployment (Week 5)
19. **Configuration Management** - Setup production file paths
20. **Monitoring** - Add logging for system health
21. **Backup Strategy** - Document backup procedures for JSON files
22. **User Training** - Train users on new export functionality

## Testing Strategy

### Unit Tests
- **ItemProcessingContext Serialization** - JSON round-trip tests
- **File Name Conflict Resolution** - Test collision handling
- **CSV Export** - Test all data types and edge cases
- **Error Handling** - Test all error scenarios

### Integration Tests
- **RestockFilterAction Integration** - Test logging during actual filter execution
- **File System Operations** - Test directory creation, file writing
- **Large Volume Testing** - Test with 3000+ items
- **Concurrent Access** - Test multiple threads writing simultaneously

### Performance Tests
- **Async Write Performance** - Measure impact on filter processing
- **CSV Export Performance** - Test large date range exports
- **Memory Usage** - Monitor memory consumption during processing
- **Disk I/O Impact** - Measure impact on system performance

## Configuration & Deployment

### Configuration Options
```csharp
public class ItemHistoryOptions
{
    public string BasePath { get; set; } = "ItemHistory";
    public string ErrorLogPath { get; set; } = "ItemHistory/errors";
    public bool EnableLogging { get; set; } = true;
    public int MaxRetries { get; set; } = 0;
    public bool CreateDailyFolders { get; set; } = true;
}
```

### Deployment Checklist
- [ ] Create ItemHistory folder structure
- [ ] Set appropriate file permissions
- [ ] Configure dependency injection
- [ ] Test error logging functionality
- [ ] Verify CSV export works
- [ ] Monitor disk space usage
- [ ] Setup backup procedures

## Performance Impact Assessment

### Expected Impact
- **CPU**: Minimal - async JSON serialization
- **Memory**: Low - single item context objects
- **Disk I/O**: Moderate - 3000 small files per day
- **Network**: None - local file operations only

### Mitigation Strategies
- **Async Processing** - Don't block filter execution
- **Error Tolerance** - Continue processing on write failures
- **Batch Operations** - Consider batching for very high volumes
- **Monitoring** - Track performance metrics

## Future Enhancements

### Potential Improvements
1. **Database Integration** - Optional database storage for faster queries
2. **Search Functionality** - Search historical data by criteria
3. **Analytics Dashboard** - Visual analysis of historical data
4. **Automated Reports** - Scheduled CSV exports
5. **Data Compression** - Compress old JSON files
6. **Cloud Storage** - Optional cloud backup of historical data

### Extensibility Points
- **IItemHistoryLogger** - Can add database, cloud, or other storage implementations
- **IItemHistoryExporter** - Can add Excel, PDF, or other export formats
- **ItemProcessingContext** - Can add additional context properties
- **Error Handling** - Can add email notifications, monitoring integration

## Conclusion

This implementation provides comprehensive historical logging of all restock filter processing with minimal performance impact. The individual JSON file approach offers maximum flexibility and reliability while supporting the required CSV export functionality for manual analysis.

The modular design allows for future enhancements while maintaining backward compatibility and system stability.
```
