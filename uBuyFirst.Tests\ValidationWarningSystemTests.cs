using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using uBuyFirst;
using uBuyFirst.UI.Validation;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class ValidationWarningSystemTests
    {
        private TreeListValidationWarningManager _warningManager;

        [TestInitialize]
        public void TestInitialize()
        {
            _warningManager = new TreeListValidationWarningManager();
        }

        [TestMethod]
        public void SetWarning_ShouldStoreWarningMessage()
        {
            // Arrange
            var nodeId = "test_node_1";
            var columnName = "Alias";
            var warningMessage = "Test warning message";

            // Act
            _warningManager.SetWarning(nodeId, columnName, warningMessage);

            // Assert
            Assert.IsTrue(_warningManager.HasWarning(nodeId, columnName));
            Assert.AreEqual(warningMessage, _warningManager.GetWarning(nodeId, columnName));
        }

        [TestMethod]
        public void ClearWarning_ShouldRemoveSpecificWarning()
        {
            // Arrange
            var nodeId = "test_node_1";
            var columnName1 = "Alias";
            var columnName2 = "Keywords";
            _warningManager.SetWarning(nodeId, columnName1, "Warning 1");
            _warningManager.SetWarning(nodeId, columnName2, "Warning 2");

            // Act
            _warningManager.ClearWarning(nodeId, columnName1);

            // Assert
            Assert.IsFalse(_warningManager.HasWarning(nodeId, columnName1));
            Assert.IsTrue(_warningManager.HasWarning(nodeId, columnName2));
        }

        [TestMethod]
        public void ClearNodeWarnings_ShouldRemoveAllWarningsForNode()
        {
            // Arrange
            var nodeId = "test_node_1";
            _warningManager.SetWarning(nodeId, "Alias", "Warning 1");
            _warningManager.SetWarning(nodeId, "Keywords", "Warning 2");

            // Act
            _warningManager.ClearNodeWarnings(nodeId);

            // Assert
            Assert.IsFalse(_warningManager.HasWarning(nodeId, "Alias"));
            Assert.IsFalse(_warningManager.HasWarning(nodeId, "Keywords"));
        }

        [TestMethod]
        public void HasAnyWarnings_ShouldReturnTrueWhenWarningsExist()
        {
            // Arrange
            Assert.IsFalse(_warningManager.HasAnyWarnings());

            // Act
            _warningManager.SetWarning("node1", "column1", "warning");

            // Assert
            Assert.IsTrue(_warningManager.HasAnyWarnings());
        }

        [TestMethod]
        public void GetWarningCount_ShouldReturnCorrectCount()
        {
            // Arrange
            _warningManager.SetWarning("node1", "column1", "warning1");
            _warningManager.SetWarning("node1", "column2", "warning2");
            _warningManager.SetWarning("node2", "column1", "warning3");

            // Act
            var count = _warningManager.GetWarningCount();

            // Assert
            Assert.AreEqual(3, count);
        }

        [TestMethod]
        public void GetNodeWarnings_ShouldReturnAllWarningsForNode()
        {
            // Arrange
            var nodeId = "test_node_1";
            _warningManager.SetWarning(nodeId, "Alias", "Alias warning");
            _warningManager.SetWarning(nodeId, "Keywords", "Keywords warning");

            // Act
            var warnings = _warningManager.GetNodeWarnings(nodeId);

            // Assert
            Assert.AreEqual(2, warnings.Count);
            Assert.AreEqual("Alias warning", warnings["Alias"]);
            Assert.AreEqual("Keywords warning", warnings["Keywords"]);
        }

        [TestMethod]
        public void GetWarning_WithNonExistentWarning_ShouldReturnNull()
        {
            // Act
            var warning = _warningManager.GetWarning("nonexistent", "column");

            // Assert
            Assert.IsNull(warning);
        }

        [TestMethod]
        public void ClearAllWarnings_ShouldRemoveAllWarnings()
        {
            // Arrange
            _warningManager.SetWarning("node1", "column1", "warning1");
            _warningManager.SetWarning("node2", "column2", "warning2");

            // Act
            _warningManager.ClearAllWarnings();

            // Assert
            Assert.IsFalse(_warningManager.HasAnyWarnings());
            Assert.AreEqual(0, _warningManager.GetWarningCount());
        }

        [TestMethod]
        public void ValidationWarningSystem_ShouldSupportMultipleWarningsPerNode()
        {
            // Arrange
            var nodeId = "test_keyword_1";

            // Act
            _warningManager.SetWarning(nodeId, "Alias", "Alias cannot be empty");
            _warningManager.SetWarning(nodeId, "Keywords", "Keywords too short");
            _warningManager.SetWarning(nodeId, "Price Min", "Price out of range");

            // Assert
            Assert.AreEqual(3, _warningManager.GetWarningCount());
            Assert.IsTrue(_warningManager.HasWarning(nodeId, "Alias"));
            Assert.IsTrue(_warningManager.HasWarning(nodeId, "Keywords"));
            Assert.IsTrue(_warningManager.HasWarning(nodeId, "Price Min"));

            var nodeWarnings = _warningManager.GetNodeWarnings(nodeId);
            Assert.AreEqual(3, nodeWarnings.Count);
            Assert.AreEqual("Alias cannot be empty", nodeWarnings["Alias"]);
            Assert.AreEqual("Keywords too short", nodeWarnings["Keywords"]);
            Assert.AreEqual("Price out of range", nodeWarnings["Price Min"]);
        }

        [TestMethod]
        public void ValidationWarningSystem_ShouldHandleComplexScenarios()
        {
            // Arrange - Simulate multiple keywords with various warning combinations
            _warningManager.SetWarning("keyword_1", "Alias", "Empty alias");
            _warningManager.SetWarning("keyword_1", "Keywords", "Invalid syntax");
            _warningManager.SetWarning("keyword_2", "Price Min", "Price too low");
            _warningManager.SetWarning("folder_1", "Alias", "Duplicate folder name");
            _warningManager.SetWarning("childterm_1", "Keywords", "Empty keywords");

            // Act & Assert
            Assert.AreEqual(5, _warningManager.GetWarningCount());
            Assert.IsTrue(_warningManager.HasAnyWarnings());

            // Verify specific warnings exist
            Assert.IsTrue(_warningManager.HasWarning("keyword_1", "Alias"));
            Assert.IsTrue(_warningManager.HasWarning("keyword_1", "Keywords"));
            Assert.IsTrue(_warningManager.HasWarning("keyword_2", "Price Min"));
            Assert.IsTrue(_warningManager.HasWarning("folder_1", "Alias"));
            Assert.IsTrue(_warningManager.HasWarning("childterm_1", "Keywords"));

            // Verify non-existent warnings
            Assert.IsFalse(_warningManager.HasWarning("keyword_1", "Price Max"));
            Assert.IsFalse(_warningManager.HasWarning("nonexistent", "Alias"));
        }
    }
}
