using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Pricing;
using uBuyFirst.Prefs;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    [TestCategory("Restocker")]
    [TestCategory("DailySpend")]
    public class PurchaseExecutionServiceDailySpendTests
    {
        private PurchaseExecutionService _service;
        private MockDailySpendService _mockDailySpendService;
        private DataList _testDataList;
        private Keyword2Find _testKeyword;

        [TestInitialize]
        public void Setup()
        {
            // Reset UserSettings for clean test state
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory.Clear();

            _mockDailySpendService = new MockDailySpendService();
            _service = new PurchaseExecutionService(_mockDailySpendService);

            // Create test DataList with pricing
            _testDataList = new DataList
            {
                ItemID = "123456789",
                Title = "Test Item for Daily Spend",
                QuantityAvailable = 10,
                ItemPricing = new ItemPricing
                {
                    ItemPrice = new CurrencyAmount(50.00, "USD")
                }
            };

            // Create test Keyword2Find
            _testKeyword = new Keyword2Find
            {
                Id = "test-keyword-daily-spend",
                Alias = "Test Daily Spend Keyword",
                JobId = "JOB-DAILY-001",
                RequiredQuantity = 3,
                PurchasedQuantity = 0
            };
        }

        [TestCleanup]
        public void Cleanup()
        {
            _service?.Dispose();
            UserSettings.DailySpendHistory.Clear();
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_WithinDailyLimit_ShouldProceedWithPurchase()
        {
            // Arrange
            _mockDailySpendService.SetTodaySpent(100m);
            _mockDailySpendService.SetDailyLimit(1000m);
            _mockDailySpendService.SetCanPurchase(true);

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsNotNull(result);
            // Note: Since we don't have actual checkout infrastructure in tests,
            // we expect this to fail at the checkout stage, not at the daily limit check
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            Assert.AreEqual(50m, _mockDailySpendService.LastAmountChecked);
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_ExceedsDailyLimit_ShouldReturnDailyLimitExceeded()
        {
            // Arrange
            _mockDailySpendService.SetTodaySpent(980m);
            _mockDailySpendService.SetDailyLimit(1000m);
            _mockDailySpendService.SetCanPurchase(false); // Limit exceeded

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsTrue(result.IsDailyLimitExceeded);
            Assert.IsTrue(result.Message.Contains("Daily spend limit exceeded"));
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            Assert.AreEqual(50m, _mockDailySpendService.LastAmountChecked);
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_MultipleQuantity_ShouldCalculateCorrectAmount()
        {
            // Arrange
            _testKeyword.RequiredQuantity = 5;
            _testKeyword.PurchasedQuantity = 2; // Need 3 more
            _mockDailySpendService.SetCanPurchase(true);

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            // Should calculate for 3 items: 3 * $50 = $150
            Assert.AreEqual(150m, _mockDailySpendService.LastAmountChecked);
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_CurrencyConversion_ShouldConvertToUSD()
        {
            // Arrange
            _testDataList.ItemPricing.ItemPrice = new CurrencyAmount(40.00, "GBP");
            _mockDailySpendService.SetCanPurchase(true);

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            // Should convert GBP to USD (exact amount depends on CurrencyConverter implementation)
            Assert.IsTrue(_mockDailySpendService.LastAmountChecked > 0);
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_NullItemPricing_ShouldHandleGracefully()
        {
            // Arrange
            _testDataList.ItemPricing = null;
            _mockDailySpendService.SetCanPurchase(true);

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            Assert.AreEqual(0m, _mockDailySpendService.LastAmountChecked);
        }

        [TestMethod]
        public async Task TryPurchaseItemAsync_ZeroPriceItem_ShouldAllowPurchase()
        {
            // Arrange
            _testDataList.ItemPricing.ItemPrice = new CurrencyAmount(0.00, "USD");
            _mockDailySpendService.SetCanPurchase(true);

            // Act
            var result = await _service.TryPurchaseItemAsync(_testDataList, _testKeyword, "test-filter");

            // Assert
            Assert.IsTrue(_mockDailySpendService.CanPurchaseWasCalled);
            Assert.AreEqual(0m, _mockDailySpendService.LastAmountChecked);
        }
    }

    /// <summary>
    /// Mock implementation of IDailySpendService for testing
    /// </summary>
    public class MockDailySpendService : IDailySpendService
    {
        private decimal _todaySpent;
        private decimal _dailyLimit = 1000m;
        private bool _canPurchase = true;

        public bool CanPurchaseWasCalled { get; private set; }
        public decimal LastAmountChecked { get; private set; }
        public bool RecordPurchaseWasCalled { get; private set; }
        public decimal LastAmountRecorded { get; private set; }

        public void SetTodaySpent(decimal amount) => _todaySpent = amount;
        public void SetDailyLimit(decimal limit) => _dailyLimit = limit;
        public void SetCanPurchase(bool canPurchase) => _canPurchase = canPurchase;

        public decimal GetTodaySpent() => _todaySpent;
        public decimal GetDailyLimit() => _dailyLimit;

        public bool CanPurchase(decimal amount)
        {
            CanPurchaseWasCalled = true;
            LastAmountChecked = amount;
            return _canPurchase;
        }

        public void RecordPurchase(decimal amount)
        {
            RecordPurchaseWasCalled = true;
            LastAmountRecorded = amount;
            _todaySpent += amount;
        }

        public decimal GetSpentOnDate(DateTime date)
        {
            // For testing, return today's spent if date is today, otherwise 0
            return date.Date == DateTime.Today ? _todaySpent : 0m;
        }

        public decimal GetRemainingBudget()
        {
            return Math.Max(0, _dailyLimit - _todaySpent);
        }

        public void CleanupOldRecords()
        {
            // Mock implementation - nothing to cleanup
        }

        public void ClearTodaySpent()
        {
            _todaySpent = 0;
        }

        public void Dispose()
        {
            // Nothing to dispose in mock
        }
    }
}
