using System.Collections.Generic;
using System.Linq;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList;
using uBuyFirst.Search;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Coordinates validation operations for TreeList nodes
    /// </summary>
    public class TreeListValidationCoordinator
    {
        private readonly TreeListValidationWarningManager _warningManager;
        private readonly ValidationServiceFactory _validatorFactory;

        /// <summary>
        /// Initializes a new instance of TreeListValidationCoordinator
        /// </summary>
        /// <param name="warningManager">The warning manager to use for storing validation results</param>
        public TreeListValidationCoordinator(TreeListValidationWarningManager warningManager = null)
        {
            _warningManager = warningManager ?? new TreeListValidationWarningManager();
            _validatorFactory = ValidationServiceFactory.Instance;
        }

        /// <summary>
        /// Gets the warning manager used by this coordinator
        /// </summary>
        public TreeListValidationWarningManager WarningManager => _warningManager;

        /// <summary>
        /// Validates a TreeList node and updates the warning system
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="node">The node to validate</param>
        /// <param name="queryList">The QueryList for context (optional)</param>
        public void ValidateNode(TreeList treeList, TreeListNode node, QueryList queryList = null)
        {
            if (node == null || treeList == null)
                return;

            var dataRecord = treeList.GetDataRecordByNode(node);
            if (dataRecord == null)
                return;

            var nodeId = NodeIdentifierService.GetNodeId(treeList, node);
            
            // Clear existing warnings for this node
            _warningManager.ClearNodeWarnings(nodeId);

            // Create validation context
            var context = CreateValidationContext(nodeId, dataRecord, queryList);

            // Get appropriate validator
            var validator = _validatorFactory.GetValidator(dataRecord);
            if (validator == null)
                return;

            // Perform validation
            var validationResult = validator.Validate(dataRecord, context);

            // Store validation results in warning manager
            _warningManager.SetWarningsFromResult(nodeId, validationResult);
        }

        /// <summary>
        /// Validates multiple nodes in batch
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="nodes">The nodes to validate</param>
        /// <param name="queryList">The QueryList for context (optional)</param>
        public void ValidateNodes(TreeList treeList, IEnumerable<TreeListNode> nodes, QueryList queryList = null)
        {
            if (nodes == null)
                return;

            foreach (var node in nodes)
            {
                ValidateNode(treeList, node, queryList);
            }
        }

        /// <summary>
        /// Validates all nodes in a TreeList
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="queryList">The QueryList for context (optional)</param>
        public void ValidateAllNodes(TreeList treeList, QueryList queryList = null)
        {
            if (treeList == null)
                return;

            var allNodes = GetAllNodes(treeList);
            ValidateNodes(treeList, allNodes, queryList);
        }

        /// <summary>
        /// Checks if a node has validation warnings
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="node">The node to check</param>
        /// <param name="columnName">Optional column name to check specifically</param>
        /// <returns>True if the node has warnings</returns>
        public bool HasWarnings(TreeList treeList, TreeListNode node, string columnName = null)
        {
            if (node == null || treeList == null)
                return false;

            var nodeId = NodeIdentifierService.GetNodeId(treeList, node);
            
            if (string.IsNullOrEmpty(columnName))
            {
                return _warningManager.GetNodeWarnings(nodeId).Any();
            }
            else
            {
                return _warningManager.HasWarning(nodeId, columnName);
            }
        }

        /// <summary>
        /// Gets validation warnings for a specific node and column
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="node">The node to get warnings for</param>
        /// <param name="columnName">The column name</param>
        /// <returns>Warning message or null if no warning exists</returns>
        public string GetWarning(TreeList treeList, TreeListNode node, string columnName)
        {
            if (node == null || treeList == null || string.IsNullOrEmpty(columnName))
                return null;

            var nodeId = NodeIdentifierService.GetNodeId(treeList, node);
            return _warningManager.GetWarning(nodeId, columnName);
        }

        /// <summary>
        /// Clears all validation warnings
        /// </summary>
        public void ClearAllWarnings()
        {
            _warningManager.ClearAllWarnings();
        }

        /// <summary>
        /// Creates a validation context for the specified data record
        /// </summary>
        private ValidationContext CreateValidationContext(string nodeId, object dataRecord, QueryList queryList)
        {
            var context = ValidationContext.ForNode(nodeId);

            // Add QueryList to context if available
            if (queryList != null)
            {
                context.SetProperty("QueryList", queryList);

                // Add existing aliases for uniqueness checking
                var existingAliases = Tools.Helpers.CountStrings(queryList.ChildrenCore.Select(k => k.Alias).ToList());
                context.SetProperty("ExistingAliases", existingAliases);
            }

            return context;
        }

        /// <summary>
        /// Gets all nodes from a TreeList recursively
        /// </summary>
        private IEnumerable<TreeListNode> GetAllNodes(TreeList treeList)
        {
            var nodes = new List<TreeListNode>();
            
            foreach (TreeListNode node in treeList.Nodes)
            {
                nodes.Add(node);
                nodes.AddRange(GetChildNodes(node));
            }

            return nodes;
        }

        /// <summary>
        /// Gets all child nodes recursively
        /// </summary>
        private IEnumerable<TreeListNode> GetChildNodes(TreeListNode parentNode)
        {
            var nodes = new List<TreeListNode>();
            
            foreach (TreeListNode childNode in parentNode.Nodes)
            {
                nodes.Add(childNode);
                nodes.AddRange(GetChildNodes(childNode));
            }

            return nodes;
        }
    }
}
