﻿using System;
using System.Diagnostics;
using System.IO;
using uBuyFirst.Data;
using uBuyFirst.Stats;
using uBuyFirst.Tools;

namespace uBuyFirst.GUI
{
    internal static class Browser
    {
        public static void OpenAffiliateLink(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.<PERSON>rowser<PERSON>ath, d.GetAffiliateLink());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }
        public static void OpenAffiliateLinkVia_uBuyFirstRedirect(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetAffiliateLinkFor_uBuyFirstRedirect());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }

        public static void OpenCheckoutLink(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.<PERSON><PERSON>er<PERSON><PERSON>, d.GetCheckoutLink());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }

        public static void LaunchBrowser(string browserPath, string url)
        {
            if (!string.IsNullOrEmpty(browserPath) && File.Exists(browserPath))
                Process.Start(browserPath, url);
            else
                Process.Start(url);
        }

        /// <summary>
        /// Launches Firefox specifically for captcha and logout scenarios.
        /// Falls back to the standard LaunchBrowser method if Firefox launching fails.
        /// </summary>
        /// <param name="url">The URL to open in Firefox</param>
        public static void LaunchFirefox(string url)
        {
            try
            {
                var psi = new ProcessStartInfo
                {
                    FileName = "firefox",   // executable
                    Arguments = url,        // first command-line argument
                    UseShellExecute = true  // lets Windows resolve App Paths / PATH
                };

                Process.Start(psi);
                System.Diagnostics.Debug.WriteLine($"Successfully launched Firefox with URL: {url}");
            }
            catch (Exception ex)
            {
                // Firefox launch failed, fall back to standard browser launching
                System.Diagnostics.Debug.WriteLine($"Firefox launch failed: {ex.Message}. Falling back to default browser.");
                LaunchBrowser(null, url);
            }
        }
    }
}
