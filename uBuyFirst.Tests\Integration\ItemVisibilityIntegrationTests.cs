using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.Integration
{
    /// <summary>
    /// Integration tests to verify that item visibility logic works correctly
    /// with the actual data model relationships
    /// </summary>
    [TestClass]
    public class ItemVisibilityIntegrationTests
    {
        private QueryList _queryList;

        [TestInitialize]
        public void Setup()
        {
            _queryList = new QueryList();
        }

        [TestMethod]
        public void KeywordCreation_InCollapsedFolder_ShouldHaveCorrectParentRelationship()
        {
            // Arrange - Create a folder structure
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root Folder",
                IsExpanded = false // Collapsed folder
            };
            _queryList.Folders.Add(rootFolder);

            // Act - Create a keyword in the collapsed folder
            var keyword = new Keyword2Find 
            { 
                Alias = "Test Keyword",
                ParentFolder = rootFolder
            };
            rootFolder.Keywords.Add(keyword);

            // Assert - Verify relationships are correct
            Assert.AreEqual(rootFolder, keyword.ParentFolder);
            Assert.IsTrue(rootFolder.Keywords.Contains(keyword));
            Assert.IsFalse(rootFolder.IsExpanded); // Folder should still be collapsed initially
        }

        [TestMethod]
        public void ChildTermCreation_InCollapsedFolderStructure_ShouldHaveCorrectParentChain()
        {
            // Arrange - Create nested folder structure
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root Folder",
                IsExpanded = false
            };
            
            var childFolder = new KeywordFolder 
            { 
                Name = "Child Folder",
                ParentFolder = rootFolder,
                IsExpanded = false
            };
            
            rootFolder.Children.Add(childFolder);
            _queryList.Folders.Add(rootFolder);

            var parentKeyword = new Keyword2Find 
            { 
                Alias = "Parent Keyword",
                ParentFolder = childFolder
            };
            childFolder.Keywords.Add(parentKeyword);

            // Act - Create a child term
            var childTerm = new ChildTerm(parentKeyword, "Test Child Term");

            // Assert - Verify the parent chain is correct
            Assert.AreEqual(parentKeyword, childTerm.GetParent());
            Assert.AreEqual(childFolder, parentKeyword.ParentFolder);
            Assert.AreEqual(rootFolder, childFolder.ParentFolder);
            Assert.IsNull(rootFolder.ParentFolder);
        }

        [TestMethod]
        public void FolderExpansionLogic_ShouldWorkWithNestedStructure()
        {
            // Arrange - Create a 3-level nested structure
            var level1 = new KeywordFolder 
            { 
                Name = "Level 1",
                IsExpanded = false
            };
            
            var level2 = new KeywordFolder 
            { 
                Name = "Level 2",
                ParentFolder = level1,
                IsExpanded = false
            };
            
            var level3 = new KeywordFolder 
            { 
                Name = "Level 3",
                ParentFolder = level2,
                IsExpanded = false
            };

            level1.Children.Add(level2);
            level2.Children.Add(level3);
            _queryList.Folders.Add(level1);

            // Act - Simulate the expansion logic for level 3
            // This simulates what EnsureFolderPathExpanded would do
            var current = level3.ParentFolder;
            var pathToExpand = new System.Collections.Generic.List<KeywordFolder>();
            
            while (current != null)
            {
                pathToExpand.Insert(0, current);
                current = current.ParentFolder;
            }

            // Simulate expanding each folder in the path
            foreach (var folder in pathToExpand)
            {
                folder.IsExpanded = true;
            }

            // Assert - Verify the correct folders were expanded
            Assert.IsTrue(level1.IsExpanded, "Level 1 should be expanded");
            Assert.IsTrue(level2.IsExpanded, "Level 2 should be expanded");
            Assert.IsFalse(level3.IsExpanded, "Level 3 should remain in its original state");
        }

        [TestMethod]
        public void KeywordInNestedFolder_ShouldHaveCorrectExpansionPath()
        {
            // Arrange - Create nested structure with keyword
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root",
                IsExpanded = false
            };
            
            var subFolder = new KeywordFolder 
            { 
                Name = "Sub Folder",
                ParentFolder = rootFolder,
                IsExpanded = false
            };
            
            rootFolder.Children.Add(subFolder);
            _queryList.Folders.Add(rootFolder);

            var keyword = new Keyword2Find 
            { 
                Alias = "Nested Keyword",
                ParentFolder = subFolder
            };
            subFolder.Keywords.Add(keyword);

            // Act - Simulate expansion logic for the keyword's parent folder
            var targetFolder = keyword.ParentFolder;
            var current = targetFolder?.ParentFolder;
            var pathToExpand = new System.Collections.Generic.List<KeywordFolder>();
            
            while (current != null)
            {
                pathToExpand.Insert(0, current);
                current = current.ParentFolder;
            }

            foreach (var folder in pathToExpand)
            {
                folder.IsExpanded = true;
            }

            // Assert - Verify correct expansion
            Assert.IsTrue(rootFolder.IsExpanded, "Root folder should be expanded to make sub folder visible");
            Assert.IsFalse(subFolder.IsExpanded, "Sub folder should remain in original state");
            Assert.AreEqual(subFolder, keyword.ParentFolder);
        }

        [TestMethod]
        public void ChildTermInNestedStructure_ShouldHaveCorrectExpansionPath()
        {
            // Arrange - Create complex nested structure
            var rootFolder = new KeywordFolder { Name = "Root", IsExpanded = false };
            var subFolder = new KeywordFolder { Name = "Sub", ParentFolder = rootFolder, IsExpanded = false };
            rootFolder.Children.Add(subFolder);
            _queryList.Folders.Add(rootFolder);

            var keyword = new Keyword2Find { Alias = "Keyword", ParentFolder = subFolder };
            subFolder.Keywords.Add(keyword);

            var childTerm = new ChildTerm(keyword, "Child Term");

            // Act - Simulate expansion logic for child term
            var parentKeyword = childTerm.GetParent();
            var targetFolder = parentKeyword?.ParentFolder;
            
            if (targetFolder != null)
            {
                var current = targetFolder.ParentFolder;
                var pathToExpand = new System.Collections.Generic.List<KeywordFolder>();
                
                while (current != null)
                {
                    pathToExpand.Insert(0, current);
                    current = current.ParentFolder;
                }

                foreach (var folder in pathToExpand)
                {
                    folder.IsExpanded = true;
                }
            }

            // Assert - Verify the expansion path is correct
            Assert.IsTrue(rootFolder.IsExpanded, "Root folder should be expanded");
            Assert.IsFalse(subFolder.IsExpanded, "Sub folder should remain in original state");
            Assert.AreEqual(keyword, childTerm.GetParent());
            Assert.AreEqual(subFolder, keyword.ParentFolder);
            Assert.AreEqual(rootFolder, subFolder.ParentFolder);
        }
    }
}
