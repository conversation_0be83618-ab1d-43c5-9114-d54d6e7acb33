﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using uBuyFirst.CustomClasses;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Intl;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst.SearchTerms
{
    internal class SearchTermManager
    {
        public static void ImportSearchTermsFromFile(string fileLocation, QueryList ebaySearches)
        {
            var importLog = "Skipped rows:\n";
            try
            {
                var columnMapping = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
                // string[] rows = File.ReadAllLines(fileLocation);
                var csvTable = new List<List<string>>();
                var columns = new List<string>();
                var columnNames = new List<string>();
                using (var reader = new CsvFileReader(fileLocation))
                {
                    if (reader.ReadRow(columns))
                    {
                        columnNames = new List<string>(columns);
                    }
                    else
                    {
                        return;
                    }

                    for (var index = 0; index < columnNames.Count; index++)
                    {
                        columnMapping[columnNames[index]] = index;
                    }

                    // Read the rest of the rows
                    while (reader.ReadRow(columns))
                    {
                        var cells = new string[columns.Count];
                        columns.CopyTo(cells);
                        csvTable.Add(cells.ToList());
                    }
                }

                var csvSearchTermIds = new List<string>();
                if (csvTable.Count > 0)
                    for (var i = 0; i < csvTable.Count; i++)
                    {
                        var cells = csvTable[i];
                        if (cells is { Count: <= 5 })
                            continue;

                        var ebaySearchAlias = GetCellValue(cells, columnMapping, "eBay Search Alias");
                        var parentId = GetCellValue(cells, columnMapping, "Id");
                        csvSearchTermIds.Add(parentId);

                        var subSearchAlias = GetCellValue(cells, columnMapping, "Sub Search Alias");

                        if (string.IsNullOrEmpty(subSearchAlias?.Trim()))
                        {
                            var kw = new Keyword2Find();

                            var importResult = Import(cells, kw, columnMapping);
                            if (string.IsNullOrEmpty(importResult))
                            {
                                if (!ResultsView.ViewsDict.ContainsKey(kw.ViewName))
                                    ResultsView.CreateView(kw.ViewName);
                                kw.GridControl = ResultsView.ViewsDict[kw.ViewName];

                                // Handle folder assignment
                                var folderPath = GetCellValue(cells, columnMapping, "Folder Path");
                                var targetFolder = FindOrCreateFolderFromPath(folderPath, ebaySearches);

                                var existingSearches = ebaySearches.ChildrenCore.Where(a => a.Alias == kw.Alias)
                                    .ToArray();
                                if (existingSearches.Length > 0)
                                {
                                    UpdateSearch(kw, existingSearches);
                                }
                                else
                                {
                                    if (targetFolder != null)
                                    {
                                        kw.ParentFolder = targetFolder;
                                        targetFolder.Keywords.Add(kw);
                                    }
                                    else
                                    {
                                        kw.ParentCore = ebaySearches;
                                    }
                                }
                            }
                            else
                            {
                                importLog += i + ". [" + kw.Alias + "] - Incorrect columns: " + importResult + "\n";
                            }
                        }
                        else
                        {
                            var kw2Find = ebaySearches.ChildrenCore.FirstOrDefault(kw => kw.Id == parentId);
                            if (kw2Find != null)
                            {
                                var csvChildTerm = new ChildTerm(null, subSearchAlias);

                                var importResult = ImportChildTerm(cells, csvChildTerm, columnMapping);
                                if (string.IsNullOrEmpty(importResult))
                                {
                                    var subSearchIsNew = true;
                                    foreach (var importedChildTerm in kw2Find.ChildrenCore)
                                    {
                                        var subSearchExists = importedChildTerm.Alias == csvChildTerm.Alias;
                                        if (subSearchExists)
                                        {
                                            importedChildTerm.Alias = csvChildTerm.Alias;
                                            importedChildTerm.Keywords = csvChildTerm.Keywords;
                                            importedChildTerm.Enabled = csvChildTerm.Enabled;
                                            importedChildTerm.SearchInDescription = csvChildTerm.SearchInDescription;
                                            importedChildTerm.PriceMin = csvChildTerm.PriceMin;
                                            importedChildTerm.PriceMax = csvChildTerm.PriceMax;
                                            importedChildTerm.CategoryIDs = csvChildTerm.CategoryIDs;
                                            importedChildTerm.Condition = csvChildTerm.Condition;
                                            importedChildTerm.SubSearch = csvChildTerm.SubSearch;

                                            subSearchIsNew = false;

                                            break;
                                        }
                                    }

                                    if (subSearchIsNew)
                                    {
                                        csvChildTerm.SetParent(kw2Find);
                                        kw2Find.ChildrenCore.Add(csvChildTerm);
                                        csvChildTerm.SubSearch?.Rebuild();
                                    }
                                }
                                else
                                {
                                    importLog += i + ". [" + csvChildTerm.Alias + "] - Incorrect columns: " + importResult +
                                                 "\n";
                                }
                            }
                            else
                                importLog += i + ". [" + ebaySearchAlias + ": " + subSearchAlias +
                                             "] - Can't find parent eBay Search: " + ebaySearchAlias + "\n";
                        }
                    }

                var ebaySearchesNotInCsv = ebaySearches.ChildrenCore.Where(ebaySearch => !csvSearchTermIds.Contains(ebaySearch.Id)).ToList();
                foreach (var removedSearch in ebaySearchesNotInCsv)
                {
                    ebaySearches.ChildrenCore.Remove(removedSearch);
                }

                if (importLog.Length > 20)
                {
                    XtraMessageBox.Show(importLog);
                }
            }
            catch (IOException ex)
            {
                XtraMessageBox.Show("Please, close all spreadsheet documents and try again.\n" + ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Importing keywords: ", ex);
            }
        }

        private static void UpdateSearch(Keyword2Find kw, Keyword2Find[] keyword2Finds)
        {
            for (var j = 0; j < keyword2Finds.Length; j++)
            {
                keyword2Finds[j].Alias = kw.Alias;
                keyword2Finds[j].AvailableTo = kw.AvailableTo;
                keyword2Finds[j].Categories4Api = kw.Categories4Api;
                keyword2Finds[j].Condition = kw.Condition;
                keyword2Finds[j].EBaySite = kw.EBaySite;
                keyword2Finds[j].EbaySiteName = kw.EbaySiteName;
                keyword2Finds[j].Frequency = kw.Frequency;
                keyword2Finds[j].KeywordEnabled = kw.KeywordEnabled;
                keyword2Finds[j].Kws = kw.Kws;
                keyword2Finds[j].ListingType = kw.ListingType;
                keyword2Finds[j].LocatedIn = kw.LocatedIn;
                keyword2Finds[j].PriceMax = kw.PriceMax;
                keyword2Finds[j].PriceMin = kw.PriceMin;
                keyword2Finds[j].SearchInDescription = kw.SearchInDescription;
                keyword2Finds[j].SellerType = kw.SellerType;
                keyword2Finds[j].Sellers = kw.Sellers;
                keyword2Finds[j].Zip = kw.Zip;

                // Update Restocker fields
                keyword2Finds[j].JobId = kw.JobId;
                keyword2Finds[j].RequiredQuantity = kw.RequiredQuantity;
                keyword2Finds[j].PurchasedQuantity = kw.PurchasedQuantity;
            }
        }

        public static void ExportSearchesToFile(List<Keyword2Find> searchTermList)
        {
            try
            {
                var header = new List<string>
                {
                    "Id",
                    "Folder Path",
                    "Sub Search Id",
                    "eBay Search Alias",
                    "Sub Search Alias",
                    "Keywords",
                    "Keyword enabled",
                    "Search in Description",
                    "Price Min",
                    "Price Max",
                    "Category ID",
                    "Condition",
                    "Site",
                    "Located in",
                    "Ships to",
                    "Ship Zipcode",
                    "Sellers",
                    "Include/Exclude",
                    "Interval",
                    "Listing Type",
                    "Filter",
                    "View"
                };

                // Add Restocker fields only if RestockerEnabled is true
                if (ConnectionConfig.RestockerEnabled)
                {
                    header.Add("Job ID");
                    header.Add("Required Quantity");
                }
                var fileContents = Helpers.CreateCSVRow(header) + "\r\n";
                foreach (var item in searchTermList)
                {
                    fileContents += Export(item);
                }

                File.WriteAllText(Path.Combine(Folders.Logs, "KeywordsExport.csv"), fileContents);
                Process.Start(Path.Combine(Folders.Logs, "KeywordsExport.csv"));
            }
            catch (IOException ex)
            {
                XtraMessageBox.Show("Please, close all spreadsheet documents and try again.\n" + ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Export keywords: ", ex);
            }
        }
        public static string Export(Keyword2Find kw)
        {
            var conditionsEbaySearch = "";
            foreach (var c in kw.Condition)
            {
                if (SearchStuff.ConditionsDict.TryGetValue(c, out var s))
                    conditionsEbaySearch += s + ",";
            }

            conditionsEbaySearch = conditionsEbaySearch.Trim(',');
            var kwProperties = new List<string>
            {
                kw.Id,
                BuildFolderPath(kw),
                "",
                kw.Alias,
                "",
                kw.Kws,
                (kw.KeywordEnabled == CheckState.Checked).ToString(),
                kw.SearchInDescription.ToString(),
                kw.PriceMin.ToString(CultureInfo.InvariantCulture),
                kw.PriceMax.ToString(CultureInfo.InvariantCulture),
                string.Join("|", kw.Categories4Api.Split(',')),
                conditionsEbaySearch,
                kw.EBaySite.ToString(),
                kw.LocatedIn,
                kw.AvailableTo,
                kw. Zip,
                kw.SellersStr,
                kw.SellerType,
                kw. Frequency.ToString(@"hh\:mm\:ss"),
                string.Join(",",kw. ListingType),
                "",
                kw.ViewName
            };

            // Add Restocker field values only if RestockerEnabled is true
            if (ConnectionConfig.RestockerEnabled)
            {
                kwProperties.Add(kw.JobId);
                kwProperties.Add(kw.RequiredQuantity.ToString(CultureInfo.InvariantCulture));
            }

            var row = Helpers.CreateCSVRow(kwProperties) + "\r\n";

            foreach (var term in kw.ChildrenCore)
            {
                var conditionsSubSearch = "";
                foreach (var c in term.Condition)
                {
                    if (SearchStuff.ConditionsDict.TryGetValue(c, out var s))
                        conditionsSubSearch += s + ",";
                }

                conditionsSubSearch = conditionsSubSearch.Trim(',');
                var subFilter = term.SubSearch?.FilterCriteria?.ToString() ?? "";
                var childRow = new List<string>
                {
                    kw.Id,
                    BuildFolderPath(kw),
                    term.Id,
                    kw.Alias,
                    term.Alias,
                    term.Keywords,
                    term.Enabled.ToString(),
                    term.SearchInDescription.ToString(),
                    term.PriceMin.ToString(CultureInfo.InvariantCulture),
                    term.PriceMax.ToString(CultureInfo.InvariantCulture),
                    string.Join("|", term.CategoryIDs),
                    conditionsSubSearch,
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    subFilter,
                    ""
                };

                // Add empty Restocker field values for child rows only if RestockerEnabled is true
                if (ConnectionConfig.RestockerEnabled)
                {
                    childRow.Add(""); // Job ID
                    childRow.Add(""); // Required Quantity
                }
                row += Helpers.CreateCSVRow(childRow) + "\r\n";
            }

            return row;
        }

        //TOOD: handle issue when cells < 8 in import file
        public static string Import(List<string> cells, Keyword2Find kw, Dictionary<string, int> columnMapping)
        {
            var importLog = "";
            try
            {
                kw.Threads = 1;

                var idValue = GetCellValue(cells, columnMapping, "Id");
                if (!string.IsNullOrEmpty(idValue))
                    kw.Id = idValue;
                else
                    importLog += "Id, ";


                var aliasValue = GetCellValue(cells, columnMapping, "eBay Search Alias");
                if (!string.IsNullOrEmpty(aliasValue))
                    kw.Alias = aliasValue;
                else
                    importLog += "Alias, ";

                kw.Kws = "";
                var kwsValue = GetCellValue(cells, columnMapping, "Keywords");
                var categoryIdValue = GetCellValue(cells, columnMapping, "Category ID");
                if (string.IsNullOrEmpty(kwsValue) && string.IsNullOrEmpty(categoryIdValue))
                    importLog += "Please provide either Keywords or Category ID, ";

                if (!string.IsNullOrEmpty(kwsValue))
                    kw.Kws = kwsValue.Trim().Trim(',');

                var keywordEnabled = GetCellValue(cells, columnMapping, "Keyword enabled");
                if (bool.TryParse(keywordEnabled, out var flag))
                    kw.KeywordEnabled = flag ? CheckState.Checked : CheckState.Unchecked;
                else
                    importLog += "Keyword Enabled, ";

                var searchInDescription = GetCellValue(cells, columnMapping, "Search in Description");
                if (bool.TryParse(searchInDescription, out flag))
                    kw.SearchInDescription = flag;

                var priceMin = GetCellValue(cells, columnMapping, "Price Min");
                if (!string.IsNullOrEmpty(priceMin))
                    kw.PriceMin = ParseHelpers.ParseDouble(priceMin.Trim());
                else
                    importLog += "PriceMin, ";

                var priceMax = GetCellValue(cells, columnMapping, "Price Max");
                if (!string.IsNullOrEmpty(priceMax))
                    kw.PriceMax = ParseHelpers.ParseDouble(priceMax.Trim());
                else
                    importLog += "PriceMax, ";

                var categoryID = GetCellValue(cells, columnMapping, "Category ID");
                if (categoryID.Contains("|"))
                {
                    kw.Categories4Api = string.Join(",", categoryID.Split('|'));
                }
                else
                {
                    kw.Categories4Api = categoryID;
                }

                var condition = GetCellValue(cells, columnMapping, "Condition");
                var condValues = condition.Split(',');
                kw.Condition = (from condDesc in condValues from condIdDesc in SearchStuff.ConditionsDict where condIdDesc.Value == condDesc select condIdDesc.Key).ToArray();

                kw.EbaySiteName = GetCellValue(cells, columnMapping, "Site"); ;
                kw.EBaySite = CountryProvider.GetEbaySite(GetCellValue(cells, columnMapping, "Site"));
                kw.LocatedIn = GetCellValue(cells, columnMapping, "Located in"); ;
                kw.AvailableTo = GetCellValue(cells, columnMapping, "Ships to"); ;
                kw.Zip = GetCellValue(cells, columnMapping, "Ship Zipcode"); ;
                kw.Sellers = GetCellValue(cells, columnMapping, "Sellers").Split(',');

                kw.SellerType = "";

                if (GetCellValue(cells, columnMapping, "Include/Exclude")?.ToLower() == "include")
                    kw.SellerType = "Include";
                if (GetCellValue(cells, columnMapping, "Include/Exclude")?.ToLower() == "exclude")
                    kw.SellerType = "Exclude";


                var viewName = GetCellValue(cells, columnMapping, "View");
                if (string.IsNullOrEmpty(viewName))
                    viewName = "Results";
                kw.ViewName = viewName;

                if (TimeSpan.TryParse(GetCellValue(cells, columnMapping, "Interval").Replace("\"", "").Replace("=", ""), out var ts))
                    kw.Frequency = ts;
                else
                    importLog += "Search Interval, ";

                // Import Restocker fields
                kw.JobId = GetCellValue(cells, columnMapping, "Job ID");

                var requiredQuantityValue = GetCellValue(cells, columnMapping, "Required Quantity");
                if (int.TryParse(requiredQuantityValue, out var requiredQuantity))
                    kw.RequiredQuantity = requiredQuantity;
                else
                    kw.RequiredQuantity = 0; // Default to 0 for invalid or empty values

                var purchasedQuantityValue = GetCellValue(cells, columnMapping, "Purchased Quantity");
                if (int.TryParse(purchasedQuantityValue, out var purchasedQuantity))
                    kw.PurchasedQuantity = purchasedQuantity;
                else
                    kw.PurchasedQuantity = 0; // Default to 0 for invalid or empty values

                var listingTypeStringList = GetCellValue(cells, columnMapping, "Listing Type").Split(',');
                var listingTypesImportedList = new List<ListingType>(); //listingTypeListString.Select(s => (ListingType)Enum.Parse(typeof(ListingType), s));
                foreach (var listingType in listingTypeStringList)
                {
                    try
                    {
                        var listing = (ListingType)Enum.Parse(typeof(ListingType), listingType);
                        listingTypesImportedList.Add(listing);
                    }
                    catch (Exception)
                    {
                        XtraMessageBox.Show($"[{kw.Alias}] Couldn't parse ListingType: '{listingType}'");
                    }
                }

                kw.ListingType = listingTypesImportedList.ToArray();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Import keywords: ", ex);
            }

            return importLog;
        }
        public static string ImportChildTerm(List<string> cells, ChildTerm term,
            Dictionary<string, int> columnMapping)
        {
            var importLog = "";
            try
            {
                var subSearchId = GetCellValue(cells, columnMapping, "Sub Search Id");
                if (!string.IsNullOrEmpty(subSearchId))
                    term.Id = subSearchId;
                else
                    importLog += "Sub Search Id, ";


                var subsSearchAlias = GetCellValue(cells, columnMapping, "Sub Search Alias");
                if (!string.IsNullOrEmpty(subsSearchAlias))
                    term.Alias = subsSearchAlias;
                else
                    importLog += "Sub Search Alias, ";

                var kwsValue = GetCellValue(cells, columnMapping, "Keywords");

                if (!string.IsNullOrEmpty(kwsValue))
                    term.Keywords = kwsValue.Trim(',');
                else
                    importLog += "Keywords, ";

                var keywordEnabled = GetCellValue(cells, columnMapping, "Keyword enabled");
                if (bool.TryParse(keywordEnabled, out var flag))
                    term.Enabled = flag;
                else
                    importLog += "Keyword Enabled, ";

                var searchInDescription = GetCellValue(cells, columnMapping, "Search in Description");
                if (bool.TryParse(searchInDescription, out flag))
                    term.SearchInDescription = flag;

                var priceMin = GetCellValue(cells, columnMapping, "Price Min");
                if (!string.IsNullOrEmpty(priceMin))
                    term.PriceMin = ParseHelpers.ParseDouble(priceMin.Trim());
                else
                    importLog += "PriceMin, ";

                var priceMax = GetCellValue(cells, columnMapping, "Price Max");
                if (!string.IsNullOrEmpty(priceMax))
                    term.PriceMax = ParseHelpers.ParseDouble(priceMax.Trim());
                else
                    importLog += "PriceMax, ";

                var categoryID = GetCellValue(cells, columnMapping, "Category ID");
                if (term.CategoryIDs.Contains("|"))
                    term.CategoryIDs = categoryID.Split('|');
                else
                    term.CategoryIDs = categoryID.Split(',');

                var condition = GetCellValue(cells, columnMapping, "Condition");
                var condValues = condition.Split(',');
                term.Condition = (from condDesc in condValues from condIdDesc in SearchStuff.ConditionsDict where condIdDesc.Value == condDesc select condIdDesc.Key).ToArray();

                var filter = GetCellValue(cells, columnMapping, "Filter");
                if (string.IsNullOrEmpty(filter))
                {
                    term.SubSearch = new XFilterClassChild
                    {
                        Action = "Keep rows",
                        Alias = term.Alias,
                    };
                }
                else
                {
                    term.SubSearch = new XFilterClassChild
                    {
                        Action = "Keep rows",
                        Alias = term.Alias,
                        Enabled = false,
                        FilterCriteria = DevExpress.Data.Filtering.CriteriaOperator.Parse(filter, null)
                    };
                }
            }
            catch (Exception ex)
            {
                importLog += ex.Message;
                ExM.ubuyExceptionHandler("Import keywords: ", ex);
            }

            return importLog;
        }
        private static string GetCellValue(List<string> cells, Dictionary<string, int> columnMapping, string columnName)
        {
            if (columnMapping.TryGetValue(columnName, out var index))
            {
                if (index >= 0 && index < cells.Count)
                {
                    return cells[index];
                }
            }
            return "";
        }

        /// <summary>
        /// Builds the folder path for a keyword
        /// </summary>
        private static string BuildFolderPath(Keyword2Find kw)
        {
            if (kw.ParentFolder == null)
                return "";

            return kw.ParentFolder.GetFullPath();
        }

        /// <summary>
        /// Finds or creates a folder from a path string
        /// </summary>
        private static Search.KeywordFolder FindOrCreateFolderFromPath(string folderPath, QueryList queryList)
        {
            if (string.IsNullOrEmpty(folderPath))
                return null;

            // Try to find existing folder
            var existingFolder = queryList.FindFolderByPath(folderPath);
            if (existingFolder != null)
                return existingFolder;

            // Create folder hierarchy
            var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
            Search.KeywordFolder currentFolder = null;
            Search.KeywordFolder parentFolder = null;

            for (int i = 0; i < pathParts.Length; i++)
            {
                var folderName = pathParts[i].Trim();
                var partialPath = string.Join(" > ", pathParts.Take(i + 1));

                currentFolder = queryList.FindFolderByPath(partialPath);
                if (currentFolder == null)
                {
                    // Generate unique folder name to avoid conflicts
                    string uniqueFolderName;
                    if (parentFolder != null)
                    {
                        uniqueFolderName = Search.KeywordFolder.GenerateUniqueName(folderName, parentFolder.Children);
                    }
                    else
                    {
                        uniqueFolderName = queryList.GenerateUniqueRootFolderName(folderName);
                    }

                    // Create new folder with unique name
                    currentFolder = new Search.KeywordFolder
                    {
                        Name = uniqueFolderName,
                        Id = Guid.NewGuid().ToString(),
                        ParentFolder = parentFolder
                    };

                    if (parentFolder != null)
                    {
                        parentFolder.Children.Add(currentFolder);
                    }
                    else
                    {
                        queryList.Folders.Add(currentFolder);
                    }
                }

                parentFolder = currentFolder;
            }

            return currentFolder;
        }
    }
}
