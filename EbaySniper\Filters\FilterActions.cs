﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Telegram;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Interface for filter actions using the Strategy Pattern - focused only on business logic
    /// </summary>
    public interface IFilterAction
    {
        /// <summary>
        /// Display name shown in the UI
        /// </summary>
        string DisplayName { get; }

        /// <summary>
        /// Unique identifier for serialization
        /// </summary>
        string ActionTypeIdentifier { get; }

        /// <summary>
        /// Execute the filter action - pure business logic only
        /// </summary>
        Task<FilterActionResult> ExecuteAsync(IFilterActionContext context);

        /// <summary>
        /// Validate the action configuration
        /// </summary>
        bool ValidateConfiguration(XFilterClass filter, out string? errorMessage);

        /// <summary>
        /// Serialize action-specific data
        /// </summary>
        Dictionary<string, object> SerializeActionData();

        /// <summary>
        /// Deserialize action-specific data
        /// </summary>
        void DeserializeActionData(Dictionary<string, object> data);
    }

    /// <summary>
    /// Interface for UI configuration - separate from business logic
    /// </summary>
    public interface IFilterActionUIConfigurator
    {
        string ActionTypeIdentifier { get; }
        FilterUIConfiguration GetUIConfiguration();
        void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor);
        void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor);
    }

    /// <summary>
    /// Declarative UI configuration - no code, just data
    /// </summary>
    public class FilterUIConfiguration
    {
        public bool ShowColumnSelection { get; set; }
        public bool ShowFormatControls { get; set; }
        public List<string> AdditionalControlsToShow { get; set; } = new();
        public List<string> AdditionalControlsToHide { get; set; } = new();
        public Dictionary<string, object> ControlValues { get; set; } = new();
    }

    /// <summary>
    /// Clean interface for form data access - no reflection needed
    /// </summary>
    public interface IFormDataAccessor
    {
        string GetSelectedColumn();
        void SetSelectedColumn(string column);
        T GetControlValue<T>(string controlName);
        void SetControlValue(string controlName, object value);
    }

    /// <summary>
    /// Context interface for action execution
    /// </summary>
    public interface IFilterActionContext
    {
        XFilterClass? FilterRule { get; }
        GridView? GridView { get; }
        DataTable? SourceDataTable { get; }
        DataRow? CurrentRow { get; }
    }

    /// <summary>
    /// Result of filter action execution
    /// </summary>
    public class FilterActionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public object Data { get; set; }

        public static FilterActionResult CreateSuccess(string message = null, object data = null)
        {
            return new FilterActionResult { Success = true, Message = message, Data = data };
        }

        public static FilterActionResult CreateFailure(string message, Exception exception = null)
        {
            return new FilterActionResult { Success = false, Message = message, Exception = exception };
        }
    }

    /// <summary>
    /// Implementation of IFilterActionContext
    /// </summary>
    public class FilterActionContext : IFilterActionContext
    {
        public XFilterClass FilterRule { get; set; }
        public GridView GridView { get; set; }
        public DataTable SourceDataTable { get; set; }
        public DataRow CurrentRow { get; set; }
    }

    /// <summary>
    /// Registry that maps actions to their UI configurators
    /// </summary>
    public static class FilterActionUIRegistry
    {
        private static readonly Dictionary<string, IFilterActionUIConfigurator> _uiConfigurators = new();
        private static bool _initialized = false;

        static FilterActionUIRegistry()
        {
            Initialize();
        }

        private static void Initialize()
        {
            if (_initialized) return;

            // Register UI configurators
            Register(new FormatCellsUIConfigurator());
            Register(new FormatRowsUIConfigurator());
            Register(new RemoveRowsUIConfigurator());
            Register(new SendToTelegramUIConfigurator());
            Register(new BuyWithAccountUIConfigurator());
            Register(new SendToWebhookUIConfigurator());
            Register(new OpenItemPageUIConfigurator());
            Register(new OpenCheckoutPageUIConfigurator());

            // Conditionally register RestockFilterUIConfigurator based on ConnectionConfig
            if (uBuyFirst.Prefs.ConnectionConfig.RestockerEnabled)
            {
                Register(new RestockFilterUIConfigurator());
            }

            _initialized = true;
        }

        /// <summary>
        /// Re-initializes the registry to update based on current ConnectionConfig state
        /// Should be called after ConnectionConfig is loaded
        /// </summary>
        public static void Reinitialize()
        {
            _uiConfigurators.Clear();
            _initialized = false;
            Initialize();
        }

        public static void Register(IFilterActionUIConfigurator configurator)
        {
            _uiConfigurators[configurator.ActionTypeIdentifier] = configurator;
        }

        public static IFilterActionUIConfigurator GetUIConfigurator(string actionTypeIdentifier)
        {
            return _uiConfigurators.TryGetValue(actionTypeIdentifier, out var configurator)
                ? configurator
                : null;
        }

        public static IEnumerable<IFilterActionUIConfigurator> GetAllUIConfigurators()
        {
            return _uiConfigurators.Values;
        }
    }

    /// <summary>
    /// Factory for creating filter actions with legacy migration support
    /// </summary>
    public static class FilterActionFactory
    {
        private static readonly Dictionary<string, Func<IFilterAction>> _actionRegistry = new();
        private static readonly Dictionary<string, string> _legacyActionMigration = new();
        private static bool _initialized = false;
        private static bool _lastRestockerEnabledState = false;

        public static void Initialize(ITelegramSender? telegramSender)
        {
            bool currentRestockerState = uBuyFirst.Prefs.ConnectionConfig.RestockerEnabled;
            bool needsReinitialization = _initialized && (_lastRestockerEnabledState != currentRestockerState);

            if (!_initialized || needsReinitialization)
            {
                if (needsReinitialization)
                {
                    // Clear existing registrations for re-initialization
                    _actionRegistry.Clear();
                    _legacyActionMigration.Clear();
                }

                // One-time registrations for actions without dependencies
                RegisterAction<FormatCellsAction>();
                RegisterAction<FormatRowsAction>();
                RegisterAction<RemoveRowsAction>();
                RegisterAction<OpenItemPageAction>();
                RegisterAction<OpenCheckoutPageAction>();

                // Conditionally register RestockFilterAction based on ConnectionConfig
                if (currentRestockerState)
                {
                    RegisterAction<RestockFilterAction>();
                    _legacyActionMigration["Restock"] = RestockFilterAction.IDENTIFIER;
                }

                // RegisterAction<BuyWithAccountAction>(); // If parameterless and one-time
                // RegisterAction<SendToWebhookAction>();  // If parameterless and one-time

                // One-time legacy migration mappings for these actions
                _legacyActionMigration["Format cells"] = FormatCellsAction.Identifier;
                _legacyActionMigration["Format rows"] = FormatRowsAction.IDENTIFIER;
                _legacyActionMigration["Remove rows"] = RemoveRowsAction.IDENTIFIER;
                _legacyActionMigration["Open item page"] = OpenItemPageAction.IDENTIFIER;
                _legacyActionMigration["Open checkout page"] = OpenCheckoutPageAction.IDENTIFIER;
                // _legacyActionMigration["Buy with Account"] = BuyWithAccountAction.IDENTIFIER; // If one-time
                // _legacyActionMigration["Send to Webhook"] = SendToWebhookAction.IDENTIFIER; // If one-time

                _initialized = true;
                _lastRestockerEnabledState = currentRestockerState;
            }

            // Registrations that happen on every call (e.g., for dependency updates)
            RegisterSendToTelegramAction(telegramSender);
            _legacyActionMigration["Send to Telegram"] = SendToTelegramAction.IDENTIFIER;

            // Commented-out actions from original code would go here if they needed similar handling:
            // RegisterAction<BuyWithAccountAction>(); // If it needs re-registration or has dependencies
            // _legacyActionMigration["Buy with Account"] = BuyWithAccountAction.IDENTIFIER;

            // RegisterAction<SendToWebhookAction>(); // If it needs re-registration or has dependencies
            // _legacyActionMigration["Send to Webhook"] = SendToWebhookAction.IDENTIFIER;
        }

        private static void RegisterAction<T>() where T : IFilterAction, new()
        {
            var instance = new T();
            var identifier = instance.ActionTypeIdentifier;
            if (string.IsNullOrEmpty(identifier))
            {
                throw new InvalidOperationException($"Action type {typeof(T).Name} returned null or empty ActionTypeIdentifier");
            }
            _actionRegistry[identifier] = () => new T();
        }

        private static void RegisterSendToTelegramAction(ITelegramSender? telegramSender)
        {
            // _telegramSender can be null here if not configured.
            // The SendToTelegramAction constructor will handle a null ITelegramSender.
            _actionRegistry[SendToTelegramAction.IDENTIFIER] = () => new SendToTelegramAction(telegramSender);
        }

        // Example for BuyWithAccountAction if it needed dependencies
        // private static void RegisterBuyWithAccountAction()
        // {
        //     // Example: RegisterAction<BuyWithAccountAction>();
        // }

        // private static void RegisterBuyWithAccountAction()
        // {
        //     // Assuming some dependency _accountService
        //     // if (_accountService == null) throw new InvalidOperationException("AccountService not initialized.");
        //     // _actionRegistry[BuyWithAccountAction.IDENTIFIER] = () => new BuyWithAccountAction(_accountService);
        //     // For now, if it has a parameterless constructor and is simple:
        //     RegisterAction<BuyWithAccountAction>();
        // }


        public static IFilterAction CreateAction(string actionIdentifier)
        {
            if (!_initialized)
            {
                // This indicates Initialize(ITelegramSender) hasn't been called.
                // Consider how to handle this: throw an error, or attempt a default initialization if possible.
                // For now, let's assume Initialize is called at startup.
                throw new InvalidOperationException("FilterActionFactory not initialized. Call Initialize with dependencies first.");
            }

            if (_actionRegistry.TryGetValue(actionIdentifier, out var factory))
            {
                return factory();
            }
            else
            {
                return null;
            }
        }

        public static IFilterAction CreateFromLegacyAction(string legacyAction)
        {
            if (!_initialized)
            {
                throw new InvalidOperationException("FilterActionFactory not initialized. Call Initialize with dependencies first.");
            }

            if (string.IsNullOrEmpty(legacyAction))
                return null;

            // Handle "Buy with [username]" pattern
            if (legacyAction.StartsWith("Buy with "))
            {
                var action = CreateAction(BuyWithAccountAction.IDENTIFIER);
                if (action is BuyWithAccountAction buyAction)
                {
                    buyAction.AccountUsername = legacyAction.Substring("Buy with ".Length);
                }
                return action;
            }

            // Handle standard legacy actions
            if (_legacyActionMigration.TryGetValue(legacyAction, out var identifier))
            {
                return CreateAction(identifier);
            }

            return null;
        }

        public static IEnumerable<IFilterAction> GetAllAvailableActions()
        {
            if (!_initialized)
            {
                throw new InvalidOperationException("FilterActionFactory not initialized. Call Initialize with dependencies first.");
            }
            return _actionRegistry.Values.Select(factory => factory());
        }

        public static void RegisterCustomAction<T>() where T : IFilterAction, new()
        {
            RegisterAction<T>();
        }

        /// <summary>
        /// Re-initializes the factory to update based on current ConnectionConfig state
        /// Should be called after ConnectionConfig is loaded
        /// </summary>
        public static void Reinitialize(ITelegramSender? telegramSender)
        {
            _actionRegistry.Clear();
            _legacyActionMigration.Clear();
            _initialized = false;
            _lastRestockerEnabledState = false; // Reset state tracking
            Initialize(telegramSender);
        }
    }

    /// <summary>
    /// UI configurator for Format Cells action
    /// </summary>
    public class FormatCellsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatCellsAction.Identifier;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = true,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            if (!string.IsNullOrEmpty(filter.FormatColumn))
            {
                formAccessor.SetSelectedColumn(filter.FormatColumn);
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            filter.FormatColumn = formAccessor.GetSelectedColumn();
        }
    }

    /// <summary>
    /// UI configurator for Format Rows action
    /// </summary>
    public class FormatRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row formatting
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row formatting
        }
    }

    /// <summary>
    /// UI configurator for Remove Rows action
    /// </summary>
    public class RemoveRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RemoveRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row removal
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row removal
        }
    }

    /// <summary>
    /// UI configurator for Send to Telegram action
    /// </summary>
    public class SendToTelegramUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToTelegramAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to load for Telegram action
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to save for Telegram action
        }
    }

    /// <summary>
    /// UI configurator for Buy with Account action
    /// </summary>
    public class BuyWithAccountUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => BuyWithAccountAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }
    }

    /// <summary>
    /// UI configurator for Send to Webhook action
    /// </summary>
    public class SendToWebhookUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToWebhookAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
        }
    }

    /// <summary>
    /// UI configurator for Open Item Page action
    /// </summary>
    public class OpenItemPageUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => OpenItemPageAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to load for Open Item Page action
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to save for Open Item Page action
        }
    }

    /// <summary>
    /// UI configurator for Open Checkout Page action
    /// </summary>
    public class OpenCheckoutPageUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => OpenCheckoutPageAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to load for Open Checkout Page action
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to save for Open Checkout Page action
        }
    }
}
