﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using BrowseAPI;
using Shopping;
using uBuyFirst.BrowseAPI;
using uBuyFirst.CustomClasses;
using uBuyFirst.Data;
using uBuyFirst.Network;
using uBuyFirst.Parsing;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Stats;
using uBuyFirst.Time;
using uBuyFirst.Tools;
using uBuyFirst.Views;

[assembly:
    Obfuscation(Exclude = false,
        Feature = "preset(minimum);"
                  + "+anti debug;"
                  + "+anti dump;"
                  + "+anti ildasm;"
                  + "+anti tamper(key=dynamic);"
                  + "+constants;"
                  + "+ctrl flow;"
                  + "+invalid metadata;"
                  + "+ref proxy;"
                  + "-rename;")]
[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]

namespace uBuyFirst.Search.Status
{
    public class GetItemsStatus
    {
        internal static int MaxUpdateSeconds = 30;

        internal static int CheckInterval = 5;
        private static bool _statusDebugger = false;
        private DateTime _lastRequestTime = DateTime.UtcNow;

        public void StartCheckStatusLoop()
        {
            if (Debugger.IsAttached)
            {
                _statusDebugger = true;
            }

            Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        var batch = GetNext20Batch();
                        var secondsSinceCheck = (DateTime.UtcNow - _lastRequestTime).TotalSeconds;
                        if (batch.Count == 0 || secondsSinceCheck < CheckInterval)
                        {
                            await Task.Delay(CheckInterval * 1000);
                        }
                        else
                        {
                            //Debug.WriteLineIf(_statusDebugger, $"-= Request {batch.Count}");
                            if (!string.IsNullOrEmpty(ConnectionConfig.Token2))
                            {
                                var updatedItems = await FetchBatchStatusBrowseAPI(batch.Keys.ToList());
                                if (updatedItems != null)
                                {
                                    UpdateGrids(batch, updatedItems);
                                }
                            }

                            _lastRequestTime = DateTime.UtcNow;
                        }
                    }
                    catch (Exception)
                    {
                        await Task.Delay(10 * 1000);
                    }
                }
            });
        }

        private static Dictionary<string, DataRow> GetNext20Batch()
        {
            var itemsToCheck = GetUpdatePriority();

            itemsToCheck = TakeTop20(itemsToCheck);

            var logStr = $"P {string.Join(",", itemsToCheck.Select(item => ((DataList)item.Value["Blob"]).StatusPriority))}";
            //Debug.WriteLineIf(_statusDebugger, logStr);

            foreach (var row in itemsToCheck)
            {
                ((DataList)row.Value["Blob"]).LastStatusCheck = new DateTimeWithDiff {
                    TimeDiff = 0,
                    Utc = DateTime.UtcNow
                };
            }

            return itemsToCheck;
        }

        private static Dictionary<string, DataRow> TakeTop20(Dictionary<string, DataRow> itemsToCheck)
        {
            var itemsPrioritySorted = itemsToCheck
                .OrderBy(item =>
                {
                    // Check if the item.Value is a DataRow and is not null
                    if (item.Value is DataRow row)
                    {
                        // Ensure the row is not detached or deleted
                        if (row.RowState != DataRowState.Detached && row.RowState != DataRowState.Deleted)
                        {
                            // Check if the "Blob" column exists and is of type DataList
                            if (row["Blob"] is DataList dataList)
                            {
                                return dataList.StatusPriority;
                            }
                        }
                    }

                    // Return a default priority value if any of the conditions fail
                    return int.MaxValue;
                })
                .ToList();
            var itemsPriorityTop20 = itemsPrioritySorted.Take(20);
            itemsToCheck = itemsPriorityTop20.ToDictionary(itemId => itemId.Key, itemLastChecked => itemLastChecked.Value);

            var firstItem = itemsToCheck.FirstOrDefault();
            var statusPriority = ((DataList)firstItem.Value?["Blob"])?.StatusPriority;
            if (statusPriority >= MaxUpdateSeconds)
                itemsToCheck.Clear();
            return itemsToCheck;
        }

        private static Dictionary<string, DataRow> GetUpdatePriority()
        {
            var itemsToCheck = new Dictionary<string, DataRow>();

            foreach (var view in ResultsView.ViewsDict.Values)
            {
                var index = 0;
                var datasource = (DataTable)view.DataSource;

                while (index < datasource.Rows.Count)
                {
                    try
                    {
                        var row = datasource.Rows[index];
                        var rowDeleted = row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached;
                        if (rowDeleted)
                        {
                            index++;
                            continue;
                        }

                        var blobCell = row["Blob"];
                        if (blobCell is not DataList d)
                            continue;

                        var listingEnded = d.ItemStatus != Data.ItemStatus.Active && d.ItemStatus != Data.ItemStatus.Unknown && d.ItemStatus != Data.ItemStatus.Updated;

                        if (listingEnded)
                        {
                            index++;
                            continue;
                        }

                        var itemPriority = int.MaxValue;

                        var isFreshItem = d.TimeSinceFound < MaxUpdateSeconds;
                        var auctionEndingSoon = d.SecondsLeft >= 0 && d.SecondsLeft < MaxUpdateSeconds;
                        var listingCompleted = d.TimeSinceEnded is > 0;

                        if (isFreshItem)
                        {
                            if (d.TimeSinceChecked > 10)
                                itemPriority = d.TimeSinceFound - 10;
                        }
                        else if (auctionEndingSoon)
                        {
                            if (d.TimeSinceChecked > 20)
                                itemPriority = MaxUpdateSeconds - (int)d.TimeLeft.TotalSeconds;
                        }
                        else if (listingCompleted)
                        {
                            //itemPriority = 0;
                            //d.Status = Data.Status.Ended;
                        }
                        else
                        {
                            index++;
                            continue;
                        }

                        d.StatusPriority = itemPriority;
                        if (!itemsToCheck.ContainsKey(d.ItemID))
                            itemsToCheck.Add(d.ItemID, row);
                    }
                    catch (Exception e)
                    {
                        Debug.WriteLine(e);
                    }

                    index++;
                }
            }

            return itemsToCheck;
        }

        internal static SimpleItemType[] FetchBatchStatus(List<string> itemIDs)
        {
            Stat.StatusUpdateCounter++;
            var ids = string.Join(",", itemIDs.Take(20).ToArray());
            var result = NetTools.ShoppingApiCall(ids, "GetItemStatus", "");

            if (string.IsNullOrEmpty(result))
                return new SimpleItemType[] { };

            var getItemStatusResponse = result.ParseXml<GetItemStatusResponseType>();
            var items = getItemStatusResponse.Item;
            return items;
        }

        internal static async Task<CoreItem[]?> FetchBatchStatusBrowseAPI(List<string> itemIDs)
        {
            Stat.StatusUpdateCounter++;
            var ids = itemIDs.Take(20).ToList();
            var token = ConnectionConfig.Token2;
            var requestParams = new BrowseAPINetwork.RequestParams { MarketplaceID = "EBAY_US", Token = token };
            try
            {
                var result = await BrowseAPINetwork.GetItems(ids, requestParams);
                return result.Items1;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return null;
        }

        public void UpdateGrids(Dictionary<string, DataRow> rows, SimpleItemType[] updatedItems)
        {
            foreach (var simpleItem in updatedItems)
            {
                foreach (var itemRowPair in rows)
                {
                    if (simpleItem.ItemID == itemRowPair.Key)
                    {
                        if (!Form1.Instance.IsDisposed)
                            ListingStatusChanged(new Tuple<SimpleItemType, DataRow>(simpleItem, itemRowPair.Value));
                    }
                }
            }
        }

        public static void UpdateGrids(Dictionary<string, DataRow> rows, CoreItem[] updatedItems)
        {
            foreach (var simpleItem in updatedItems)
            {
                foreach (var itemRowPair in rows)
                {
                    if (simpleItem.ItemId.Contains(itemRowPair.Key))
                    {
                        if (!Form1.Instance.IsDisposed)
                            Form1._synchronizationContext.Post(_ =>
                            {
                                ListingStatusChanged2(new Tuple<CoreItem, DataRow>(simpleItem, itemRowPair.Value));
                            }, simpleItem.ItemId);

                    }
                }
            }
        }

        public static async void ListingStatusChanged(Tuple<SimpleItemType, DataRow> tuple)
        {
            var item = tuple.Item1;
            var row = tuple.Item2;

            if (row == null || row.RowState != DataRowState.Added)
                return;

            var d = (DataList)row["Blob"];

            if (item.TimeLeft == "PT0S") //not ended fix
            {
                if (d.TimeSinceEnded is > 60)
                {
                    if (item.ListingStatus == ListingStatusCodeType.Active)
                        item.ListingStatus = ListingStatusCodeType.Completed;
                }
            }

            // Helper method to safely update status on UI thread
            void SafeSetStatus(Data.ItemStatus status)
            {
                if (Form1.Instance?.InvokeRequired == true)
                {
                    Form1.Instance.Invoke(() => d.SetStatus(status));
                }
                else
                {
                    d.SetStatus(status);
                }
            }

            switch (item.ListingStatus)
            {
                default:
                    SafeSetStatus(Data.ItemStatus.Ended);
                    break;

                case ListingStatusCodeType.Active:

                    var itemModified = false;

                    if (d.BuyItNowAvailable != item.BuyItNowAvailable)
                    {
                        d.BuyItNowAvailable = item.BuyItNowAvailable;
                        itemModified = true;
                    }

                    if (d.Bids != item.BidCount)
                    {
                        d.Bids = item.BidCount;
                        itemModified = true;
                    }

                    if (d.ListingCodeType.Contains("Auction"))
                        if (d.ItemPricing?.AuctionPrice?.Currency == "USD")
                        {
                            if (!d.ItemPricing.AuctionPrice.Value.Equals(item.ConvertedCurrentPrice.Value))
                            {
                                d.Row["Auction Price"] = item.ConvertedCurrentPrice.Value;
                                d.ItemPricing.AuctionPrice.Value = item.ConvertedCurrentPrice.Value;
                                itemModified = true;
                            }
                        }

                    if (itemModified)
                    {
                        SafeSetStatus(Data.ItemStatus.Updated);
                    }

                    if (d.SecondsLeft is > 5)
                        SafeSetStatus(Data.ItemStatus.Active);
                    break;

                case ListingStatusCodeType.Completed:
                case ListingStatusCodeType.Ended:
                    await MarkItemCompleted(d);
                    return;
            }
        }

        public static async void ListingStatusChanged2(Tuple<CoreItem, DataRow> tuple)
        {
            var item = tuple.Item1;
            var row = tuple.Item2;

            if (row == null || row.RowState != DataRowState.Added)
                return;
            if (Debugger.IsAttached)
                row["Status Time"] = new DateTimeWithDiff(DateTime.UtcNow, null).LocalTime;
            var d = (DataList)row["Blob"];
            d.LastStatusCheck = new DateTimeWithDiff { TimeDiff = 0, Utc = DateTime.UtcNow };

            if (item.EstimatedAvailabilities == null || item.EstimatedAvailabilities.FirstOrDefault() == null)
                return;

            var itemStatus = BrowseAPIParser.ParseInitialItemStatus(item.ItemEndDate, item.EstimatedAvailabilities.FirstOrDefault());
            switch (itemStatus)
            {
                default:
                    d.SetStatus(Data.ItemStatus.Ended);
                    break;

                case Data.ItemStatus.Active:

                    var itemModified = false;

                    if (item.BidCount.HasValue)
                        if (d.Bids != item.BidCount.Value)
                        {
                            d.Bids = item.BidCount.Value;
                            itemModified = true;
                        }

                    if (d.ListingCodeType.Contains("Auction"))
                        if (item.CurrentBidPrice != null)
                        {
                            double bidValue;
                            if (string.IsNullOrEmpty(item.CurrentBidPrice.ConvertedFromValue))
                            {
                                bidValue = double.Parse(item.CurrentBidPrice.Value, CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                bidValue = double.Parse(item.CurrentBidPrice.ConvertedFromValue, CultureInfo.InvariantCulture);
                            }

                            if (!d.ItemPricing.AuctionPrice.Value.Equals(bidValue))
                            {
                                d.Row["Auction Price"] = bidValue;
                                d.ItemPricing.AuctionPrice.Value = bidValue;
                                itemModified = true;
                            }
                        }

                    if (itemModified)
                    {
                        d.SetStatus(Data.ItemStatus.Updated);
                    }

                    if (d.SecondsLeft is > 5)
                        d.SetStatus(Data.ItemStatus.Active);
                    break;
                case Data.ItemStatus.NotAvailable:
                case Data.ItemStatus.Sold:
                case Data.ItemStatus.Ended:
                    await MarkItemCompleted(d);
                    return;
            }
        }

        internal static async Task MarkItemCompleted(DataList d)
        {
            try
            {
                // Helper method to safely update status on UI thread
                void SafeSetStatus(Data.ItemStatus status)
                {
                    if (Form1.Instance?.InvokeRequired == true)
                    {
                        Form1.Instance.Invoke(() => d.SetStatus(status));
                    }
                    else
                    {
                        d.SetStatus(status);
                    }
                }

                var theItem = await ApiService.GetItemSafe(d.ItemID, ConnectionConfig.GetGuestApiContext(d.EBaySite.SiteCode));
                if (theItem == null)
                {
                    SafeSetStatus(Data.ItemStatus.Ended);
                    return;
                }

                if (theItem.SellingStatus.QuantitySold == 0 && theItem.SellingStatus.BidCount == 0)
                    SafeSetStatus(Data.ItemStatus.Ended);
                else
                    SafeSetStatus(Data.ItemStatus.Sold);

                if (theItem.ListingDetails.EndingReasonSpecified)
                {
                    DateParser.SetEndTime_Timeleft(d, DateTime.SpecifyKind(theItem.ListingDetails.EndTime, DateTimeKind.Utc));
                    d.Row["Time Left"] = d.TimeLeft;
                    if (Enum.TryParse(theItem.ListingDetails.EndingReason.ToString(), out Data.ItemStatus status))
                    {
                        SafeSetStatus(status);
                    }
                    else
                    {
                        SafeSetStatus(Data.ItemStatus.Ended);
                    }
                }

                if (d.ItemPricing?.AuctionPrice != null && theItem.SellingStatus.CurrentPrice != null)
                {
                    d.Row["Auction Price"] = theItem.SellingStatus.CurrentPrice.Value;
                    d.ItemPricing.AuctionPrice = new CurrencyAmount(theItem.SellingStatus.CurrentPrice.Value, theItem.SellingStatus.CurrentPrice.currencyID.ToString());
                }

                var endTimeUtc = theItem.ListingDetails.EndTime.ToUniversalTime();
                if (endTimeUtc > DateTime.UtcNow)
                {
                    endTimeUtc = DateTime.UtcNow;
                }

                d.SoldTime = new DateTimeWithDiff { Utc = endTimeUtc, TimeDiff = Math.Round((endTimeUtc - d.FoundTime.Utc).TotalSeconds, 0) };
                d.Row["Sold Time"] = d.SoldTime.LocalTime;

                d.HighBidder = $"{theItem.SellingStatus?.HighBidder?.UserID} {theItem.SellingStatus?.HighBidder?.FeedbackScore}";

                Analytics.ReportSoldTime(d.SoldTime);
                return;
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            d.SetStatus(Data.ItemStatus.Ended);
        }
    }
}
