using System;
using System.Collections.Generic;
using uBuyFirst.UI.Validation;
using uBuyFirst.SubSearch;
using uBuyFirst.Search;

namespace uBuyFirst
{
    /// <summary>
    /// Simple test class to verify validation system functionality
    /// </summary>
    public static class ValidationTest
    {
        public static void RunTests()
        {
            Console.WriteLine("=== Validation System Tests ===");

            TestValidationResult();
            TestValidationContext();
            TestNodeIdentifierService();
            TestTreeListValidationWarningManager();
            TestValidators();
            TestValidationServiceFactory();
            TestTreeListValidationCoordinator();

            TestValidationOnSearchStart();

            Console.WriteLine("=== All Tests Completed ===");
        }

        private static void TestValidationResult()
        {
            Console.WriteLine("Testing ValidationResult...");

            var result = new ValidationResult();

            // Test empty result
            if (!result.IsValid || result.HasWarnings || result.WarningCount != 0)
            {
                Console.WriteLine("FAIL: Empty ValidationResult should be valid");
                return;
            }

            // Test adding warnings
            result.AddWarning("Alias", "Test warning");
            result.AddWarning("Keywords", "Another warning");

            if (result.IsValid || !result.HasWarnings || result.WarningCount != 2)
            {
                Console.WriteLine("FAIL: ValidationResult with warnings should be invalid");
                return;
            }

            if (!result.HasWarningsForColumn("Alias") || !result.HasWarningsForColumn("Keywords"))
            {
                Console.WriteLine("FAIL: ValidationResult should have warnings for specific columns");
                return;
            }

            Console.WriteLine("PASS: ValidationResult tests passed");
        }

        private static void TestValidationContext()
        {
            Console.WriteLine("Testing ValidationContext...");

            var context = ValidationContext.ForNode("test_node");

            if (context.NodeId != "test_node")
            {
                Console.WriteLine("FAIL: ValidationContext NodeId not set correctly");
                return;
            }

            // Test properties
            context.SetProperty("TestKey", "TestValue");

            if (!context.HasProperty("TestKey"))
            {
                Console.WriteLine("FAIL: ValidationContext should have property");
                return;
            }

            var value = context.GetProperty<string>("TestKey");
            if (value != "TestValue")
            {
                Console.WriteLine("FAIL: ValidationContext property value incorrect");
                return;
            }

            Console.WriteLine("PASS: ValidationContext tests passed");
        }

        private static void TestNodeIdentifierService()
        {
            Console.WriteLine("Testing NodeIdentifierService...");

            var keyword = new Keyword2Find { Alias = "test", Kws = "test" };
            var childTerm = new ChildTerm { Alias = "child", Keywords = "child" };
            var folder = new KeywordFolder { Name = "folder" };

            var keywordId = NodeIdentifierService.GetNodeId(keyword);
            var childTermId = NodeIdentifierService.GetNodeId(childTerm);
            var folderId = NodeIdentifierService.GetNodeId(folder);

            if (!keywordId.StartsWith("keyword_") ||
                !childTermId.StartsWith("childterm_") ||
                !folderId.StartsWith("folder_"))
            {
                Console.WriteLine("FAIL: NodeIdentifierService not generating correct prefixes");
                return;
            }

            if (keywordId == childTermId || keywordId == folderId || childTermId == folderId)
            {
                Console.WriteLine("FAIL: NodeIdentifierService generating duplicate IDs");
                return;
            }

            Console.WriteLine("PASS: NodeIdentifierService tests passed");
        }

        private static void TestTreeListValidationWarningManager()
        {
            Console.WriteLine("Testing TreeListValidationWarningManager...");

            var manager = new TreeListValidationWarningManager();

            // Test setting and getting warnings
            manager.SetWarning("node1", "Alias", "Test warning");

            if (!manager.HasWarning("node1", "Alias"))
            {
                Console.WriteLine("FAIL: Warning manager should have warning");
                return;
            }

            var warning = manager.GetWarning("node1", "Alias");
            if (warning != "Test warning")
            {
                Console.WriteLine("FAIL: Warning manager returned incorrect warning");
                return;
            }

            // Test integration with ValidationResult
            var result = new ValidationResult();
            result.AddWarning("Keywords", "Keywords warning");

            manager.SetWarningsFromResult("node2", result);

            if (!manager.HasWarning("node2", "Keywords"))
            {
                Console.WriteLine("FAIL: Warning manager should integrate with ValidationResult");
                return;
            }

            Console.WriteLine("PASS: TreeListValidationWarningManager tests passed");
        }

        private static void TestValidators()
        {
            Console.WriteLine("Testing Validators...");

            // Test Keyword2FindValidator
            var keywordValidator = new Keyword2FindValidator();
            var keyword = new Keyword2Find { Alias = "", Kws = "test" };
            var context = ValidationContext.ForNode("test");

            var result = keywordValidator.Validate(keyword, context);
            if (!result.HasWarningsForColumn("Alias"))
            {
                Console.WriteLine("FAIL: Keyword2FindValidator should validate empty alias");
                return;
            }

            // Test ChildTermValidator
            var childTermValidator = new ChildTermValidator();
            var childTerm = new ChildTerm { Alias = "test", Keywords = "" };

            result = childTermValidator.Validate(childTerm, context);
            if (!result.HasWarningsForColumn("Keywords"))
            {
                Console.WriteLine("FAIL: ChildTermValidator should validate empty keywords");
                return;
            }

            // Test KeywordFolderValidator
            var folderValidator = new KeywordFolderValidator();
            var folder = new KeywordFolder { Name = "" };

            result = folderValidator.Validate(folder, context);
            if (!result.HasWarningsForColumn("Alias"))
            {
                Console.WriteLine("FAIL: KeywordFolderValidator should validate empty name");
                return;
            }

            Console.WriteLine("PASS: Validator tests passed");
        }

        private static void TestValidationServiceFactory()
        {
            Console.WriteLine("Testing ValidationServiceFactory...");

            var factory = ValidationServiceFactory.Instance;

            if (factory.ValidatorCount < 3)
            {
                Console.WriteLine("FAIL: ValidationServiceFactory should have default validators");
                return;
            }

            // Test getting validators
            var keyword = new Keyword2Find();
            var validator = factory.GetValidator(keyword);

            if (validator == null || !validator.CanValidate(keyword))
            {
                Console.WriteLine("FAIL: ValidationServiceFactory should return appropriate validator");
                return;
            }

            Console.WriteLine("PASS: ValidationServiceFactory tests passed");
        }

        private static void TestTreeListValidationCoordinator()
        {
            Console.WriteLine("Testing TreeListValidationCoordinator...");

            var coordinator = new TreeListValidationCoordinator();

            if (coordinator.WarningManager == null)
            {
                Console.WriteLine("FAIL: TreeListValidationCoordinator should have warning manager");
                return;
            }

            Console.WriteLine("PASS: TreeListValidationCoordinator tests passed");
        }

        private static void TestValidationOnSearchStart()
        {
            Console.WriteLine("Testing Validation on Search Start...");

            // Create a coordinator and some test keywords
            var coordinator = new TreeListValidationCoordinator();

            // Test keyword with validation issues
            var invalidKeyword = new Keyword2Find
            {
                Alias = "", // Empty alias should cause validation error
                Kws = "test",
                KeywordEnabled = CheckState.Checked
            };

            var validKeyword = new Keyword2Find
            {
                Alias = "valid_keyword",
                Kws = "test keyword",
                KeywordEnabled = CheckState.Checked
            };

            // Validate the keywords
            var context = ValidationContext.ForNode("test_node");
            var validator = new Keyword2FindValidator();

            var invalidResult = validator.Validate(invalidKeyword, context);
            var validResult = validator.Validate(validKeyword, context);

            if (!invalidResult.HasWarnings)
            {
                Console.WriteLine("FAIL: Invalid keyword should have validation warnings");
                return;
            }

            if (validResult.HasWarnings)
            {
                Console.WriteLine("FAIL: Valid keyword should not have validation warnings");
                return;
            }

            // Test that validation coordinator can detect issues
            coordinator.WarningManager.SetWarningsFromResult("invalid_node", invalidResult);
            coordinator.WarningManager.SetWarningsFromResult("valid_node", validResult);

            if (!coordinator.WarningManager.HasWarning("invalid_node", "Alias"))
            {
                Console.WriteLine("FAIL: Coordinator should detect validation warnings");
                return;
            }

            if (coordinator.WarningManager.HasWarning("valid_node", "Alias"))
            {
                Console.WriteLine("FAIL: Coordinator should not have warnings for valid keyword");
                return;
            }

            Console.WriteLine("PASS: Validation on Search Start tests passed");
        }
    }
}
