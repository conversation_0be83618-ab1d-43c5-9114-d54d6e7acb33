﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Menu;
using DevExpress.XtraBars;
using uBuyFirst.Services;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;
using System;
using DevExpress.Utils.Menu;

namespace uBuyFirst.Tests.Services
{
    /// <summary>
    /// Tests for the TreeListContextMenuBuilder service
    /// </summary>
    [TestClass]
    public class TreeListContextMenuBuilderTests
    {
        private Mock<TreeList> _mockTreeList;
        private MenuEventHandlers _eventHandlers;
        private MenuResources _resources;
        private TreeListContextMenuBuilder _builder;
        private Mock<TreeListNodeMenu> _mockNodeMenu;

        [TestInitialize]
        public void Setup()
        {
            _mockTreeList = new Mock<TreeList>();
            _eventHandlers = CreateMockEventHandlers();
            _resources = CreateMockResources();
            _builder = new TreeListContextMenuBuilder(_mockTreeList.Object, _eventHandlers, _resources);

            _mockNodeMenu = new Mock<TreeListNodeMenu>();
            _mockNodeMenu.Setup(m => m.Items).Returns(new DXMenuItemCollection(_mockNodeMenu.Object));
        }

        [TestMethod]
        public void Constructor_WithNullTreeList_ShouldThrowArgumentNullException()
        {
            Assert.ThrowsException<ArgumentNullException>(() =>
                new TreeListContextMenuBuilder(null, _eventHandlers, _resources));
        }

        [TestMethod]
        public void Constructor_WithNullEventHandlers_ShouldThrowArgumentNullException()
        {
            Assert.ThrowsException<ArgumentNullException>(() =>
                new TreeListContextMenuBuilder(_mockTreeList.Object, null, _resources));
        }

        [TestMethod]
        public void Constructor_WithNullResources_ShouldThrowArgumentNullException()
        {
            Assert.ThrowsException<ArgumentNullException>(() =>
                new TreeListContextMenuBuilder(_mockTreeList.Object, _eventHandlers, null));
        }

        [TestMethod]
        public void BuildMenuForDataRecord_WithKeywordFolder_ShouldAddFolderMenuItems()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Test Folder" };

            // Act
            _builder.BuildMenuForDataRecord(_mockNodeMenu.Object, folder);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(2, items.Count, "Should add 2 menu items for folder");
            Assert.AreEqual("Add eBay Search", items[0].Caption);
            Assert.AreEqual("New Folder", items[1].Caption);
        }

        [TestMethod]
        public void BuildMenuForDataRecord_WithKeyword2Find_ShouldAddKeywordMenuItems()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "Test Keyword" };

            // Act
            _builder.BuildMenuForDataRecord(_mockNodeMenu.Object, keyword);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(4, items.Count, "Should add 4 menu items for keyword");
            Assert.AreEqual("Add eBay Search (Insert)", items[0].Caption);
            Assert.AreEqual("Duplicate eBay Search", items[1].Caption);
            Assert.AreEqual("New Sub Search (Insert)", items[2].Caption);
            Assert.AreEqual("New Folder", items[3].Caption);
        }

        [TestMethod]
        public void BuildMenuForDataRecord_WithChildTerm_ShouldAddChildTermMenuItems()
        {
            // Arrange
            var parentKeyword = new Keyword2Find { Alias = "Parent" };
            var childTerm = new ChildTerm(parentKeyword, "Test Child Term");

            // Act
            _builder.BuildMenuForDataRecord(_mockNodeMenu.Object, childTerm);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(2, items.Count, "Should add 2 menu items for child term");
            Assert.AreEqual("New Sub Search (Insert)", items[0].Caption);
            Assert.AreEqual("Duplicate Sub Search", items[1].Caption);
        }

        [TestMethod]
        public void BuildMenuForDataRecord_WithUnknownType_ShouldAddDefaultMenuItems()
        {
            // Arrange
            var unknownObject = "Unknown Type";

            // Act
            _builder.BuildMenuForDataRecord(_mockNodeMenu.Object, unknownObject);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(1, items.Count, "Should add 1 default menu item");
            Assert.AreEqual("Add eBay Search (Insert)", items[0].Caption);
        }

        [TestMethod]
        public void AddCommonMenuItems_ShouldAddEnableDisableMenuItems()
        {
            // Act
            _builder.AddCommonMenuItems(_mockNodeMenu.Object);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(2, items.Count, "Should add 2 common menu items");
            Assert.AreEqual("Enable Selected", items[0].Caption);
            Assert.AreEqual("Disable Selected", items[1].Caption);
            Assert.IsTrue(items[0].BeginGroup, "First item should begin a group");
        }

        [TestMethod]
        public void AddOutOfStockMenuItems_WithOutOfStockKeyword_ShouldAddWatchlistMenuItem()
        {
            // Arrange
            var keyword = new Keyword2Find
            {
                Alias = "Test Keyword",
                ListingType = new ListingType[] { ListingType.OutOfStock }
            };

            // Act
            _builder.AddOutOfStockMenuItems(_mockNodeMenu.Object, keyword);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(1, items.Count, "Should add 1 OutOfStock menu item");
            Assert.AreEqual("Import and replace ItemIDs from Ebay Watchlist", items[0].Caption);
        }

        [TestMethod]
        public void AddOutOfStockMenuItems_WithRegularKeyword_ShouldNotAddMenuItem()
        {
            // Arrange
            var keyword = new Keyword2Find
            {
                Alias = "Test Keyword",
                ListingType = new ListingType[] { ListingType.BuyItNow }
            };

            // Act
            _builder.AddOutOfStockMenuItems(_mockNodeMenu.Object, keyword);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(0, items.Count, "Should not add any menu items for regular keyword");
        }

        [TestMethod]
        public void AddDeleteMenuItem_ShouldAddDeleteMenuItemWithSelectionCount()
        {
            // Arrange
            var mockSelection = new Mock<TreeListSelection>();
            mockSelection.Setup(s => s.Count).Returns(3);
            _mockTreeList.Setup(t => t.Selection).Returns(mockSelection.Object);

            // Act
            _builder.AddDeleteMenuItem(_mockNodeMenu.Object);

            // Assert
            var items = _mockNodeMenu.Object.Items;
            Assert.AreEqual(1, items.Count, "Should add 1 delete menu item");
            Assert.AreEqual("Delete [3] item(s)", items[0].Caption);
            Assert.IsTrue(items[0].BeginGroup, "Delete item should begin a group");
        }

        [TestMethod]
        public void BuildEmptySpaceMenu_ShouldAddNewFolderMenuItem()
        {
            // Arrange
            var mockTreeListMenu = new Mock<TreeListMenu>();
            mockTreeListMenu.Setup(m => m.Items).Returns(new DXMenuItemCollection(mockTreeListMenu.Object));

            // Act
            _builder.BuildEmptySpaceMenu(mockTreeListMenu.Object);

            // Assert
            var items = mockTreeListMenu.Object.Items;
            Assert.AreEqual(1, items.Count, "Should add 1 menu item for empty space");
            Assert.AreEqual("New Folder", items[0].Caption);
        }

        #region Helper Methods

        private MenuEventHandlers CreateMockEventHandlers()
        {
            return new MenuEventHandlers
            {
                NewEBaySearch = (s, e) => { },
                NewFolder = (s, e) => { },
                NewFolderSameLevel = (s, e) => { },
                NewRootFolder = (s, e) => { },
                NewSubSearch = (s, e) => { },
                NewCopy = (s, e) => { },
                CheckSelected = (s, e) => { },
                UnCheckSelected = (s, e) => { },
                AddFromWatchList = (s, e) => { },
                Delete = (s, e) => { }
            };
        }

        private MenuResources CreateMockResources()
        {
            return new MenuResources
            {
                Add = new object(),
                NewFolder = new object(),
                Duplicate = new object(),
                AddTaskList = new object(),
                CheckboxCheck = new object(),
                SearchUncheck = new object(),
                Remove = new object()
            };
        }

        #endregion
    }
}
