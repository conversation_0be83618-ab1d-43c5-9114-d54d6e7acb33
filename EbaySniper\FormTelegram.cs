﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using uBuyFirst.Prefs;
using uBuyFirst.Telegram;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class FormTelegram : RibbonForm
    {
        public List<Keyword2Find> ebaySearches;
        public TelegramSender TelegramTmp;

        public FormTelegram()
        {
            InitializeComponent();
        }

        private async void FormTelegram_Shown(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(TelegramTmp.BodyTemplate))
                memoBodyTemplate.Text = TelegramTmp.BodyTemplate.Replace("\r\n", "\n").Replace("\n", "\r\n");

            chkSendNotifications.Checked = TelegramTmp.Enabled;
            spinEditMaxMessagesPerMinute.EditValue = TelegramTmp.MaxMessagesPerMinute;
            spinEditNumberOfPicturesToAttach.EditValue = TelegramTmp.PicturesCountToAttach;
            if (TelegramTmp != null)
            {
                if (!string.IsNullOrEmpty(TelegramTmp.TelegramBotID))
                {
                    try
                    {
                        txtToken.Text = TelegramTmp.TelegramBotID;
                        txtTelegramAccount.Text = TelegramTmp.TelegramAccount;

                        txtToken.Text = txtToken.Text.Trim();
                        lblBotStatusValue.Text = TelegramTmp.GetBotState();

                        if (string.IsNullOrEmpty(txtToken.Text))
                            return;
                        //var result = Task.Run(() => { return TelegramTmp.ApplyBotToken(txtToken.Text); }).Result;
                        await TelegramTmp.ApplyBotToken(txtToken.Text).ContinueWith(a =>
                        {
                            if (a.Exception != null)
                            {
                                a.Exception.Handle(ex =>
                                {
                                    HandleException(ex);
                                    return true;
                                });
                            }
                            else
                            {
                                lblBotStatusValue.Text = TelegramTmp.GetBotState();
                            }
                        }, TaskScheduler.FromCurrentSynchronizationContext());

                        await TelegramTmp.ConnectTelegramAccount(txtTelegramAccount.Text).ContinueWith(a =>
                        {
                            if (a.Exception != null)
                            {
                                a.Exception.Handle(ex =>
                                {
                                    HandleException(ex);
                                    return true;
                                });
                            }
                        }, TaskScheduler.FromCurrentSynchronizationContext());
                        lblStatusValue.Text = TelegramTmp.AccountConnectionState;
                    }
                    catch (Exception ex)
                    {
                        HandleException(ex);
                    }
                }
            }

            CreateMenuColumnsNames();
        }

        private void HandleException(Exception ex)
        {

            if (ex.Message == "Forbidden: bot was blocked by the user" || ex.Message == "chat not found" || ex.Message == "Unauthorized")
                return;

            if (TelegramSender.HandleTelegramException(ex, TelegramTmp.GetBotName()))
            {
                return;
            }

            if (ex.Message.Contains("Too Many Requests"))
            {
                XtraMessageBox.Show("Telegram message limit reached. Please, try again later.");

                return;
            }

            if (Debugger.IsAttached)
                XtraMessageBox.Show(ex.Message);
            else
                throw ex;
        }

        public bool HandleTelegramException(Exception ex, string botName)
        {
            if (ex.Message.Contains("Forbidden: bot was blocked by the user"))
            {
                var msgText = "Telegram bot " + botName + " is blocked. To unblock - send any message to bot " + botName + " from your telegram account.";

                XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("chat not found"))
            {
                var msgText = "To activate your bot: Send any message to bot " + botName + " from your telegram account.";
                XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("Unauthorized"))
            {
                var msgText = "Telegram HTTP API token is invalid. Provide a valid token.";
                //XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("Bad Request: group send failed"))
            {
                return true;
            }

            if (ex.Message.Contains("Not a valid InputFile"))
            {
                return true;
            }

            if (ex.Message.Contains("Bad Request: failed to get HTTP URL content"))
            {
                return true;
            }

            return false;
        }

        private async void btnApplyToken_Click(object sender, EventArgs e)
        {
            txtToken.Text = txtToken.Text.Trim();
            lblBotStatusValue.Text = TelegramTmp.GetBotState();
            if (string.IsNullOrEmpty(txtToken.Text) || txtToken.Text.Length < 30)
            {
                XtraMessageBox.Show("Please, provide a valid telegram bot HTTP API token");

                return;
            }

            await TelegramTmp.ApplyBotToken(txtToken.Text);
            lblBotStatusValue.Text = TelegramTmp.GetBotState();
            await TelegramTmp.ConnectTelegramAccount(txtTelegramAccount.Text);
            lblStatusValue.Text = TelegramTmp.AccountConnectionState;
        }

        private async void btnConnect_Click(object sender, EventArgs e)
        {
            try
            {
                txtTelegramAccount.Text = txtTelegramAccount.Text.Trim().Trim('@');
                if (txtTelegramAccount.Text.Length < 6)
                {
                    XtraMessageBox.Show("Please, provide your Telegram username.");
                }

                await TelegramTmp.ConnectTelegramAccount(txtTelegramAccount.Text);

                lblStatusValue.Text = TelegramTmp.AccountConnectionState;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                btnSave.Enabled = false;
                btnTestMessage.Enabled = false;
            }
        }

        private void simpleTestMessage_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(memoBodyTemplate.Text))
            {
                MessageBox.Show("Body template should not be empty");

                return;
            }

            var columnsOk = TelegramTmp.SetBody(memoBodyTemplate.Text.Replace("\r\n", "\n").Replace("\n", "\r\n"));
            if (columnsOk)
            {
                var (row, _) = Helpers.GetRandomRow(ebaySearches);
                if (row == null)
                {
                    XtraMessageBox.Show("To send a test message, please, get some results first. Click \"Start\" button");

                    return;
                }
            }
            else
            {
                return;
            }

            var (row2, _) = Helpers.GetRandomRow(ebaySearches);
            if (row2 != null)
                TelegramTmp.PushItem(row2);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            TelegramTmp.Enabled = chkSendNotifications.Checked;
            TelegramTmp.SetMaxMessagesPerMinute((int)spinEditMaxMessagesPerMinute.Value);
            TelegramTmp.PicturesCountToAttach = (int)spinEditNumberOfPicturesToAttach.Value;
            TelegramTmp.TelegramBotID = txtToken.Text;

            if (string.IsNullOrEmpty(TelegramTmp.TelegramBotID))
            {
                TelegramTmp.Enabled = false;
            }

            if (!string.IsNullOrEmpty(txtTelegramAccount.Text))
            {
                if (ApplyTelegramAccount())
                    return;
            }

            var columnsOk = TelegramTmp.SetBody(memoBodyTemplate.Text);
            if (!columnsOk)
            {
                XtraMessageBox.Show("Couldn't parse your Title or Body template.");

                return;
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private bool ApplyTelegramAccount()
        {
            if (txtTelegramAccount.Text.Length < 6)
            {
                XtraMessageBox.Show("Please, provide your Telegram account name.");

                return true;
            }

            TelegramTmp.TelegramAccount = txtTelegramAccount.Text.Trim().Trim('@');

            return false;
        }

        private void CreateMenuColumnsNames()
        {
            popupMenuColumnNames.ItemLinks.Clear();

            var allowedColumns = ColumnsManager.GetAllowedColumnsTelegram();
            allowedColumns.Insert(0, "ViewUrl");
            allowedColumns.Insert(0, "CheckoutUrl");
            allowedColumns.Insert(0, "ContactUrl");
            allowedColumns.Remove("Term");
            if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("1BE9-4A48"))
            {
                allowedColumns.Insert(0, "ItemID");
            }

            foreach (string s in allowedColumns)
            {
                var item = new BarButtonItem();
                item.Caption = $"{{{s}}}";
                item.ItemClick += OnItemClick;
                popupMenuColumnNames.ItemLinks.Add(item);
            }
        }

        private void OnItemClick(object sender, ItemClickEventArgs e)
        {
            if (!(sender is RibbonBarManager))
                return;

            BarButtonItem item = e.Item as BarButtonItem;

            var inserText = item.Caption + " ";
            var selectionIndex = memoBodyTemplate.SelectionStart;
            memoBodyTemplate.Focus();
            memoBodyTemplate.Text = memoBodyTemplate.Text.Replace("\r\n", "\n").Replace("\n", "\r\n").Insert(selectionIndex, inserText);
            memoBodyTemplate.SelectionStart = selectionIndex + inserText.Length;
        }

        private void popupMenuColumnNames_BeforePopup(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var menu = sender as PopupMenu;
            if (menu?.Activator is Control control)
            {
                if (control.Focused)
                {
                    if (memoBodyTemplate.Text == control.Text)
                    {
                        memoBodyTemplate.Tag = "Focused";
                    }
                    else
                    {
                        memoBodyTemplate.Tag = null;
                    }
                }
                else
                {
                    e.Cancel = true;
                }
            }
        }

        private void lnkTelegramHelp_Click(object sender, EventArgs e)
        {
            Process.Start("https://ubuyfirst.com/docs/telegram-setup/");
        }

        private void txtTelegramAccount_Properties_EditValueChanged(object sender, EventArgs e)
        {
            var textEdit = (TextEdit)sender;
            textEdit.Text = textEdit.Text.Trim().Trim('@').Trim();
        }

        private void btnTelegramInstruction_Click(object sender, EventArgs e)
        {
            var sea = new ToolTipControllerShowEventArgs();
            sea.SuperTip = btnTelegramInstruction.SuperTip;
            sea.ToolTipType = ToolTipType.SuperTip;
            sea.AutoHide = false;

            toolTipController1.ShowHint(sea);
        }

        private void btnTelegramAccount_Click(object sender, EventArgs e)
        {
            var sea = new ToolTipControllerShowEventArgs();
            sea.SuperTip = btnTelegramAccount.SuperTip;
            sea.ToolTipType = ToolTipType.SuperTip;
            sea.AutoHide = false;

            toolTipController1.ShowHint(sea);
        }

        private void lnkTelegram_Click(object sender, EventArgs e)
        {
            Process.Start("https://telegram.org/");
        }

        private void lblPushbulletToken_Click(object sender, EventArgs e)
        {
            Process.Start("https://t.me/BotFather");
        }
        private void spinEditNumberOfPicturesToAttach_EditValueChanged(object sender, EventArgs e)
        {
            var spinEdit = (SpinEdit)sender;
            TelegramTmp.PicturesCountToAttach = (int)spinEdit.Value;
        }

        private void spinEditMaxMessagesPerMinute_EditValueChanged(object sender, EventArgs e)
        {
            var spinEdit = (SpinEdit)sender;
            TelegramTmp.SetMaxMessagesPerMinute((int)spinEdit.Value);
        }
    }
}
