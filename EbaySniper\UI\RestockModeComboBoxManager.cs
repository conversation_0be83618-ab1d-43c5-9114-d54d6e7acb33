﻿using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using uBuyFirst.Prefs;

namespace uBuyFirst.UI
{
    /// <summary>
    /// Manages the restock mode combo box functionality including initialization,
    /// custom drawing, and value change handling
    /// </summary>
    public class RestockModeComboBoxManager
    {
        private readonly RepositoryItemComboBox _repositoryItemComboBox;
        private readonly BarEditItem _barEditItem;

        /// <summary>
        /// Event fired when the restock mode changes
        /// </summary>
        public event EventHandler<RestockModeEnum> RestockModeChanged;

        /// <summary>
        /// Initializes a new instance of the RestockModeComboBoxManager
        /// </summary>
        /// <param name="repositoryItemComboBox">The repository item combo box control</param>
        /// <param name="barEditItem">The bar edit item that uses the combo box</param>
        public RestockModeComboBoxManager(RepositoryItemComboBox repositoryItemComboBox, BarEditItem barEditItem)
        {
            _repositoryItemComboBox = repositoryItemComboBox ?? throw new ArgumentNullException(nameof(repositoryItemComboBox));
            _barEditItem = barEditItem ?? throw new ArgumentNullException(nameof(barEditItem));
        }

        /// <summary>
        /// Initializes the combo box with restock mode options and sets up event handlers
        /// </summary>
        public void Initialize()
        {
            // Clear existing items and add the three restock mode options
            _repositoryItemComboBox.Items.Clear();

            // Add items as simple strings - we'll map values by index
            _repositoryItemComboBox.Items.Add("⏸ Disabled");
            _repositoryItemComboBox.Items.Add("🧪 Test Mode");
            _repositoryItemComboBox.Items.Add("$ Live Mode");

            // Set up custom drawing for colored dropdown items
            _repositoryItemComboBox.DrawItem += OnDrawItem;

            // Also try setting up appearance properties on the repository item
            _repositoryItemComboBox.AppearanceDropDown.Options.UseBackColor = true;
            _repositoryItemComboBox.AppearanceDropDown.Options.UseForeColor = true;

            // Set the current value based on UserSettings.RestockMode (select by index)
            SetCurrentValue(UserSettings.RestockMode);

            // Set the initial appearance to match the current mode
            UpdateDisplayAppearance(UserSettings.RestockMode);
        }

        /// <summary>
        /// Sets the current value of the combo box based on the restock mode
        /// </summary>
        /// <param name="mode">The restock mode to set</param>
        public void SetCurrentValue(RestockModeEnum mode)
        {
            var modeIndex = (int)mode;
            if (modeIndex >= 0 && modeIndex < _repositoryItemComboBox.Items.Count)
            {
                _barEditItem.EditValue = _repositoryItemComboBox.Items[modeIndex];
            }
        }

        /// <summary>
        /// Handles the value changed event for the combo box
        /// </summary>
        /// <param name="sender">The sender object</param>
        /// <param name="e">Event arguments</param>
        public void OnValueChanged(object sender, EventArgs e)
        {
            if (sender is BarEditItem barEditItem && barEditItem.EditValue != null)
            {
                // ComboBox returns the selected string item, find its index to map to enum
                var selectedText = barEditItem.EditValue.ToString();
                var modeIndex = _repositoryItemComboBox.Items.IndexOf(selectedText);

                if (modeIndex >= 0 && modeIndex <= 2) // Valid enum range
                {
                    var newMode = (RestockModeEnum)modeIndex;

                    // Show confirmation dialog when switching to Live Mode
                    if (newMode == RestockModeEnum.LiveMode && UserSettings.RestockMode != RestockModeEnum.LiveMode)
                    {
                        var result = XtraMessageBox.Show(
                            "⚠️ WARNING: You are switching to Live Mode!\n\n" +
                            "Live Mode will perform ACTUAL PURCHASES with REAL MONEY.\n" +
                            "Make sure you have:\n" +
                            "• Set appropriate daily spend limits\n" +
                            "• Configured quantity limits correctly\n\n" +
                            "Are you sure you want to enable Live Mode?",
                            "Live Mode Confirmation",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Warning);

                        if (result != DialogResult.Yes)
                        {
                            // User cancelled - revert to previous mode
                            SetCurrentValue(UserSettings.RestockMode);
                            return;
                        }
                    }

                    UserSettings.RestockMode = newMode;

                    // Update the displayed value's appearance to match the selected mode
                    UpdateDisplayAppearance(newMode);

                    // Fire the event to notify subscribers of the mode change
                    RestockModeChanged?.Invoke(this, newMode);
                }
            }
        }

        /// <summary>
        /// Custom draw handler for combo box items with colored backgrounds
        /// </summary>
        /// <param name="sender">The sender object</param>
        /// <param name="e">Draw item event arguments</param>
        private void OnDrawItem(object sender, DevExpress.XtraEditors.ListBoxDrawItemEventArgs e)
        {
            if (e.Index < 0 || e.Item == null)
                return;

            // Get the RestockMode from the item index (maps directly to enum values)
            var mode = (RestockModeEnum)e.Index;

            // Get colors for the mode
            var (backColor, foreColor) = GetModeColors(mode);

            // Fill the entire background manually
            using (var brush = new SolidBrush(backColor))
            {
                e.Graphics.FillRectangle(brush, e.Bounds);
            }

            // Draw the text manually
            using (var textBrush = new SolidBrush(foreColor))
            {
                var textRect = new Rectangle(e.Bounds.X + 4, e.Bounds.Y, e.Bounds.Width - 8, e.Bounds.Height);
                var stringFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center
                };
                // Use the control's font or a default font
                var font = _repositoryItemComboBox.Appearance.Font ?? SystemFonts.DefaultFont;
                e.Graphics.DrawString(e.Item.ToString(), font, textBrush, textRect, stringFormat);
            }

            // Draw focus rectangle if needed
            if ((e.State & DrawItemState.Focus) == DrawItemState.Focus)
            {
                ControlPaint.DrawFocusRectangle(e.Graphics, e.Bounds);
            }

            // Mark as handled to prevent default drawing
            e.Handled = true;
        }

        /// <summary>
        /// Updates the display appearance of the combo box based on the selected mode
        /// </summary>
        /// <param name="mode">The restock mode</param>
        private void UpdateDisplayAppearance(RestockModeEnum mode)
        {
            // Get colors for the mode
            var (backColor, foreColor) = GetModeColors(mode);

            // Also update the repository item appearance for consistency
            _repositoryItemComboBox.Appearance.BackColor = backColor;
            _repositoryItemComboBox.Appearance.ForeColor = foreColor;
            _repositoryItemComboBox.Appearance.Options.UseBackColor = true;
            _repositoryItemComboBox.Appearance.Options.UseForeColor = true;
        }

        /// <summary>
        /// Gets the background and foreground colors for a specific restock mode
        /// </summary>
        /// <param name="mode">The restock mode</param>
        /// <returns>A tuple containing the background and foreground colors</returns>
        private static (Color backColor, Color foreColor) GetModeColors(RestockModeEnum mode)
        {
            return mode switch
            {
                RestockModeEnum.Disabled => (Color.LightGray, Color.DarkGray),
                RestockModeEnum.TestMode => (Color.FromArgb(255, 193, 7), Color.Black), // Bootstrap warning yellow
                RestockModeEnum.LiveMode => (Color.FromArgb(40, 167, 69), Color.White), // Bootstrap success green
                _ => (Color.White, Color.Black)
            };
        }

        /// <summary>
        /// Cleans up event handlers when the manager is no longer needed
        /// </summary>
        public void Dispose()
        {
            _repositoryItemComboBox.DrawItem -= OnDrawItem;
        }
    }
}
