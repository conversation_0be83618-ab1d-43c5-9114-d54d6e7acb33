﻿using System;
using System.Linq;
using DevExpress.Utils.Menu;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Menu;
using DevExpress.XtraBars;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Services
{
    /// <summary>
    /// Service for building context menus for TreeList items based on their data type
    /// </summary>
    public class TreeListContextMenuBuilder
    {
        #region Dependencies

        private readonly TreeList _treeList;
        private readonly MenuEventHandlers _eventHandlers;
        private readonly MenuResources _resources;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the TreeListContextMenuBuilder
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="eventHandlers">Event handlers for menu item clicks</param>
        /// <param name="resources">Resources for menu item icons</param>
        public TreeListContextMenuBuilder(
            TreeList treeList,
            MenuEventHandlers eventHandlers,
            MenuResources resources)
        {
            _treeList = treeList ?? throw new ArgumentNullException(nameof(treeList));
            _eventHandlers = eventHandlers ?? throw new ArgumentNullException(nameof(eventHandlers));
            _resources = resources ?? throw new ArgumentNullException(nameof(resources));
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Builds context menu for a specific data record type
        /// </summary>
        /// <param name="menu">The menu to add items to</param>
        /// <param name="dataRecord">The data record to build menu for</param>
        public void BuildMenuForDataRecord(TreeListNodeMenu menu, object dataRecord)
        {
            switch (dataRecord)
            {
                case KeywordFolder folder:
                    AddFolderMenuItems(menu, folder);
                    break;
                case Keyword2Find keyword:
                    AddKeywordMenuItems(menu, keyword);
                    break;
                case ChildTerm childTerm:
                    AddChildTermMenuItems(menu, childTerm);
                    break;
                default:
                    AddDefaultMenuItems(menu);
                    break;
            }
        }

        /// <summary>
        /// Adds common menu items that appear for all data types
        /// </summary>
        /// <param name="menu">The menu to add items to</param>
        public void AddCommonMenuItems(TreeListNodeMenu menu)
        {
            // Enable/Disable items
            var menuCheckSelected = CreateMenuItem("Enable Selected", _eventHandlers.CheckSelected, _resources.CheckboxCheck);
            menuCheckSelected.BeginGroup = true;
            menu.Items.Add(menuCheckSelected);

            var menuUnCheckSelected = CreateMenuItem("Disable Selected", _eventHandlers.UnCheckSelected, _resources.SearchUncheck);
            menu.Items.Add(menuUnCheckSelected);
        }

        /// <summary>
        /// Adds special menu items for OutOfStock keywords
        /// </summary>
        /// <param name="menu">The menu to add items to</param>
        /// <param name="keyword">The keyword to check for OutOfStock type</param>
        public void AddOutOfStockMenuItems(TreeListNodeMenu menu, Keyword2Find keyword)
        {
            if (keyword.ListingType.Contains(ListingType.OutOfStock))
            {
                var menuAddFromWatchList = CreateMenuItem(
                    "Import and replace ItemIDs from Ebay Watchlist",
                    _eventHandlers.AddFromWatchList);
                menu.Items.Add(menuAddFromWatchList);
            }
        }

        /// <summary>
        /// Adds delete menu item with selection count
        /// </summary>
        /// <param name="menu">The menu to add items to</param>
        public void AddDeleteMenuItem(TreeListNodeMenu menu)
        {
            var selectionCount = _treeList.Selection.Count;
            var menuDelete = CreateMenuItem(
                $"Delete [{selectionCount}] item(s)",
                _eventHandlers.Delete,
                _resources.Remove);
            menuDelete.BeginGroup = true;
            menu.Items.Add(menuDelete);
        }

        /// <summary>
        /// Builds menu for empty space (TreeListMenu)
        /// </summary>
        /// <param name="menu">The TreeListMenu to add items to</param>
        public void BuildEmptySpaceMenu(TreeListMenu menu)
        {
            var menuNewFolder = CreateMenuItem("New Folder", _eventHandlers.NewRootFolder, _resources.NewFolder);
            menu.Items.Add(menuNewFolder);
        }

        #endregion

        #region Private Methods - Type-Specific Menu Builders

        /// <summary>
        /// Adds menu items specific to KeywordFolder
        /// </summary>
        private void AddFolderMenuItems(TreeListNodeMenu menu, KeywordFolder folder)
        {
            var menuNewKeyword = CreateMenuItem("Add eBay Search", _eventHandlers.NewEBaySearch, _resources.Add);
            menuNewKeyword.BeginGroup = true;
            menu.Items.Add(menuNewKeyword);

            var menuNewFolder = CreateMenuItem("New Folder", _eventHandlers.NewFolder, _resources.NewFolder);
            menuNewFolder.BeginGroup = true;
            menu.Items.Add(menuNewFolder);
        }

        /// <summary>
        /// Adds menu items specific to Keyword2Find
        /// </summary>
        private void AddKeywordMenuItems(TreeListNodeMenu menu, Keyword2Find keyword)
        {
            var menuNewEBaySearch = CreateMenuItem("Add eBay Search (Insert)", _eventHandlers.NewEBaySearch, _resources.Add);
            menu.Items.Add(menuNewEBaySearch);

            var menuDuplicate = CreateMenuItem("Duplicate eBay Search", _eventHandlers.NewCopy, _resources.Duplicate);
            menu.Items.Add(menuDuplicate);

            var menuNewSubSearch = CreateMenuItem("New Sub Search (Insert)", _eventHandlers.NewSubSearch, _resources.AddTaskList);
            menu.Items.Add(menuNewSubSearch);

            var menuNewFolder = CreateMenuItem("New Folder", _eventHandlers.NewFolderSameLevel, _resources.NewFolder);
            menuNewFolder.BeginGroup = true;
            menu.Items.Add(menuNewFolder);
        }

        /// <summary>
        /// Adds menu items specific to ChildTerm
        /// </summary>
        private void AddChildTermMenuItems(TreeListNodeMenu menu, ChildTerm childTerm)
        {
            var menuNewSubSearch = CreateMenuItem("New Sub Search (Insert)", _eventHandlers.NewSubSearch, _resources.Add);
            menu.Items.Add(menuNewSubSearch);

            var menuDuplicate = CreateMenuItem("Duplicate Sub Search", _eventHandlers.NewCopy, _resources.Duplicate);
            menuDuplicate.BeginGroup = true;
            menu.Items.Add(menuDuplicate);
        }

        /// <summary>
        /// Adds default menu items for unknown data types
        /// </summary>
        private void AddDefaultMenuItems(TreeListNodeMenu menu)
        {
            var menuNewEBaySearch = CreateMenuItem("Add eBay Search (Insert)", _eventHandlers.NewEBaySearch);
            menu.Items.Add(menuNewEBaySearch);
        }

        #endregion

        #region Private Methods - Menu Item Creation

        /// <summary>
        /// Creates a DXMenuItem with standard properties
        /// </summary>
        /// <param name="text">Menu item text</param>
        /// <param name="clickHandler">Click event handler</param>
        /// <param name="svgImage">Optional SVG image</param>
        /// <returns>Configured DXMenuItem</returns>
        private DXMenuItem CreateMenuItem(string text, EventHandler clickHandler, object svgImage = null)
        {
            var menuItem = new DXMenuItem(text, clickHandler);
            if (svgImage != null)
            {
                menuItem.SvgImage = (DevExpress.Utils.Svg.SvgImage)svgImage;
            }
            return menuItem;
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Container for menu event handlers
    /// </summary>
    public class MenuEventHandlers
    {
        public EventHandler NewEBaySearch { get; set; }
        public EventHandler NewFolder { get; set; }
        public EventHandler NewFolderSameLevel { get; set; }
        public EventHandler NewRootFolder { get; set; }
        public EventHandler NewSubSearch { get; set; }
        public EventHandler NewCopy { get; set; }
        public EventHandler CheckSelected { get; set; }
        public EventHandler UnCheckSelected { get; set; }
        public EventHandler AddFromWatchList { get; set; }
        public EventHandler Delete { get; set; }
    }

    /// <summary>
    /// Container for menu resources (icons)
    /// </summary>
    public class MenuResources
    {
        public object Add { get; set; }
        public object NewFolder { get; set; }
        public object Duplicate { get; set; }
        public object AddTaskList { get; set; }
        public object CheckboxCheck { get; set; }
        public object SearchUncheck { get; set; }
        public object Remove { get; set; }
    }

    #endregion
}
