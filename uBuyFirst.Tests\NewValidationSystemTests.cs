using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using uBuyFirst;
using uBuyFirst.UI.Validation;
using uBuyFirst.SubSearch;
using uBuyFirst.Search;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class NewValidationSystemTests
    {
        private TreeListValidationCoordinator _coordinator;
        private ValidationServiceFactory _factory;

        [TestInitialize]
        public void TestInitialize()
        {
            _coordinator = new TreeListValidationCoordinator();
            _factory = ValidationServiceFactory.Instance;
        }

        [TestMethod]
        public void ValidationServiceFactory_ShouldCreateDefaultValidators()
        {
            // Act
            var validatorCount = _factory.ValidatorCount;

            // Assert
            Assert.IsTrue(validatorCount >= 3, "Should have at least 3 default validators");
        }

        [TestMethod]
        public void Keyword2FindValidator_ShouldValidateEmptyAlias()
        {
            // Arrange
            var validator = new Keyword2FindValidator();
            var keyword = new Keyword2Find { Alias = "", Kws = "test" };
            var context = ValidationContext.ForNode("test_node");

            // Act
            var result = validator.Validate(keyword, context);

            // Assert
            Assert.IsTrue(result.HasWarnings);
            Assert.IsTrue(result.HasWarningsForColumn("Alias"));
        }

        [TestMethod]
        public void ChildTermValidator_ShouldValidateEmptyKeywords()
        {
            // Arrange
            var validator = new ChildTermValidator();
            var childTerm = new ChildTerm { Alias = "test", Keywords = "" };
            var context = ValidationContext.ForNode("test_node");

            // Act
            var result = validator.Validate(childTerm, context);

            // Assert
            Assert.IsTrue(result.HasWarnings);
            Assert.IsTrue(result.HasWarningsForColumn("Keywords"));
        }

        [TestMethod]
        public void KeywordFolderValidator_ShouldValidateEmptyName()
        {
            // Arrange
            var validator = new KeywordFolderValidator();
            var folder = new KeywordFolder { Name = "" };
            var context = ValidationContext.ForNode("test_node");

            // Act
            var result = validator.Validate(folder, context);

            // Assert
            Assert.IsTrue(result.HasWarnings);
            Assert.IsTrue(result.HasWarningsForColumn("Alias"));
        }

        [TestMethod]
        public void ValidationResult_ShouldHandleMultipleWarnings()
        {
            // Arrange
            var result = new ValidationResult();

            // Act
            result.AddWarning("Alias", "Alias warning");
            result.AddWarning("Keywords", "Keywords warning");
            result.AddWarning("Price Min", "Price warning");

            // Assert
            Assert.AreEqual(3, result.WarningCount);
            Assert.IsTrue(result.HasWarnings);
            Assert.IsFalse(result.IsValid);
            Assert.IsTrue(result.HasWarningsForColumn("Alias"));
            Assert.IsTrue(result.HasWarningsForColumn("Keywords"));
            Assert.IsTrue(result.HasWarningsForColumn("Price Min"));
        }

        [TestMethod]
        public void ValidationContext_ShouldStoreProperties()
        {
            // Arrange
            var context = ValidationContext.ForNode("test_node");
            var testDict = new Dictionary<string, int> { { "test", 1 } };

            // Act
            context.SetProperty("TestProperty", testDict);

            // Assert
            Assert.IsTrue(context.HasProperty("TestProperty"));
            var retrieved = context.GetProperty<Dictionary<string, int>>("TestProperty");
            Assert.IsNotNull(retrieved);
            Assert.AreEqual(1, retrieved["test"]);
        }

        [TestMethod]
        public void NodeIdentifierService_ShouldGenerateUniqueIds()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "test", Kws = "test" };
            var childTerm = new ChildTerm { Alias = "child", Keywords = "child" };
            var folder = new KeywordFolder { Name = "folder" };

            // Act
            var keywordId = NodeIdentifierService.GetNodeId(keyword);
            var childTermId = NodeIdentifierService.GetNodeId(childTerm);
            var folderId = NodeIdentifierService.GetNodeId(folder);

            // Assert
            Assert.IsTrue(keywordId.StartsWith("keyword_"));
            Assert.IsTrue(childTermId.StartsWith("childterm_"));
            Assert.IsTrue(folderId.StartsWith("folder_"));
            Assert.AreNotEqual(keywordId, childTermId);
            Assert.AreNotEqual(keywordId, folderId);
            Assert.AreNotEqual(childTermId, folderId);
        }

        [TestMethod]
        public void TreeListValidationWarningManager_ShouldIntegrateWithValidationResult()
        {
            // Arrange
            var warningManager = new TreeListValidationWarningManager();
            var result = new ValidationResult();
            result.AddWarning("Alias", "Test warning");
            result.AddWarning("Keywords", "Another warning");

            // Act
            warningManager.SetWarningsFromResult("test_node", result);

            // Assert
            Assert.IsTrue(warningManager.HasWarning("test_node", "Alias"));
            Assert.IsTrue(warningManager.HasWarning("test_node", "Keywords"));
            Assert.AreEqual("Test warning", warningManager.GetWarning("test_node", "Alias"));
            Assert.AreEqual("Another warning", warningManager.GetWarning("test_node", "Keywords"));
        }

        [TestMethod]
        public void ValidationCoordinator_ShouldHaveWarningManager()
        {
            // Act
            var warningManager = _coordinator.WarningManager;

            // Assert
            Assert.IsNotNull(warningManager);
            Assert.IsInstanceOfType(warningManager, typeof(TreeListValidationWarningManager));
        }
    }
}
