# Bulk Property Edit Feature

This feature allows users to bulk edit properties of search terms at the folder level by directly editing folder cells in the TreeList, applying changes to all recursive descendant folders and search terms.

## Features

- **Direct cell editing**: Click on any bulk-editable folder cell to edit properties for all contained search terms
- **Visual indicators**: Bulk-editable folder cells have distinctive styling (light blue background with subtle borders)
- **Recursive processing**: Changes apply to all search terms in the selected folder and all its subfolders
- **Protected field enforcement**: Certain fields (enabled, alias, keywords, purchased quantity, jobid, required quantity) cannot be bulk edited
- **Comprehensive validation**: Input validation with cross-field checks (e.g., PriceMin ≤ PriceMax)
- **Immediate feedback**: Changes are applied instantly with error handling and validation messages
- **Contextual tooltips**: Hover over bulk-editable folder cells to see helpful information

## Usage

1. **Navigate to a folder** in the search terms tree
2. **Click on a bulk-editable cell** (Price Min, Price Max, Threads, etc.) - these have a light blue background
3. **Enter the new value** directly in the cell
4. **Press Enter** to apply the change to all search terms in the folder and its subfolders
5. **View immediate feedback** - the TreeList refreshes to show updated values

## Architecture

### Core Components

- **BulkPropertyEditor**: Main service class handling bulk operations and validation
- **BulkEditResult**: Result class providing operation feedback
- **KeywordFolder**: Enhanced with TreeList cell-based bulk editing capabilities
- **Form1.Treelist**: TreeList event handlers for bulk editing UI integration

### Bulk-Editable Properties

**Search Criteria:**
- **Price Min/Max**: Shows ranges when values differ, applies to all keywords
- **Search in Description**: Shows "Mixed" or boolean value, bulk toggles search behavior
- **Condition**: Shows "Mixed" or single condition, bulk sets item condition
- **Category ID**: Shows "Mixed" or single category, bulk sets search category

**Location Settings:**
- **Located In**: Shows "Mixed" or single location, bulk sets item location
- **Ships To**: Shows "Mixed" or single destination, bulk sets shipping destination
- **Ship Zipcode**: Shows "Mixed" or single ZIP, bulk sets shipping ZIP code

**Seller Settings:**
- **Seller Type**: Shows "Mixed" or single type, bulk sets seller type preference
- **Sellers**: Shows "Mixed" or seller count, bulk sets specific sellers

**Technical Settings:**
- **Threads**: Shows ranges when values differ, bulk sets search thread count
- **Site**: Shows "Mixed" or single site, bulk sets eBay site
- **View Name**: Shows "Mixed" or single view, bulk sets results view name
- **Type**: Shows "Mixed" or listing type, bulk sets listing type (Auction, Buy It Now, etc.)
- **Interval**: Shows time ranges, bulk sets search frequency

### Protected Properties (Cannot be bulk edited)

- `enabled` (KeywordEnabled)
- `alias` (Alias)
- `keywords` (Kws)
- `purchased quantity` (PurchasedQuantity)
- `jobid` (JobId)
- `required quantity` (RequiredQuantity)

## Validation Rules

- **Threads**: Must be between 1 and 10
- **PriceMin/PriceMax**: Must be positive numbers
- **Price Range**: PriceMin cannot be greater than PriceMax
- **ZIP Code**: Maximum 10 characters
- **Location fields**: Maximum 50 characters
- **View Name**: Cannot be empty, maximum 100 characters

## Error Handling

- **Input validation**: Comprehensive validation before applying changes
- **Protected field checks**: Prevents modification of restricted properties
- **Rollback capability**: Automatic rollback if >10% of operations fail
- **Detailed error reporting**: Clear error messages for troubleshooting

## Testing

The bulk editing functionality can be tested by:

1. **Manual Testing**: Click on bulk-editable folder cells in the TreeList
2. **Integration Testing**: Verify changes propagate to all descendant keywords
3. **Validation Testing**: Test error handling for invalid values
4. **UI Testing**: Verify visual feedback and TreeList refresh behavior

## Implementation Details

### TreeList Cell Integration
The feature integrates directly with the existing TreeList infrastructure:
- Extends `KeywordFolder.VirtualTreeGetCellValue` to show representative values for bulk-editable properties
- Implements `KeywordFolder.VirtualTreeSetCellValue` to handle bulk changes when folder cells are edited
- Updates cell editing permissions to allow folder cells to be editable for bulk properties
- Adds visual styling to distinguish bulk-editable folder cells

### Recursive Folder Traversal
Uses the existing `KeywordFolder.GetAllKeywords()` method for efficient recursive collection of all keywords in a folder hierarchy.

### DevExpress Integration
Built using existing DevExpress TreeList components:
- Leverages existing repository items for different data types (text, numeric, boolean)
- Uses existing cell styling and validation infrastructure
- Integrates with existing tooltip and visual feedback systems

## Future Enhancements

- **Bulk Enable/Disable**: Separate feature for folder-level activation
- **Template System**: Save common property combinations as templates
- **Conditional Bulk Edits**: Apply changes only to keywords matching criteria
- **Audit Trail**: Log all bulk changes for compliance
- **Preview Mode**: Show preview of changes before applying
- **Selective Updates**: Choose which properties to update per keyword

## Security Considerations

- **Input validation**: All property values are validated before application
- **Protected field enforcement**: System prevents unauthorized changes to restricted fields
- **Permission checks**: Could be extended to include user permission validation
- **Audit logging**: Framework in place for tracking bulk modifications
