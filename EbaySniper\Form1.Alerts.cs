﻿using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Alerter;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing;

namespace uBuyFirst
{
    public partial class Form1
    {
        private void alertControl1_BeforeFormShow(object sender, AlertFormEventArgs e)
        {
            e.AlertForm.Size = new Size(Convert.ToInt32(AlertOptionsClass.AlertOptions.Width), alertControl1.AutoHeight ? 100 : AlertOptionsClass.AlertOptions.Height);
            e.AlertForm.OpacityLevel = 1;
            e.AlertForm.Info.AutoCloseFormOnClick = true;
        }

        private void barButtonItemTrayAlert_ItemClick(object sender, ItemClickEventArgs e)
        {
            var formAlert = new FormAlert
            {
                AControl = alertControl1,
                EbaySearches = _ebaySearches.ChildrenCore
            };
            formAlert.ShowDialog();
            alertControl1.AppearanceCaption.FontSizeDelta = AlertOptionsClass.AlertOptions.FontDelta - 4;
            alertControl1.AppearanceText.FontSizeDelta = AlertOptionsClass.AlertOptions.FontDelta - 5;
            SaveSettings();
        }

        private void barButtonItemTrayAlertOptions_ItemClick(object sender, ItemClickEventArgs e)
        {
            var formAlert = new FormAlert
            {
                AControl = alertControl1,
                EbaySearches = _ebaySearches.ChildrenCore
            };
            formAlert.ShowDialog();
            alertControl1.AppearanceCaption.FontSizeDelta = AlertOptionsClass.AlertOptions.FontDelta - 4;
            alertControl1.AppearanceText.FontSizeDelta = AlertOptionsClass.AlertOptions.FontDelta - 5;
            SaveSettings();
        }

        private void barButtonItemCloseAll_ItemClick(object sender, ItemClickEventArgs e)
        {
            var count = alertControl1.PostponedFormList.Count;
            var i = 0;
            while (alertControl1.PostponedFormList.Count > 0 && count + 10 > i)
            {
                alertControl1.PostponedFormList.RemoveAt(0);
                i++;
            }

            foreach (var alertForm in alertControl1.AlertFormList)
            {
                alertForm.Close();
            }
        }

        private void AlertControl1_AlertClick(object sender, AlertClickEventArgs e)
        {
            var tuple = (Tuple<DataRow, GridControl>)e.Info.Tag;
            var row = tuple.Item1;
            if (row != null)
            {
                if (row.RowState != DataRowState.Added || row["ItemID"] == DBNull.Value)
                    return;
                var d = (DataList)row["Blob"];
                if (!string.IsNullOrEmpty(d.GetAffiliateLink()))
                    Browser.OpenAffiliateLink(d);
            }
        }

        private void alertControl1_ButtonClick(object sender, AlertButtonClickEventArgs e)
        {
            ProgramState.Idlesw.Restart();
            var tuple = (Tuple<DataRow, GridControl>)e.Info.Tag;
            var row = tuple.Item1;
            var gridControl = tuple.Item2;

            if (row != null)
            {
                if (row.RowState != DataRowState.Added || row["ItemID"] == DBNull.Value)
                    return;
                var d = (DataList)row["Blob"];
                if (d != null)
                {

                    var grView = (AdvBandedGridView)gridControl.MainView;
                    var rowHandle = grView.LocateByValue("ItemID", row["ItemID"]);
                    if (rowHandle != GridControl.InvalidRowHandle)
                    {
                        grView.FocusedRowHandle = rowHandle;
                        grView.ClearSelection();
                        grView.SelectRow(grView.FocusedRowHandle);
                    }

                    if (e.ButtonName == "alertCheckoutPage")
                    {
                        Browser.OpenCheckoutLink(d);
                    }
                }
            }

            Show();
            WindowState = FormWindowState.Maximized;
            Activate();
            if (e.ButtonName == "alertButtonBuy")
            {
                Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
                return;
            }

            if (e.ButtonName == "alertButtonOffer")
            {
                Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
            }
        }

        private void AlertControl1_FormLoad(object sender, AlertFormLoadEventArgs e)
        {
            var tuple = (Tuple<DataRow, GridControl>)e.AlertForm.AlertInfo.Tag;
            var row = tuple.Item1;
            if (row != null && row.RowState != DataRowState.Detached && row.RowState != DataRowState.Deleted && e.AlertForm.Buttons != null)
            {
                var d = (DataList)row["Blob"];
                if (!d.CommitToBuy || d.Variation)
                {
                    e.AlertForm.Buttons["alertButtonBuy"].Visible = false;
                }
                else
                {
                    e.AlertForm.Buttons["alertButtonBuy"].Visible = true;
                }

                if (!d.BestOffer)
                {
                    e.AlertForm.Buttons["alertButtonOffer"].Visible = false;
                }
                else
                {
                    e.AlertForm.Buttons["alertButtonOffer"].Visible = true;
                }
            }
        }

        /// <summary>
        /// Shows a tray alert notification about captcha detection and restock cooldown
        /// </summary>
        public void ShowCaptchaTrayAlert()
        {
            try
            {
                var title = "Restock Captcha Detected";
                var body = "Auto purchasing has been paused for 5 minutes.\nPlease solve the captcha in the opened browser window.";

                // Create a simple alert that works on all systems, not just Windows 7
                var alert = new DevExpress.XtraBars.Alerter.AlertControl();
                alert.AllowHtmlText = true;
                alert.AutoHeight = true;
                alert.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
                alert.ShowPinButton = true;

                // Show the alert without setting a Tag to avoid event handler issues
                alert.Show(this, title, body);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing captcha tray alert: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a tray alert notification about logout detection and restock cooldown
        /// </summary>
        public void ShowLogoutTrayAlert()
        {
            try
            {
                var title = "Restock Logout Detected";
                var body = "Auto purchasing has been paused for 5 minutes.\nPlease log into eBay in the opened browser window.";

                // Create a simple alert that works on all systems, not just Windows 7
                var alert = new DevExpress.XtraBars.Alerter.AlertControl();
                alert.AllowHtmlText = true;
                alert.AutoHeight = true;
                alert.FormShowingEffect = DevExpress.XtraBars.Alerter.AlertFormShowingEffect.SlideHorizontal;
                alert.ShowPinButton = true;

                // Show the alert without setting a Tag to avoid event handler issues
                alert.Show(this, title, body);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing logout tray alert: {ex.Message}");
            }
        }
    }
}
