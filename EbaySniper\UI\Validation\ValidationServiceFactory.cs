using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Factory for creating and managing validation services
    /// </summary>
    public class ValidationServiceFactory
    {
        private static ValidationServiceFactory _instance;
        private readonly List<ITreeListValidator> _validators;

        private ValidationServiceFactory()
        {
            _validators = new List<ITreeListValidator>();
            RegisterDefaultValidators();
        }

        /// <summary>
        /// Gets the singleton instance of the validation service factory
        /// </summary>
        public static ValidationServiceFactory Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ValidationServiceFactory();
                }
                return _instance;
            }
        }

        /// <summary>
        /// Registers the default validators for the application
        /// </summary>
        private void RegisterDefaultValidators()
        {
            RegisterValidator(new Keyword2FindValidator());
            RegisterValidator(new ChildTermValidator());
            RegisterValidator(new KeywordFolderValidator());
        }

        /// <summary>
        /// Registers a validator with the factory
        /// </summary>
        /// <param name="validator">The validator to register</param>
        public void RegisterValidator(ITreeListValidator validator)
        {
            if (validator != null && !_validators.Contains(validator))
            {
                _validators.Add(validator);
            }
        }

        /// <summary>
        /// Unregisters a validator from the factory
        /// </summary>
        /// <param name="validator">The validator to unregister</param>
        public void UnregisterValidator(ITreeListValidator validator)
        {
            if (validator != null)
            {
                _validators.Remove(validator);
            }
        }

        /// <summary>
        /// Gets a validator that can handle the specified item
        /// </summary>
        /// <param name="item">The item to find a validator for</param>
        /// <returns>A validator that can handle the item, or null if none found</returns>
        public ITreeListValidator GetValidator(object item)
        {
            if (item == null)
                return null;

            return _validators.FirstOrDefault(v => v.CanValidate(item));
        }

        /// <summary>
        /// Gets all registered validators
        /// </summary>
        /// <returns>A read-only list of all validators</returns>
        public IReadOnlyList<ITreeListValidator> GetAllValidators()
        {
            return _validators.AsReadOnly();
        }

        /// <summary>
        /// Gets validators that can handle the specified item type
        /// </summary>
        /// <typeparam name="T">The type to find validators for</typeparam>
        /// <returns>Validators that can handle the specified type</returns>
        public IEnumerable<ITreeListValidator> GetValidatorsForType<T>()
        {
            return _validators.Where(v => v.CanValidate(typeof(T)));
        }

        /// <summary>
        /// Clears all registered validators
        /// </summary>
        public void ClearValidators()
        {
            _validators.Clear();
        }

        /// <summary>
        /// Resets the factory to its default state with default validators
        /// </summary>
        public void Reset()
        {
            ClearValidators();
            RegisterDefaultValidators();
        }

        /// <summary>
        /// Gets the count of registered validators
        /// </summary>
        public int ValidatorCount => _validators.Count;
    }
}
