using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Linq;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.UI
{
    [TestClass]
    public class KeywordDeletionTests
    {
        private QueryList _queryList;
        private KeywordFolder _testFolder;

        [TestInitialize]
        public void Setup()
        {
            _queryList = new QueryList();
            _testFolder = new KeywordFolder
            {
                Name = "Test Folder",
                Id = "test-folder"
            };
            _queryList.Folders.Add(_testFolder);
        }

        [TestMethod]
        public void RemoveKeywordFromFolder_ShouldRemoveFromUnderlyingStructure()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "Keyword1", Kws = "test1" };
            var keyword2 = new Keyword2Find { Alias = "Keyword2", Kws = "test2" };

            keyword1.ParentFolder = _testFolder;
            keyword2.ParentFolder = _testFolder;
            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);

            // Verify initial state
            Assert.AreEqual(2, _testFolder.Keywords.Count);
            Assert.AreEqual(2, _queryList.ChildrenCore.Count);

            // Act - Simulate the deletion logic from RemoveSearchItem
            _testFolder.Keywords.Remove(keyword1);

            // Assert
            Assert.AreEqual(1, _testFolder.Keywords.Count);
            Assert.AreEqual(1, _queryList.ChildrenCore.Count);
            Assert.IsFalse(_testFolder.Keywords.Contains(keyword1));
            Assert.IsTrue(_testFolder.Keywords.Contains(keyword2));
        }

        [TestMethod]
        public void RemoveChildTermFromKeyword_ShouldRemoveFromUnderlyingStructure()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "ParentKeyword", Kws = "parent" };
            keyword.ParentFolder = _testFolder;
            _testFolder.Keywords.Add(keyword);

            var childTerm1 = new ChildTerm(keyword, "Child1");
            var childTerm2 = new ChildTerm(keyword, "Child2");

            // Verify initial state
            Assert.AreEqual(2, keyword.ChildrenCore.Count);

            // Act - Simulate the deletion logic from RemoveSearchItem
            keyword.ChildrenCore.Remove(childTerm1);

            // Assert
            Assert.AreEqual(1, keyword.ChildrenCore.Count);
            Assert.IsFalse(keyword.ChildrenCore.Contains(childTerm1));
            Assert.IsTrue(keyword.ChildrenCore.Contains(childTerm2));
        }

        [TestMethod]
        public void RemoveFolder_ShouldRemoveFromQueryList()
        {
            // Arrange
            var childFolder = new KeywordFolder
            {
                Name = "Child Folder",
                Id = "child-folder",
                ParentFolder = _testFolder
            };
            _testFolder.Children.Add(childFolder);

            // Verify initial state
            Assert.AreEqual(1, _testFolder.Children.Count);

            // Act - Simulate the deletion logic from RemoveSearchItem
            _testFolder.Children.Remove(childFolder);

            // Assert
            Assert.AreEqual(0, _testFolder.Children.Count);
            Assert.IsFalse(_testFolder.Children.Contains(childFolder));
        }

        [TestMethod]
        public void RemoveRootFolder_ShouldRemoveFromQueryListFolders()
        {
            // Arrange
            var rootFolder = new KeywordFolder
            {
                Name = "Root Folder",
                Id = "root-folder"
            };
            _queryList.Folders.Add(rootFolder);

            // Verify initial state
            Assert.AreEqual(2, _queryList.Folders.Count); // _testFolder + rootFolder

            // Act - Simulate the deletion logic from RemoveSearchItem
            _queryList.Folders.Remove(rootFolder);

            // Assert
            Assert.AreEqual(1, _queryList.Folders.Count);
            Assert.IsFalse(_queryList.Folders.Contains(rootFolder));
            Assert.IsTrue(_queryList.Folders.Contains(_testFolder));
        }

        [TestMethod]
        public void DeleteKeyword_ThenAddNew_ShouldNotRestoreDeletedKeyword()
        {
            // Arrange - This test simulates the original bug scenario
            var keyword1 = new Keyword2Find { Alias = "ToDelete", Kws = "delete me" };
            var keyword2 = new Keyword2Find { Alias = "ToKeep", Kws = "keep me" };

            keyword1.ParentFolder = _testFolder;
            keyword2.ParentFolder = _testFolder;
            _testFolder.Keywords.Add(keyword1);
            _testFolder.Keywords.Add(keyword2);

            // Verify initial state
            Assert.AreEqual(2, _queryList.ChildrenCore.Count);

            // Act - Delete keyword1 (simulate proper deletion)
            _testFolder.Keywords.Remove(keyword1);

            // Verify deletion worked
            Assert.AreEqual(1, _queryList.ChildrenCore.Count);
            Assert.IsFalse(_queryList.ChildrenCore.Any(k => k.Alias == "ToDelete"));

            // Act - Add a new keyword (this would trigger RefreshDataSource in real UI)
            var newKeyword = new Keyword2Find { Alias = "NewKeyword", Kws = "new" };
            newKeyword.ParentFolder = _testFolder;
            _testFolder.Keywords.Add(newKeyword);

            // Assert - The deleted keyword should NOT reappear
            Assert.AreEqual(2, _queryList.ChildrenCore.Count);
            Assert.IsFalse(_queryList.ChildrenCore.Any(k => k.Alias == "ToDelete"));
            Assert.IsTrue(_queryList.ChildrenCore.Any(k => k.Alias == "ToKeep"));
            Assert.IsTrue(_queryList.ChildrenCore.Any(k => k.Alias == "NewKeyword"));
        }

        [TestMethod]
        public void RemoveOneSubsearch_ShouldOnlyRemoveSelectedSubsearch()
        {
            // Arrange - This test verifies the fix for the double deletion bug
            var keyword = new Keyword2Find { Alias = "ParentKeyword", Kws = "parent" };
            keyword.ParentFolder = _testFolder;
            _testFolder.Keywords.Add(keyword);

            var subsearch1 = new ChildTerm(keyword, "Subsearch1");
            var subsearch2 = new ChildTerm(keyword, "Subsearch2");
            var subsearch3 = new ChildTerm(keyword, "Subsearch3");

            // Verify initial state - should have 3 subsearches
            Assert.AreEqual(3, keyword.ChildrenCore.Count);
            Assert.IsTrue(keyword.ChildrenCore.Contains(subsearch1));
            Assert.IsTrue(keyword.ChildrenCore.Contains(subsearch2));
            Assert.IsTrue(keyword.ChildrenCore.Contains(subsearch3));

            // Act - Remove only subsearch2 (simulating user selecting one subsearch to delete)
            var parentKeyword = subsearch2.GetParent();
            Assert.IsNotNull(parentKeyword, "Parent keyword should not be null");

            // This simulates the fixed deletion logic from HandleNonFolderDeletion
            parentKeyword.ChildrenCore.Remove(subsearch2);

            // Assert - Only subsearch2 should be removed, others should remain
            Assert.AreEqual(2, keyword.ChildrenCore.Count, "Should have exactly 2 subsearches remaining");
            Assert.IsTrue(keyword.ChildrenCore.Contains(subsearch1), "Subsearch1 should still exist");
            Assert.IsFalse(keyword.ChildrenCore.Contains(subsearch2), "Subsearch2 should be deleted");
            Assert.IsTrue(keyword.ChildrenCore.Contains(subsearch3), "Subsearch3 should still exist");

            // Verify the aliases are correct
            var remainingAliases = keyword.ChildrenCore.Select(c => c.Alias).ToList();
            Assert.IsTrue(remainingAliases.Contains("Subsearch1"));
            Assert.IsFalse(remainingAliases.Contains("Subsearch2"));
            Assert.IsTrue(remainingAliases.Contains("Subsearch3"));
        }
    }
}
