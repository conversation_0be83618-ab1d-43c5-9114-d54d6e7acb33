using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using eBay.Service.Core.Soap;
using uBuyFirst.Tools;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Validator for Keyword2Find objects
    /// </summary>
    public class Keyword2FindValidator : ITreeListValidator<Keyword2Find>, ITreeListValidator
    {
        /// <summary>
        /// Validates a Keyword2Find object and returns validation results
        /// </summary>
        /// <param name="item">The Keyword2Find to validate</param>
        /// <param name="context">Validation context containing additional information</param>
        /// <returns>Validation result containing any warnings</returns>
        public ValidationResult Validate(Keyword2Find item, ValidationContext context)
        {
            if (item == null)
                return ValidationResult.Success();

            var result = new ValidationResult();

            // Get existing aliases from context for uniqueness checking
            var existingAliases = context.GetProperty<Dictionary<string, int>>("ExistingAliases") ?? new Dictionary<string, int>();

            ValidateAlias(item, result, existingAliases);
            ValidateKeywords(item, result);
            ValidateCategories(item, result);
            ValidateLocation(item, result);
            ValidatePrices(item, result);
            ValidateSellers(item, result);

            return result;
        }

        /// <summary>
        /// Non-generic validation method
        /// </summary>
        public ValidationResult Validate(object item, ValidationContext context)
        {
            if (item is Keyword2Find keyword)
                return Validate(keyword, context);

            return ValidationResult.Success();
        }

        /// <summary>
        /// Determines if this validator can handle the specified item
        /// </summary>
        public bool CanValidate(object item)
        {
            return item is Keyword2Find;
        }

        private void ValidateAlias(Keyword2Find kw, ValidationResult result, Dictionary<string, int> existingAliases)
        {
            if (string.IsNullOrEmpty(kw.Alias))
            {
                result.AddWarning("Alias", $"Alias field should not be empty. [{kw.Kws}]");
            }
            else
            {
                // Apply unique alias logic (this modifies the object)
                kw.Alias = Helpers.MakeUniqAliasOnEdit(kw.Alias, existingAliases);
            }
        }

        private void ValidateKeywords(Keyword2Find kw, ValidationResult result)
        {
            if (string.IsNullOrEmpty(kw.Kws))
            {
                ValidateEmptyKeywords(kw, result);
            }
            else
            {
                ValidateNonEmptyKeywords(kw, result);
            }
        }

        private void ValidateEmptyKeywords(Keyword2Find kw, ValidationResult result)
        {
            if (Form1.LicenseUtility.CurrentLimits.SearchTermsCount != 10000)
            {
                result.AddWarning("Keywords", $"Keywords field should not be empty. \nWith Enterprise license you are able to leave Keywords field empty and search just by Category. [{kw.Alias}]");
            }

            if (string.IsNullOrEmpty(kw.Categories4Api))
            {
                result.AddWarning("Keywords", $"Please, fill either Keywords or Category field. [{kw.Alias}]");
            }

            if (kw.ListingType != null && kw.ListingType.Any(lt => lt == ListingType.OutOfStock))
            {
                result.AddWarning("Keywords", $"Please, enter items IDs separated by commas [{kw.Alias}]");
            }
        }

        private void ValidateNonEmptyKeywords(Keyword2Find kw, ValidationResult result)
        {
            if (kw.ListingType != null && kw.ListingType.Any(lt => lt == ListingType.OutOfStock))
            {
                ValidateOutOfStockKeywords(kw, result);
            }
            else
            {
                ValidateRegularKeywords(kw, result);
            }
        }

        private void ValidateOutOfStockKeywords(Keyword2Find kw, ValidationResult result)
        {
            if (kw.Kws.Length > 10000 || kw.Kws.Length < 12)
            {
                result.AddWarning("Keywords", $"Min/Max length for Out Of Stock Search is  12/3000. You have {kw.Kws.Length} [{kw.Alias}]");
            }

            var itemIDsPattern = "^((\\d{12})(,\\s*)*)*$";
            if (!Regex.IsMatch(kw.Kws, itemIDsPattern))
            {
                result.AddWarning("Keywords", $"Please, enter items IDs separated by commas [{kw.Alias}]");
            }
        }

        private void ValidateRegularKeywords(Keyword2Find kw, ValidationResult result)
        {
            if (kw.Kws.Length > 350 || kw.Kws.Length < 2)
            {
                result.AddWarning("Keywords", $"Min/Max length for eBay Search is  2/350. The maximum length for a single word is 98. [{kw.Alias}]");
            }

            if (kw.Kws.StartsWith("-"))
            {
                result.AddWarning("Keywords", $"eBay Search cannot start with '-' symbol. [{kw.Alias}]");
            }

            var error = KeywordHelpers.ValidateLuceneKeyword(kw.Kws, kw.Categories4Api.Length > 0);
            if (!string.IsNullOrEmpty(error))
            {
                result.AddWarning("Keywords", $"[{kw.Alias}]\r\nKeywords field has incorrect value.\r\n" +
                                           "Common issues: \r\nNot matched parentheses.\r\nExtra commas.\r\n" +
                                           "Unsupported special chars like - \"(inches).\r\nCan't start with '*'.");
            }
        }

        private void ValidateCategories(Keyword2Find kw, ValidationResult result)
        {
            if (kw.Categories4Api.Split(',').Length > 3)
            {
                result.AddWarning("Category ID", $"Maximum number of categories for eBay Search is 3. Best is to use 1 category. [{kw.Alias}]");
            }

            if (!Regex.IsMatch(kw.Categories4Api, "^[0-9,\\s]*$", RegexOptions.None))
            {
                result.AddWarning("Category ID", $"Category field should be empty OR contain only comma separated numbers. [{kw.Alias}]");
            }
        }

        private void ValidateLocation(Keyword2Find kw, ValidationResult result)
        {
            if (!Enum.IsDefined(typeof(CountryCodeType), kw.LocatedIn) && kw.LocatedIn != "Any")
            {
                result.AddWarning("Category ID", $"'Located in' value  [{kw.LocatedIn}] in search '{kw.Alias}' is invalid. Please, select a correct value.");
            }

            if (!Enum.IsDefined(typeof(CountryCodeType), kw.AvailableTo))
            {
                result.AddWarning("Category ID", $"'Available to' value [{kw.AvailableTo}] in search '{kw.Alias}' is invalid. Please, select a correct value.");
            }
        }

        private void ValidatePrices(Keyword2Find kw, ValidationResult result)
        {
            var priceMin = kw.PriceMin;
            var priceMax = kw.PriceMax;

            if (priceMin < 0.01 || priceMin > 10000000)
            {
                result.AddWarning("Price Min", $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
            }

            if (priceMax < 0.01 || priceMax > 10000000)
            {
                result.AddWarning("Price Max", $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
            }
        }

        private void ValidateSellers(Keyword2Find kw, ValidationResult result)
        {
            if (kw.Sellers.Length > 10)
            {
                result.AddWarning("Sellers", $"Maximum number of sellers is 10. [{kw.Alias}]");
            }
        }
    }
}
