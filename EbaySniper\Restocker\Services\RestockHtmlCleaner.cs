using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.Purchasing;
using uBuyFirst.Other;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for cleaning up old checkout HTML files from Reports\CheckoutHtml folder
    /// </summary>
    public static class RestockHtmlCleaner
    {
        private static System.Timers.Timer _dailyTimer;
        private const int DefaultRetentionDays = 14;

        /// <summary>
        /// Starts the cleanup timer to run daily
        /// </summary>
        public static void StartCleanupTimer()
        {
            // Run cleanup daily (24 hours = 24 * 60 * 60 * 1000 milliseconds)
            _dailyTimer = new System.Timers.Timer(24 * 60 * 60 * 1000);
            _dailyTimer.Elapsed += (sender, args) => CleanupExpiredHtml();
            _dailyTimer.Start();

            // Also run cleanup immediately on startup
            Task.Run(CleanupExpiredHtml);
        }

        /// <summary>
        /// Stops the cleanup timer
        /// </summary>
        public static void StopCleanupTimer()
        {
            _dailyTimer?.Stop();
            _dailyTimer?.Dispose();
            _dailyTimer = null;
        }

        /// <summary>
        /// Cleans up all checkout HTML files older than the retention period from Reports\CheckoutHtml folder
        /// </summary>
        public static void CleanupExpiredHtml()
        {
            Task.Run(() =>
            {
                try
                {
                    // HTML files are saved to Reports\CheckoutHtml folder in settings directory
                    var checkoutHtmlFolder = Path.Combine(Folders.Settings, "Reports", "CheckoutHtml");

                    if (!Directory.Exists(checkoutHtmlFolder))
                        return;

                    var cutoffDate = DateTime.UtcNow.AddDays(-DefaultRetentionDays);

                    PaymentLogger.LogPaymentToFile($"Starting checkout HTML cleanup - removing HTML files older than {DefaultRetentionDays} days");

                    var deletedCount = 0;
                    var errorCount = 0;

                    // Clean up all HTML files in the CheckoutHtml folder (both manual and restock)
                    deletedCount += CleanupCheckoutHtmlFiles(checkoutHtmlFolder, cutoffDate, ref errorCount);

                    PaymentLogger.LogPaymentToFile($"Checkout HTML cleanup completed - deleted {deletedCount} files with {errorCount} errors");
                }
                catch (Exception ex)
                {
                    PaymentLogger.LogPaymentToFile($"Checkout HTML cleanup failed: {ex.Message}");
                    Debug.WriteLine($"Checkout HTML cleanup error: {ex}");
                }
            });
        }

        /// <summary>
        /// Cleans up all checkout HTML files in the Reports\CheckoutHtml directory
        /// </summary>
        /// <param name="directoryPath">Directory to clean</param>
        /// <param name="cutoffDate">Files older than this date will be deleted</param>
        /// <param name="errorCount">Reference to error counter</param>
        /// <returns>Number of files deleted</returns>
        private static int CleanupCheckoutHtmlFiles(string directoryPath, DateTime cutoffDate, ref int errorCount)
        {
            var deletedCount = 0;

            try
            {
                if (!Directory.Exists(directoryPath))
                    return 0;

                // Target all HTML files in the CheckoutHtml folder (both manual and restock)
                var files = Directory.GetFiles(directoryPath, "*.html");

                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTimeUtc < cutoffDate)
                        {
                            File.Delete(file);
                            deletedCount++;
                            Debug.WriteLine($"Deleted expired checkout HTML file: {file}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed to delete file {file}: {ex.Message}");
                        errorCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to process directory {directoryPath}: {ex.Message}");
                errorCount++;
            }

            return deletedCount;
        }

        /// <summary>
        /// Manually triggers cleanup (for testing or immediate cleanup)
        /// </summary>
        /// <param name="retentionDays">Override retention days (optional)</param>
        /// <returns>Number of files deleted</returns>
        public static async Task<int> ManualCleanupAsync(int? retentionDays = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var effectiveRetentionDays = retentionDays ?? DefaultRetentionDays;
                    var checkoutHtmlFolder = Path.Combine(Folders.Settings, "Reports", "CheckoutHtml");

                    if (!Directory.Exists(checkoutHtmlFolder))
                        return 0;

                    var cutoffDate = DateTime.UtcNow.AddDays(-effectiveRetentionDays);
                    var deletedCount = 0;
                    var errorCount = 0;

                    // Clean up all HTML files (both manual and restock)
                    deletedCount += CleanupCheckoutHtmlFiles(checkoutHtmlFolder, cutoffDate, ref errorCount);

                    return deletedCount;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Manual cleanup error: {ex}");
                    return 0;
                }
            });
        }
    }
}
