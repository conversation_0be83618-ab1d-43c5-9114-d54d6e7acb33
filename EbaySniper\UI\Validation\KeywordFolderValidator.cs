using DevExpress.XtraEditors;
using uBuyFirst.Search;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Validator for KeywordFolder objects
    /// </summary>
    public class KeywordFolderValidator : ITreeListValidator<KeywordFolder>, ITreeListValidator
    {
        /// <summary>
        /// Validates a KeywordFolder object and returns validation results
        /// </summary>
        /// <param name="item">The KeywordFolder to validate</param>
        /// <param name="context">Validation context containing additional information</param>
        /// <returns>Validation result containing any warnings</returns>
        public ValidationResult Validate(KeywordFolder item, ValidationContext context)
        {
            if (item == null)
                return ValidationResult.Success();

            var result = new ValidationResult();

            ValidateFolderName(item, result, context);
            ValidateHierarchy(item, result);

            return result;
        }

        /// <summary>
        /// Non-generic validation method
        /// </summary>
        public ValidationResult Validate(object item, ValidationContext context)
        {
            if (item is KeywordFolder folder)
                return Validate(folder, context);

            return ValidationResult.Success();
        }

        /// <summary>
        /// Determines if this validator can handle the specified item
        /// </summary>
        public bool CanValidate(object item)
        {
            return item is KeywordFolder;
        }

        private void ValidateFolderName(KeywordFolder folder, ValidationResult result, ValidationContext context)
        {
            if (string.IsNullOrEmpty(folder.Name))
            {
                result.AddWarning("Alias", "Folder name should not be empty.");
                return;
            }

            // Check for duplicate names among siblings
            var queryList = context.GetProperty<QueryList>("QueryList");
            if (queryList != null && queryList.IsFolderNameTakenBySiblings(folder, folder.Name))
            {
                result.AddWarning("Alias", "A folder with this name already exists at this level. Please choose a different name.");

                // Show message box to user (keep this as it's important feedback)
                // Note: This is a side effect in validation, but preserving existing behavior
                XtraMessageBox.Show(
                    $"A folder with the name '{folder.Name}' already exists at this level.\n\nPlease choose a different name.",
                    "Duplicate Folder Name",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Warning);
            }
        }

        private void ValidateHierarchy(KeywordFolder folder, ValidationResult result)
        {
            if (!folder.ValidateHierarchy())
            {
                result.AddWarning("Alias", "Circular reference detected in folder hierarchy.");
            }
        }
    }
}
