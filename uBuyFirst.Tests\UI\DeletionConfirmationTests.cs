using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Linq;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.UI
{
    [TestClass]
    public class DeletionConfirmationTests
    {
        [TestMethod]
        public void BuildConfirmationMessage_WithFewItems_ShowsItemNames()
        {
            // Arrange
            var keyword1 = new Keyword2Find { Alias = "iPhone" };
            var keyword2 = new Keyword2Find { Alias = "Samsung" };
            var folder = new KeywordFolder { Name = "Electronics" };
            
            var items = new List<object> { keyword1, keyword2, folder };

            // Act
            var message = BuildConfirmationMessage(items);

            // Assert
            Assert.IsTrue(message.Contains("Keyword: iPhone"));
            Assert.IsTrue(message.Contains("Keyword: Samsung"));
            Assert.IsTrue(message.Contains("Folder: Electronics"));
            Assert.IsTrue(message.Contains("Remove the following 3 item(s)?"));
        }

        [TestMethod]
        public void BuildConfirmationMessage_WithManyItems_ShowsCountOnly()
        {
            // Arrange
            var items = new List<object>();
            for (int i = 0; i < 15; i++)
            {
                items.Add(new Keyword2Find { Alias = $"Keyword{i}" });
            }

            // Act
            var message = BuildConfirmationMessage(items);

            // Assert
            Assert.IsTrue(message.Contains("Remove 15 selected items?"));
            Assert.IsFalse(message.Contains("Keyword0")); // Should not show individual names
        }

        [TestMethod]
        public void BuildConfirmationMessage_WithExactly10Items_ShowsItemNames()
        {
            // Arrange
            var items = new List<object>();
            for (int i = 0; i < 10; i++)
            {
                items.Add(new Keyword2Find { Alias = $"Keyword{i}" });
            }

            // Act
            var message = BuildConfirmationMessage(items);

            // Assert
            Assert.IsTrue(message.Contains("Remove the following 10 item(s)?"));
            Assert.IsTrue(message.Contains("Keyword: Keyword0"));
            Assert.IsTrue(message.Contains("Keyword: Keyword9"));
        }

        [TestMethod]
        public void BuildConfirmationMessage_WithMixedItemTypes_ShowsCorrectLabels()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "TestKeyword" };
            var childTerm = new ChildTerm(keyword, "TestChild");
            var folder = new KeywordFolder { Name = "TestFolder" };
            
            var items = new List<object> { keyword, childTerm, folder };

            // Act
            var message = BuildConfirmationMessage(items);

            // Assert
            Assert.IsTrue(message.Contains("Keyword: TestKeyword"));
            Assert.IsTrue(message.Contains("Sub Search: TestChild"));
            Assert.IsTrue(message.Contains("Folder: TestFolder"));
        }

        // Helper method that simulates the logic from RemoveSearchItem
        private string BuildConfirmationMessage(List<object> items)
        {
            if (items.Count <= 10)
            {
                var itemNames = new List<string>();
                foreach (var dataRecord in items)
                {
                    string itemName = dataRecord switch
                    {
                        Keyword2Find keyword => $"Keyword: {keyword.Alias}",
                        ChildTerm childTerm => $"Sub Search: {childTerm.Alias}",
                        KeywordFolder folder => $"Folder: {folder.Name}",
                        _ => "Unknown item"
                    };
                    itemNames.Add(itemName);
                }
                
                return $"Remove the following {items.Count} item(s)?\n\n" + 
                       string.Join("\n", itemNames);
            }
            else
            {
                return $"Remove {items.Count} selected items?";
            }
        }
    }
}
