using System;
using System.Collections.Generic;
using System.Data;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Data;
using uBuyFirst.Pricing;
using uBuyFirst.Telegram;
using uBuyFirst.Time;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class TelegramMessageTemplateManagerTests
    {
        private TelegramSender _telegramSender;
        private MessageTemplateManager _messageTemplateManager;
        private DataTable _testTable;
        private DataList _testDataList;

        [TestInitialize]
        public void Setup()
        {
            _telegramSender = new TelegramSender();
            _messageTemplateManager = new MessageTemplateManager(_telegramSender);

            // Create a test DataTable with some columns
            _testTable = new DataTable();
            _testTable.Columns.Add("ItemID", typeof(string));
            _testTable.Columns.Add("Title", typeof(string));
            _testTable.Columns.Add("Description", typeof(string));
            _testTable.Columns.Add("Blob", typeof(object));

            // Create test DataList
            _testDataList = new DataList();
            _testDataList.ItemPricing = new ItemPricing { ItemPrice = new CurrencyAmount(10.50, "USD") };
            _testDataList.ItemShipping = new ItemShipping { FullSingleShippingPrice = new CurrencyAmount(5.00, "USD") };
            _testDataList.FoundTime = new DateTimeWithDiff(DateTime.UtcNow, null);
        }

        [TestMethod]
        public void GetFormattedMessage_WithDeletedRow_ReturnsEmptyString()
        {
            // Arrange
            var row = _testTable.NewRow();
            row["ItemID"] = "123456789";
            row["Title"] = "Test Item";
            row["Description"] = "Test Description";
            row["Blob"] = _testDataList;
            _testTable.Rows.Add(row);

            // Set up a simple template
            _telegramSender.BodyTemplate = "Item: {Title}";
            _telegramSender.BodyColumns = new List<string> { "Title" };

            // Delete the row to simulate the race condition
            row.Delete();

            // Act
            var result = _messageTemplateManager.GetFormattedMessage(row, _testDataList);

            // Assert
            Assert.AreEqual("", result, "Should return empty string for deleted row");
        }

        [TestMethod]
        public void GetFormattedMessage_WithDetachedRow_ReturnsEmptyString()
        {
            // Arrange
            var row = _testTable.NewRow();
            row["ItemID"] = "123456789";
            row["Title"] = "Test Item";
            row["Description"] = "Test Description";
            row["Blob"] = _testDataList;
            _testTable.Rows.Add(row);

            // Set up a simple template
            _telegramSender.BodyTemplate = "Item: {Title}";
            _telegramSender.BodyColumns = new List<string> { "Title" };

            // Remove the row from table to simulate detached state
            _testTable.Rows.Remove(row);

            // Act
            var result = _messageTemplateManager.GetFormattedMessage(row, _testDataList);

            // Assert
            Assert.AreEqual("", result, "Should return empty string for detached row");
        }

        [TestMethod]
        public void GetFormattedMessage_WithValidRow_ReturnsFormattedMessage()
        {
            // Arrange
            var row = _testTable.NewRow();
            row["ItemID"] = "123456789";
            row["Title"] = "Test Item";
            row["Description"] = "Test Description";
            row["Blob"] = _testDataList;
            _testTable.Rows.Add(row);

            // Set up a simple template
            _telegramSender.BodyTemplate = "Item: {Title}";
            _telegramSender.BodyColumns = new List<string> { "Title" };

            // Act
            var result = _messageTemplateManager.GetFormattedMessage(row, _testDataList);

            // Assert
            Assert.AreEqual("Item: Test Item", result, "Should return formatted message for valid row");
        }

        [TestMethod]
        public void GetFormattedMessage_WithNullRow_ReturnsEmptyString()
        {
            // Arrange
            _telegramSender.BodyTemplate = "Item: {Title}";
            _telegramSender.BodyColumns = new List<string> { "Title" };

            // Act
            var result = _messageTemplateManager.GetFormattedMessage(null, _testDataList);

            // Assert
            Assert.AreEqual("", result, "Should return empty string for null row");
        }

        [TestMethod]
        public void GetFormattedMessage_WithDescriptionColumn_HandlesDeletedRowGracefully()
        {
            // Arrange
            var row = _testTable.NewRow();
            row["ItemID"] = "123456789";
            row["Title"] = "Test Item";
            row["Description"] = "Test Description with <b>HTML</b> content";
            row["Blob"] = _testDataList;
            _testTable.Rows.Add(row);

            // Set up template with Description column (the problematic one from the stack trace)
            _telegramSender.BodyTemplate = "Description: {Description}";
            _telegramSender.BodyColumns = new List<string> { "Description" };

            // Delete the row after setting up template
            row.Delete();

            // Act
            var result = _messageTemplateManager.GetFormattedMessage(row, _testDataList);

            // Assert
            Assert.AreEqual("", result, "Should handle deleted row gracefully when accessing Description column");
        }
    }
}
