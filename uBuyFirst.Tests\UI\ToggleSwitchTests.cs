using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraTreeList;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace uBuyFirst.Tests.UI
{
    [TestClass]
    public class ToggleSwitchTests
    {
        [TestMethod]
        public void ToggleSwitch_CanBeCreated()
        {
            // Arrange & Act
            var toggleSwitch = new RepositoryItemToggleSwitch();

            // Assert
            Assert.IsNotNull(toggleSwitch);
            Assert.AreEqual("repositoryItemToggleSwitch1", toggleSwitch.Name);
        }

        [TestMethod]
        public void ToggleSwitch_CanSetOnOffText()
        {
            // Arrange
            var toggleSwitch = new RepositoryItemToggleSwitch();

            // Act
            toggleSwitch.OnText = "Enabled";
            toggleSwitch.OffText = "Disabled";

            // Assert
            Assert.AreEqual("Enabled", toggleSwitch.OnText);
            Assert.AreEqual("Disabled", toggleSwitch.OffText);
        }

        [TestMethod]
        public void ToggleSwitch_CanHandleBooleanValues()
        {
            // Arrange
            var toggleSwitch = new RepositoryItemToggleSwitch();
            var control = new ToggleSwitch();

            // Act
            control.IsOn = true;
            var isOnValue = control.IsOn;

            control.IsOn = false;
            var isOffValue = control.IsOn;

            // Assert
            Assert.IsTrue(isOnValue);
            Assert.IsFalse(isOffValue);
        }

        [TestMethod]
        public void ToggleSwitch_CanConvertToCheckState()
        {
            // Arrange
            var toggleSwitch = new ToggleSwitch();

            // Act & Assert
            toggleSwitch.IsOn = true;
            var checkedState = toggleSwitch.IsOn ? CheckState.Checked : CheckState.Unchecked;
            Assert.AreEqual(CheckState.Checked, checkedState);

            toggleSwitch.IsOn = false;
            var uncheckedState = toggleSwitch.IsOn ? CheckState.Checked : CheckState.Unchecked;
            Assert.AreEqual(CheckState.Unchecked, uncheckedState);
        }

        [TestMethod]
        public void RepositoryItemToggleSwitch_CanSetThumbWidth()
        {
            // Arrange
            var repositoryItem = new RepositoryItemToggleSwitch();

            // Act
            repositoryItem.ThumbWidth = 8;

            // Assert
            Assert.AreEqual(8, repositoryItem.ThumbWidth);
        }

        [TestMethod]
        public void RepositoryItemToggleSwitch_CanSetEditorToThumbWidthRatio()
        {
            // Arrange
            var repositoryItem = new RepositoryItemToggleSwitch();

            // Act
            repositoryItem.EditorToThumbWidthRatio = 2.0F;

            // Assert
            Assert.AreEqual(2.0F, repositoryItem.EditorToThumbWidthRatio);
        }
    }
}
