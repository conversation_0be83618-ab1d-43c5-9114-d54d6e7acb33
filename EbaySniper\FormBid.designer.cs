﻿using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout;

namespace uBuyFirst
{
    partial class FormBid
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormBid));
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions5 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject17 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject18 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject19 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject20 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions1 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject3 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject4 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions6 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject21 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject22 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject23 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject24 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.XtraEditors.Controls.EditorButtonImageOptions editorButtonImageOptions7 = new DevExpress.XtraEditors.Controls.EditorButtonImageOptions();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject25 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject26 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject27 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject28 = new DevExpress.Utils.SerializableAppearanceObject();
            this.numPrice = new System.Windows.Forms.NumericUpDown();
            this.lblCurrentBid = new System.Windows.Forms.Label();
            this.lblCurrentBidNum = new System.Windows.Forms.Label();
            this.lblBidsNum = new System.Windows.Forms.Label();
            this.grpAuction = new System.Windows.Forms.Panel();
            this.lblValShipping = new System.Windows.Forms.Label();
            this.grpBestOffer = new System.Windows.Forms.Panel();
            this.lblBestOffer = new System.Windows.Forms.Label();
            this.btnBestOffer = new System.Windows.Forms.Button();
            this.lblValBidsNumberForBO = new System.Windows.Forms.Label();
            this.numBestOfferAmount = new System.Windows.Forms.NumericUpDown();
            this.lblValCurrentPriceForBestOffer = new System.Windows.Forms.Label();
            this.LstShipSvc = new System.Windows.Forms.ListView();
            this.ClmService = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ClmCost = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ClmAddedCost = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.ClmShipLocation = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.grpShipping = new System.Windows.Forms.Panel();
            this.linkLblShipping = new System.Windows.Forms.LinkLabel();
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barButtonItemAppearance = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemCustomizeLayout = new DevExpress.XtraBars.BarButtonItem();
            this.ResetLayout = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemIncreaseFont = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemDecreaseFont = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItemHelp = new DevExpress.XtraBars.BarButtonItem();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.lcShippingDays = new DevExpress.XtraEditors.LabelControl();
            this.lcAuctionPrice = new DevExpress.XtraEditors.LabelControl();
            this.lcListingType = new DevExpress.XtraEditors.LabelControl();
            this.lcBids = new DevExpress.XtraEditors.LabelControl();
            this.lciTax = new DevExpress.XtraEditors.LabelControl();
            this.lcOfferTotalPrice = new DevExpress.XtraEditors.LabelControl();
            this.spinEditOfferPercentage = new DevExpress.XtraEditors.SpinEdit();
            this.memoEditBestOfferMessage = new DevExpress.XtraEditors.MemoEdit();
            this.spinEditBestOfferItemQuantity = new DevExpress.XtraEditors.SpinEdit();
            this.spinEditBestOfferPricePerItem = new DevExpress.XtraEditors.SpinEdit();
            this.simpleButtonBestOffer = new DevExpress.XtraEditors.SimpleButton();
            this.spinEditBuyNowQuantity = new DevExpress.XtraEditors.SpinEdit();
            this.pictureEdit1 = new DevExpress.XtraEditors.PictureEdit();
            this.btnBuyItnow = new DevExpress.XtraEditors.SimpleButton();
            this.lcEbayAccount = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lcTerm = new DevExpress.XtraEditors.LabelControl();
            this.lcAlias = new DevExpress.XtraEditors.LabelControl();
            this.lcItemID = new DevExpress.XtraEditors.LabelControl();
            this.lcTitle = new DevExpress.XtraEditors.LabelControl();
            this.lcTotalPrice = new DevExpress.XtraEditors.LabelControl();
            this.lcReturns = new DevExpress.XtraEditors.LabelControl();
            this.lcBestOffer = new DevExpress.XtraEditors.LabelControl();
            this.lcFoundTime = new DevExpress.XtraEditors.LabelControl();
            this.lcAutoPay = new DevExpress.XtraEditors.LabelControl();
            this.lcCategoryID = new DevExpress.XtraEditors.LabelControl();
            this.lcCategoryName = new DevExpress.XtraEditors.LabelControl();
            this.lcConditionDescription = new DevExpress.XtraEditors.LabelControl();
            this.lcCondition = new DevExpress.XtraEditors.LabelControl();
            this.lcLocation = new DevExpress.XtraEditors.LabelControl();
            this.lcFromCountry = new DevExpress.XtraEditors.LabelControl();
            this.lcToCountry = new DevExpress.XtraEditors.LabelControl();
            this.lcFeedbackRating = new DevExpress.XtraEditors.LabelControl();
            this.lcFeedbackScore = new DevExpress.XtraEditors.LabelControl();
            this.lcItemPrice = new DevExpress.XtraEditors.LabelControl();
            this.lcPostedTime = new DevExpress.XtraEditors.LabelControl();
            this.lcQuantity = new DevExpress.XtraEditors.LabelControl();
            this.lcSellerName = new DevExpress.XtraEditors.LabelControl();
            this.lcShipping = new DevExpress.XtraEditors.LabelControl();
            this.lcShippingType = new DevExpress.XtraEditors.LabelControl();
            this.lcShipAdditionalItem = new DevExpress.XtraEditors.LabelControl();
            this.lcSoldTime = new DevExpress.XtraEditors.LabelControl();
            this.lcEbayWebsite = new DevExpress.XtraEditors.LabelControl();
            this.lcPageViews = new DevExpress.XtraEditors.LabelControl();
            this.lcUPC = new DevExpress.XtraEditors.LabelControl();
            this.lcVariation = new DevExpress.XtraEditors.LabelControl();
            this.lcPayment = new DevExpress.XtraEditors.LabelControl();
            this.lblAvailableCount = new DevExpress.XtraEditors.LabelControl();
            this.lblSold = new DevExpress.XtraEditors.LabelControl();
            this.lblPurchaseResult = new DevExpress.XtraEditors.LabelControl();
            this.lblShippingtype = new DevExpress.XtraEditors.LabelControl();
            this.lblHandlingCosts = new DevExpress.XtraEditors.LabelControl();
            this.lblShippingPackage = new DevExpress.XtraEditors.LabelControl();
            this.lblShippingWeight = new DevExpress.XtraEditors.LabelControl();
            this.rdioShippingOptions = new DevExpress.XtraEditors.RadioGroup();
            this.chkSubstractShipping = new DevExpress.XtraEditors.CheckEdit();
            this.boxMakeOfferMessages = new DevExpress.XtraEditors.LookUpEdit();
            this.lciVariation = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciUPC = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciTerm = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAlias = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciSoldTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShipAdditionalItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingType = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciQuantity = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPageViews = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPostedTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFoundTime = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciEbayWebsite = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCategoryName = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCategoryID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBestOffer = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAutoPay = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciConditionDescription = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciReturns = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciTitle = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciEbayAccount = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAvailable = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciTotalPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.buttonBuyItNow = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciItemPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPurchaseResult = new DevExpress.XtraLayout.LayoutControlItem();
            this.simpleSeparator1 = new DevExpress.XtraLayout.SimpleSeparator();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.tabbedControlGroup1 = new DevExpress.XtraLayout.TabbedControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciItemID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciLocation = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFromCountry = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciToCountry = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciSellerName = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFeedbackScore = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciFeedbackRating = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCondition = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPayment = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingtype2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciHandlingCosts = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingPackage = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingWeight = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lciBids = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciListingType = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAuctionPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingDays = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciMakeOffertab = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciBestOfferSellerMessage = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBestOfferQuantity = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBestOfferButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciBestOfferPricePerItem = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciItemPricePercent = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciOfferTotalPrice = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciSubstractShipping = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciMakeOfferMessages = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShippingOptionsTab = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciShippingOptionsRadioGroup = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciSold = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciQuantityEdit = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciShipping = new DevExpress.XtraLayout.LayoutControlItem();
            this.lcTax = new DevExpress.XtraLayout.LayoutControlItem();
            this.popupMenuItemDetails = new DevExpress.XtraBars.PopupMenu(this.components);
            this.linkeBayPrivacyPolicy = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.linkeBayUserAgreement = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.pictureEditLogo = new DevExpress.XtraEditors.PictureEdit();
            ((System.ComponentModel.ISupportInitialize)(this.numPrice)).BeginInit();
            this.grpAuction.SuspendLayout();
            this.grpBestOffer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBestOfferAmount)).BeginInit();
            this.grpShipping.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOfferPercentage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditBestOfferMessage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBestOfferItemQuantity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBestOfferPricePerItem.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBuyNowQuantity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcEbayAccount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdioShippingOptions.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSubstractShipping.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxMakeOfferMessages.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciVariation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciUPC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTerm)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAlias)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSoldTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipAdditionalItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPageViews)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostedTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFoundTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayWebsite)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOffer)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAutoPay)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciConditionDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciReturns)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTitle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAvailable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTotalPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.buttonBuyItNow)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPurchaseResult)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciLocation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFromCountry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciToCountry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSellerName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackScore)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackRating)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCondition)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPayment)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingtype2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciHandlingCosts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingPackage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingWeight)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBids)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciListingType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAuctionPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingDays)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciMakeOffertab)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferSellerMessage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferPricePerItem)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPricePercent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciOfferTotalPrice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSubstractShipping)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciMakeOfferMessages)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingOptionsTab)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingOptionsRadioGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantityEdit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipping)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcTax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuItemDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureEditLogo.Properties)).BeginInit();
            this.SuspendLayout();
            //
            // numPrice
            //
            this.numPrice.DecimalPlaces = 2;
            this.numPrice.Location = new System.Drawing.Point(87, 78);
            this.numPrice.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numPrice.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.numPrice.Name = "numPrice";
            this.numPrice.Size = new System.Drawing.Size(60, 21);
            this.numPrice.TabIndex = 2;
            this.numPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numPrice.Value = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            //
            // lblCurrentBid
            //
            this.lblCurrentBid.AutoSize = true;
            this.lblCurrentBid.Location = new System.Drawing.Point(6, 50);
            this.lblCurrentBid.Name = "lblCurrentBid";
            this.lblCurrentBid.Size = new System.Drawing.Size(65, 13);
            this.lblCurrentBid.TabIndex = 3;
            this.lblCurrentBid.Text = "Current bid:";
            //
            // lblCurrentBidNum
            //
            this.lblCurrentBidNum.AutoSize = true;
            this.lblCurrentBidNum.Location = new System.Drawing.Point(84, 50);
            this.lblCurrentBidNum.Name = "lblCurrentBidNum";
            this.lblCurrentBidNum.Size = new System.Drawing.Size(29, 13);
            this.lblCurrentBidNum.TabIndex = 3;
            this.lblCurrentBidNum.Text = "0.00";
            //
            // lblBidsNum
            //
            this.lblBidsNum.AutoSize = true;
            this.lblBidsNum.Location = new System.Drawing.Point(153, 50);
            this.lblBidsNum.Name = "lblBidsNum";
            this.lblBidsNum.Size = new System.Drawing.Size(49, 13);
            this.lblBidsNum.TabIndex = 3;
            this.lblBidsNum.Text = "[ 0 bids ]";
            //
            // grpAuction
            //
            this.grpAuction.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grpAuction.Controls.Add(this.lblCurrentBid);
            this.grpAuction.Controls.Add(this.lblBidsNum);
            this.grpAuction.Controls.Add(this.numPrice);
            this.grpAuction.Controls.Add(this.lblCurrentBidNum);
            this.grpAuction.Location = new System.Drawing.Point(48, 262);
            this.grpAuction.Name = "grpAuction";
            this.grpAuction.Size = new System.Drawing.Size(50, 75);
            this.grpAuction.TabIndex = 4;
            this.grpAuction.Visible = false;
            //
            // lblValShipping
            //
            this.lblValShipping.AutoSize = true;
            this.lblValShipping.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(204)));
            this.lblValShipping.Location = new System.Drawing.Point(3, 48);
            this.lblValShipping.MaximumSize = new System.Drawing.Size(220, 0);
            this.lblValShipping.Name = "lblValShipping";
            this.lblValShipping.Size = new System.Drawing.Size(220, 26);
            this.lblValShipping.TabIndex = 4;
            this.lblValShipping.Text = "                                                                                 " +
    "                                   ";
            //
            // grpBestOffer
            //
            this.grpBestOffer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grpBestOffer.Controls.Add(this.lblBestOffer);
            this.grpBestOffer.Controls.Add(this.btnBestOffer);
            this.grpBestOffer.Controls.Add(this.lblValBidsNumberForBO);
            this.grpBestOffer.Controls.Add(this.numBestOfferAmount);
            this.grpBestOffer.Controls.Add(this.lblValCurrentPriceForBestOffer);
            this.grpBestOffer.Location = new System.Drawing.Point(12, 264);
            this.grpBestOffer.Name = "grpBestOffer";
            this.grpBestOffer.Size = new System.Drawing.Size(30, 32);
            this.grpBestOffer.TabIndex = 7;
            this.grpBestOffer.Visible = false;
            //
            // lblBestOffer
            //
            this.lblBestOffer.AutoSize = true;
            this.lblBestOffer.Location = new System.Drawing.Point(6, 50);
            this.lblBestOffer.Name = "lblBestOffer";
            this.lblBestOffer.Size = new System.Drawing.Size(59, 13);
            this.lblBestOffer.TabIndex = 3;
            this.lblBestOffer.Text = "Best offer:";
            //
            // btnBestOffer
            //
            this.btnBestOffer.Location = new System.Drawing.Point(156, 75);
            this.btnBestOffer.Name = "btnBestOffer";
            this.btnBestOffer.Size = new System.Drawing.Size(85, 23);
            this.btnBestOffer.TabIndex = 0;
            this.btnBestOffer.Text = "Make an offer";
            this.btnBestOffer.UseVisualStyleBackColor = true;
            //
            // lblValBidsNumberForBO
            //
            this.lblValBidsNumberForBO.AutoSize = true;
            this.lblValBidsNumberForBO.Location = new System.Drawing.Point(153, 50);
            this.lblValBidsNumberForBO.Name = "lblValBidsNumberForBO";
            this.lblValBidsNumberForBO.Size = new System.Drawing.Size(49, 13);
            this.lblValBidsNumberForBO.TabIndex = 3;
            this.lblValBidsNumberForBO.Text = "[ 0 bids ]";
            //
            // numBestOfferAmount
            //
            this.numBestOfferAmount.DecimalPlaces = 2;
            this.numBestOfferAmount.Location = new System.Drawing.Point(87, 78);
            this.numBestOfferAmount.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.numBestOfferAmount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            this.numBestOfferAmount.Name = "numBestOfferAmount";
            this.numBestOfferAmount.Size = new System.Drawing.Size(60, 21);
            this.numBestOfferAmount.TabIndex = 2;
            this.numBestOfferAmount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.numBestOfferAmount.Value = new decimal(new int[] {
            1,
            0,
            0,
            131072});
            //
            // lblValCurrentPriceForBestOffer
            //
            this.lblValCurrentPriceForBestOffer.AutoSize = true;
            this.lblValCurrentPriceForBestOffer.Location = new System.Drawing.Point(84, 50);
            this.lblValCurrentPriceForBestOffer.Name = "lblValCurrentPriceForBestOffer";
            this.lblValCurrentPriceForBestOffer.Size = new System.Drawing.Size(29, 13);
            this.lblValCurrentPriceForBestOffer.TabIndex = 3;
            this.lblValCurrentPriceForBestOffer.Text = "0.00";
            //
            // LstShipSvc
            //
            this.LstShipSvc.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.LstShipSvc.BackColor = System.Drawing.SystemColors.Control;
            this.LstShipSvc.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.ClmService,
            this.ClmCost,
            this.ClmAddedCost,
            this.ClmShipLocation});
            this.LstShipSvc.GridLines = true;
            this.LstShipSvc.HideSelection = false;
            this.LstShipSvc.Location = new System.Drawing.Point(227, 21);
            this.LstShipSvc.Name = "LstShipSvc";
            this.LstShipSvc.Size = new System.Drawing.Size(17, 99);
            this.LstShipSvc.TabIndex = 77;
            this.LstShipSvc.UseCompatibleStateImageBehavior = false;
            this.LstShipSvc.View = System.Windows.Forms.View.Details;
            //
            // ClmService
            //
            this.ClmService.Text = "Shipping Service";
            this.ClmService.Width = 150;
            //
            // ClmCost
            //
            this.ClmCost.Text = "Cost";
            this.ClmCost.Width = 70;
            //
            // ClmAddedCost
            //
            this.ClmAddedCost.Text = "Additional Items";
            this.ClmAddedCost.Width = 83;
            //
            // ClmShipLocation
            //
            this.ClmShipLocation.Text = "Locations";
            this.ClmShipLocation.Width = 90;
            //
            // grpShipping
            //
            this.grpShipping.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.grpShipping.Controls.Add(this.linkLblShipping);
            this.grpShipping.Controls.Add(this.LstShipSvc);
            this.grpShipping.Controls.Add(this.lblValShipping);
            this.grpShipping.Location = new System.Drawing.Point(104, 266);
            this.grpShipping.Name = "grpShipping";
            this.grpShipping.Size = new System.Drawing.Size(50, 43);
            this.grpShipping.TabIndex = 5;
            this.grpShipping.Visible = false;
            //
            // linkLblShipping
            //
            this.linkLblShipping.AutoSize = true;
            this.linkLblShipping.Location = new System.Drawing.Point(3, 28);
            this.linkLblShipping.Name = "linkLblShipping";
            this.linkLblShipping.Size = new System.Drawing.Size(81, 13);
            this.linkLblShipping.TabIndex = 78;
            this.linkLblShipping.TabStop = true;
            this.linkLblShipping.Text = "Shipping details";
            //
            // ribbonControl1
            //
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.barButtonItemAppearance,
            this.barButtonItemCustomizeLayout,
            this.ResetLayout,
            this.barButtonItemIncreaseFont,
            this.barButtonItemDecreaseFont,
            this.barButtonItemHelp});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 7;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsMenuMinWidth = 283;
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.QuickToolbarItemLinks.Add(this.barButtonItemAppearance);
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.Size = new System.Drawing.Size(855, 49);
            //
            // barButtonItemAppearance
            //
            this.barButtonItemAppearance.Caption = "Customize Appearance";
            this.barButtonItemAppearance.CategoryGuid = new System.Guid("6ffddb2b-9015-4d97-a4c1-91613e0ef537");
            this.barButtonItemAppearance.Id = 1;
            this.barButtonItemAppearance.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemAppearance.ImageOptions.Image")));
            this.barButtonItemAppearance.Name = "barButtonItemAppearance";
            this.barButtonItemAppearance.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemChangeAppearance_ItemClick);
            //
            // barButtonItemCustomizeLayout
            //
            this.barButtonItemCustomizeLayout.Caption = "Customize Layout";
            this.barButtonItemCustomizeLayout.Id = 2;
            this.barButtonItemCustomizeLayout.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemCustomizeLayout.ImageOptions.Image")));
            this.barButtonItemCustomizeLayout.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemCustomizeLayout.ImageOptions.LargeImage")));
            this.barButtonItemCustomizeLayout.Name = "barButtonItemCustomizeLayout";
            this.barButtonItemCustomizeLayout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemCustomizeLayout_ItemClick);
            //
            // ResetLayout
            //
            this.ResetLayout.Caption = "Reset Layout";
            this.ResetLayout.Id = 3;
            this.ResetLayout.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("ResetLayout.ImageOptions.Image")));
            this.ResetLayout.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("ResetLayout.ImageOptions.LargeImage")));
            this.ResetLayout.Name = "ResetLayout";
            this.ResetLayout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemResetLayout_ItemClick);
            //
            // barButtonItemIncreaseFont
            //
            this.barButtonItemIncreaseFont.Caption = "Increase Font Size";
            this.barButtonItemIncreaseFont.Id = 4;
            this.barButtonItemIncreaseFont.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemIncreaseFont.ImageOptions.Image")));
            this.barButtonItemIncreaseFont.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemIncreaseFont.ImageOptions.LargeImage")));
            this.barButtonItemIncreaseFont.Name = "barButtonItemIncreaseFont";
            this.barButtonItemIncreaseFont.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemIncreaseFont_ItemClick);
            //
            // barButtonItemDecreaseFont
            //
            this.barButtonItemDecreaseFont.Caption = "Decrease Font Size";
            this.barButtonItemDecreaseFont.Id = 5;
            this.barButtonItemDecreaseFont.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemDecreaseFont.ImageOptions.Image")));
            this.barButtonItemDecreaseFont.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemDecreaseFont.ImageOptions.LargeImage")));
            this.barButtonItemDecreaseFont.Name = "barButtonItemDecreaseFont";
            this.barButtonItemDecreaseFont.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemDecreaseFont_ItemClick);
            //
            // barButtonItemHelp
            //
            this.barButtonItemHelp.Caption = "Help";
            this.barButtonItemHelp.Id = 6;
            this.barButtonItemHelp.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItemHelp.ImageOptions.Image")));
            this.barButtonItemHelp.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItemHelp.ImageOptions.LargeImage")));
            this.barButtonItemHelp.Name = "barButtonItemHelp";
            this.barButtonItemHelp.Tag = "https://ubuyfirst.com/08-commit-to-buy-best-offer/";
            this.barButtonItemHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItemHelp_ItemClick);
            //
            // layoutControl1
            //
            this.layoutControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.layoutControl1.Appearance.Control.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.layoutControl1.Appearance.Control.Options.UseFont = true;
            this.layoutControl1.Controls.Add(this.lcShippingDays);
            this.layoutControl1.Controls.Add(this.lcAuctionPrice);
            this.layoutControl1.Controls.Add(this.lcListingType);
            this.layoutControl1.Controls.Add(this.lcBids);
            this.layoutControl1.Controls.Add(this.lciTax);
            this.layoutControl1.Controls.Add(this.lcOfferTotalPrice);
            this.layoutControl1.Controls.Add(this.spinEditOfferPercentage);
            this.layoutControl1.Controls.Add(this.memoEditBestOfferMessage);
            this.layoutControl1.Controls.Add(this.spinEditBestOfferItemQuantity);
            this.layoutControl1.Controls.Add(this.spinEditBestOfferPricePerItem);
            this.layoutControl1.Controls.Add(this.simpleButtonBestOffer);
            this.layoutControl1.Controls.Add(this.spinEditBuyNowQuantity);
            this.layoutControl1.Controls.Add(this.pictureEdit1);
            this.layoutControl1.Controls.Add(this.btnBuyItnow);
            this.layoutControl1.Controls.Add(this.lcEbayAccount);
            this.layoutControl1.Controls.Add(this.lcTerm);
            this.layoutControl1.Controls.Add(this.lcAlias);
            this.layoutControl1.Controls.Add(this.lcItemID);
            this.layoutControl1.Controls.Add(this.lcTitle);
            this.layoutControl1.Controls.Add(this.lcTotalPrice);
            this.layoutControl1.Controls.Add(this.lcReturns);
            this.layoutControl1.Controls.Add(this.lcBestOffer);
            this.layoutControl1.Controls.Add(this.lcFoundTime);
            this.layoutControl1.Controls.Add(this.lcAutoPay);
            this.layoutControl1.Controls.Add(this.lcCategoryID);
            this.layoutControl1.Controls.Add(this.lcCategoryName);
            this.layoutControl1.Controls.Add(this.lcConditionDescription);
            this.layoutControl1.Controls.Add(this.lcCondition);
            this.layoutControl1.Controls.Add(this.lcLocation);
            this.layoutControl1.Controls.Add(this.lcFromCountry);
            this.layoutControl1.Controls.Add(this.lcToCountry);
            this.layoutControl1.Controls.Add(this.lcFeedbackRating);
            this.layoutControl1.Controls.Add(this.lcFeedbackScore);
            this.layoutControl1.Controls.Add(this.lcItemPrice);
            this.layoutControl1.Controls.Add(this.lcPostedTime);
            this.layoutControl1.Controls.Add(this.lcQuantity);
            this.layoutControl1.Controls.Add(this.lcSellerName);
            this.layoutControl1.Controls.Add(this.lcShipping);
            this.layoutControl1.Controls.Add(this.lcShippingType);
            this.layoutControl1.Controls.Add(this.lcShipAdditionalItem);
            this.layoutControl1.Controls.Add(this.lcSoldTime);
            this.layoutControl1.Controls.Add(this.lcEbayWebsite);
            this.layoutControl1.Controls.Add(this.lcPageViews);
            this.layoutControl1.Controls.Add(this.lcUPC);
            this.layoutControl1.Controls.Add(this.lcVariation);
            this.layoutControl1.Controls.Add(this.lcPayment);
            this.layoutControl1.Controls.Add(this.lblAvailableCount);
            this.layoutControl1.Controls.Add(this.lblSold);
            this.layoutControl1.Controls.Add(this.lblPurchaseResult);
            this.layoutControl1.Controls.Add(this.lblShippingtype);
            this.layoutControl1.Controls.Add(this.lblHandlingCosts);
            this.layoutControl1.Controls.Add(this.lblShippingPackage);
            this.layoutControl1.Controls.Add(this.lblShippingWeight);
            this.layoutControl1.Controls.Add(this.rdioShippingOptions);
            this.layoutControl1.Controls.Add(this.chkSubstractShipping);
            this.layoutControl1.Controls.Add(this.boxMakeOfferMessages);
            this.layoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciVariation,
            this.lciUPC,
            this.lciTerm,
            this.lciAlias,
            this.lciSoldTime,
            this.lciShipAdditionalItem,
            this.lciShippingType,
            this.lciQuantity,
            this.lciPageViews,
            this.lciPostedTime,
            this.lciFoundTime,
            this.lciEbayWebsite,
            this.lciCategoryName,
            this.lciCategoryID,
            this.lciBestOffer,
            this.lciAutoPay,
            this.lciConditionDescription,
            this.lciReturns});
            this.layoutControl1.Location = new System.Drawing.Point(0, 27);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(899, 254, 721, 963);
            this.layoutControl1.OptionsCustomizationForm.ShowPropertyGrid = true;
            this.layoutControl1.OptionsSerialization.RestoreAppearanceItemCaption = true;
            this.layoutControl1.OptionsSerialization.RestoreLayoutGroupAppearanceGroup = true;
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(855, 573);
            this.layoutControl1.TabIndex = 4;
            this.layoutControl1.Text = "layoutControl1";
            this.layoutControl1.LayoutUpgrade += new DevExpress.Utils.LayoutUpgradeEventHandler(this.layoutControl1_LayoutUpgrade);
            //
            // lcShippingDays
            //
            this.lcShippingDays.Location = new System.Drawing.Point(282, 368);
            this.lcShippingDays.Name = "lcShippingDays";
            this.lcShippingDays.Size = new System.Drawing.Size(55, 13);
            this.lcShippingDays.StyleController = this.layoutControl1;
            this.lcShippingDays.TabIndex = 26;
            //
            // lcAuctionPrice
            //
            this.lcAuctionPrice.Location = new System.Drawing.Point(124, 487);
            this.lcAuctionPrice.Name = "lcAuctionPrice";
            this.lcAuctionPrice.Size = new System.Drawing.Size(213, 13);
            this.lcAuctionPrice.StyleController = this.layoutControl1;
            this.lcAuctionPrice.TabIndex = 23;
            //
            // lcListingType
            //
            this.lcListingType.Location = new System.Drawing.Point(124, 470);
            this.lcListingType.Name = "lcListingType";
            this.lcListingType.Size = new System.Drawing.Size(213, 13);
            this.lcListingType.StyleController = this.layoutControl1;
            this.lcListingType.TabIndex = 22;
            //
            // lcBids
            //
            this.lcBids.Location = new System.Drawing.Point(124, 453);
            this.lcBids.Name = "lcBids";
            this.lcBids.Size = new System.Drawing.Size(213, 13);
            this.lcBids.StyleController = this.layoutControl1;
            this.lcBids.TabIndex = 20;
            //
            // lciTax
            //
            this.lciTax.Location = new System.Drawing.Point(112, 82);
            this.lciTax.Name = "lciTax";
            this.lciTax.Size = new System.Drawing.Size(100, 13);
            this.lciTax.StyleController = this.layoutControl1;
            this.lciTax.TabIndex = 18;
            //
            // lcOfferTotalPrice
            //
            this.lcOfferTotalPrice.Appearance.Options.UseTextOptions = true;
            this.lcOfferTotalPrice.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lcOfferTotalPrice.Location = new System.Drawing.Point(124, 232);
            this.lcOfferTotalPrice.Name = "lcOfferTotalPrice";
            this.lcOfferTotalPrice.Size = new System.Drawing.Size(213, 13);
            this.lcOfferTotalPrice.StyleController = this.layoutControl1;
            this.lcOfferTotalPrice.TabIndex = 17;
            this.lcOfferTotalPrice.Text = "0";
            //
            // spinEditOfferPercentage
            //
            this.spinEditOfferPercentage.EditValue = new decimal(new int[] {
            50,
            0,
            0,
            0});
            this.spinEditOfferPercentage.Location = new System.Drawing.Point(124, 273);
            this.spinEditOfferPercentage.MenuManager = this.ribbonControl1;
            this.spinEditOfferPercentage.MinimumSize = new System.Drawing.Size(100, 0);
            this.spinEditOfferPercentage.Name = "spinEditOfferPercentage";
            this.spinEditOfferPercentage.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditOfferPercentage.Properties.EditValueChangedDelay = 25;
            this.spinEditOfferPercentage.Properties.Increment = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditOfferPercentage.Properties.Mask.EditMask = "P";
            this.spinEditOfferPercentage.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.spinEditOfferPercentage.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.spinEditOfferPercentage.Size = new System.Drawing.Size(100, 20);
            this.spinEditOfferPercentage.StyleController = this.layoutControl1;
            this.spinEditOfferPercentage.TabIndex = 4;
            this.spinEditOfferPercentage.EditValueChanged += new System.EventHandler(this.spinEditOfferPercentage_EditValueChanged);
            //
            // memoEditBestOfferMessage
            //
            this.memoEditBestOfferMessage.Location = new System.Drawing.Point(124, 368);
            this.memoEditBestOfferMessage.MenuManager = this.ribbonControl1;
            this.memoEditBestOfferMessage.Name = "memoEditBestOfferMessage";
            this.memoEditBestOfferMessage.Size = new System.Drawing.Size(213, 181);
            this.memoEditBestOfferMessage.StyleController = this.layoutControl1;
            this.memoEditBestOfferMessage.TabIndex = 6;
            this.memoEditBestOfferMessage.EditValueChanged += new System.EventHandler(this.MemoEditBestOfferMessage_EditValueChanged);
            this.memoEditBestOfferMessage.Leave += new System.EventHandler(this.MemoEditBestOfferMessage_Leave);
            this.memoEditBestOfferMessage.MouseLeave += new System.EventHandler(this.MemoEditBestOfferMessage_MouseLeave);
            //
            // spinEditBestOfferItemQuantity
            //
            this.spinEditBestOfferItemQuantity.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditBestOfferItemQuantity.Location = new System.Drawing.Point(124, 297);
            this.spinEditBestOfferItemQuantity.MenuManager = this.ribbonControl1;
            this.spinEditBestOfferItemQuantity.Name = "spinEditBestOfferItemQuantity";
            this.spinEditBestOfferItemQuantity.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditBestOfferItemQuantity.Properties.EditValueChangedDelay = 25;
            this.spinEditBestOfferItemQuantity.Properties.IsFloatValue = false;
            this.spinEditBestOfferItemQuantity.Properties.Mask.EditMask = "N00";
            this.spinEditBestOfferItemQuantity.Size = new System.Drawing.Size(100, 20);
            this.spinEditBestOfferItemQuantity.StyleController = this.layoutControl1;
            this.spinEditBestOfferItemQuantity.TabIndex = 5;
            this.spinEditBestOfferItemQuantity.EditValueChanged += new System.EventHandler(this.spinEditBestOfferItemQuantity_EditValueChanged);
            //
            // spinEditBestOfferPricePerItem
            //
            this.spinEditBestOfferPricePerItem.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditBestOfferPricePerItem.Location = new System.Drawing.Point(124, 249);
            this.spinEditBestOfferPricePerItem.MenuManager = this.ribbonControl1;
            this.spinEditBestOfferPricePerItem.MinimumSize = new System.Drawing.Size(100, 0);
            this.spinEditBestOfferPricePerItem.Name = "spinEditBestOfferPricePerItem";
            this.spinEditBestOfferPricePerItem.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditBestOfferPricePerItem.Properties.EditValueChangedDelay = 25;
            this.spinEditBestOfferPricePerItem.Properties.Mask.EditMask = "\\$ \\d+(\\.\\d{1,2})?";
            this.spinEditBestOfferPricePerItem.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx;
            this.spinEditBestOfferPricePerItem.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.spinEditBestOfferPricePerItem.Properties.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.spinEditBestOfferPricePerItem.Size = new System.Drawing.Size(100, 20);
            this.spinEditBestOfferPricePerItem.StyleController = this.layoutControl1;
            this.spinEditBestOfferPricePerItem.TabIndex = 3;
            this.spinEditBestOfferPricePerItem.EditValueChanged += new System.EventHandler(this.spinEditBestOfferPricePerItem_EditValueChanged);
            this.spinEditBestOfferPricePerItem.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.spinEditBestOfferPricePerItem_EditValueChanging);
            //
            // simpleButtonBestOffer
            //
            this.simpleButtonBestOffer.Location = new System.Drawing.Point(228, 262);
            this.simpleButtonBestOffer.Name = "simpleButtonBestOffer";
            this.simpleButtonBestOffer.Size = new System.Drawing.Size(109, 65);
            this.simpleButtonBestOffer.StyleController = this.layoutControl1;
            this.simpleButtonBestOffer.TabIndex = 7;
            this.simpleButtonBestOffer.Text = "Make Offer";
            this.simpleButtonBestOffer.Click += new System.EventHandler(this.simpleButtonBestOffer_Click);
            //
            // spinEditBuyNowQuantity
            //
            this.spinEditBuyNowQuantity.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.spinEditBuyNowQuantity.Location = new System.Drawing.Point(112, 99);
            this.spinEditBuyNowQuantity.MenuManager = this.ribbonControl1;
            this.spinEditBuyNowQuantity.Name = "spinEditBuyNowQuantity";
            serializableAppearanceObject17.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(204)));
            serializableAppearanceObject17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            serializableAppearanceObject17.Options.UseFont = true;
            serializableAppearanceObject17.Options.UseForeColor = true;
            this.spinEditBuyNowQuantity.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph, "All", 20, true, true, false, editorButtonImageOptions5, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject17, serializableAppearanceObject18, serializableAppearanceObject19, serializableAppearanceObject20, "Toggle button to always buy all available quantity.", "AllQuantity", null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.spinEditBuyNowQuantity.Properties.IsFloatValue = false;
            this.spinEditBuyNowQuantity.Properties.Mask.EditMask = "N00";
            this.spinEditBuyNowQuantity.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.spinEditBuyNowQuantity.Size = new System.Drawing.Size(100, 20);
            this.spinEditBuyNowQuantity.StyleController = this.layoutControl1;
            this.spinEditBuyNowQuantity.TabIndex = 2;
            this.spinEditBuyNowQuantity.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.spinEditBuyNowQuantity_ButtonClick);
            this.spinEditBuyNowQuantity.EditValueChanged += new System.EventHandler(this.BuyNowQuantityChanged);
            //
            // pictureEdit1
            //
            this.pictureEdit1.Cursor = System.Windows.Forms.Cursors.Default;
            this.pictureEdit1.EditValue = ((object)(resources.GetObject("pictureEdit1.EditValue")));
            this.pictureEdit1.Location = new System.Drawing.Point(353, 198);
            this.pictureEdit1.MenuManager = this.ribbonControl1;
            this.pictureEdit1.Name = "pictureEdit1";
            this.pictureEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.pictureEdit1.Properties.NullText = " ";
            this.pictureEdit1.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            this.pictureEdit1.Size = new System.Drawing.Size(490, 363);
            this.pictureEdit1.StyleController = this.layoutControl1;
            this.pictureEdit1.TabIndex = 9;
            //
            // btnBuyItnow
            //
            this.btnBuyItnow.ImageOptions.Image = global::uBuyFirst.Properties.Resources.Cart16;
            this.btnBuyItnow.ImageOptions.ImageToTextAlignment = DevExpress.XtraEditors.ImageAlignToText.LeftCenter;
            this.btnBuyItnow.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnBuyItnow.Location = new System.Drawing.Point(587, 34);
            this.btnBuyItnow.Name = "btnBuyItnow";
            this.btnBuyItnow.Size = new System.Drawing.Size(238, 82);
            this.btnBuyItnow.StyleController = this.layoutControl1;
            this.btnBuyItnow.TabIndex = 0;
            this.btnBuyItnow.Text = "Buy It Now";
            this.btnBuyItnow.Click += new System.EventHandler(this.BtnPurchase_Click);
            this.btnBuyItnow.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormClose_OnESC);
            //
            // lcEbayAccount
            //
            this.lcEbayAccount.Location = new System.Drawing.Point(112, 157);
            this.lcEbayAccount.MenuManager = this.ribbonControl1;
            this.lcEbayAccount.Name = "lcEbayAccount";
            this.lcEbayAccount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.lcEbayAccount.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.lcEbayAccount.Size = new System.Drawing.Size(245, 20);
            this.lcEbayAccount.StyleController = this.layoutControl1;
            this.lcEbayAccount.TabIndex = 1;
            this.lcEbayAccount.SelectedValueChanged += new System.EventHandler(this.lcEbayAccount_SelectedValueChanged);
            //
            // lcTerm
            //
            this.lcTerm.Location = new System.Drawing.Point(339, 132);
            this.lcTerm.Name = "lcTerm";
            this.lcTerm.Size = new System.Drawing.Size(0, 13);
            this.lcTerm.StyleController = this.layoutControl1;
            this.lcTerm.TabIndex = 6;
            this.lcTerm.MouseClick += new System.Windows.Forms.MouseEventHandler(this.lcTerm_MouseClick);
            //
            // lcAlias
            //
            this.lcAlias.Location = new System.Drawing.Point(339, 149);
            this.lcAlias.Name = "lcAlias";
            this.lcAlias.Size = new System.Drawing.Size(0, 13);
            this.lcAlias.StyleController = this.layoutControl1;
            this.lcAlias.TabIndex = 6;
            this.lcAlias.MouseClick += new System.Windows.Forms.MouseEventHandler(this.lcTerm_MouseClick);
            //
            // lcItemID
            //
            this.lcItemID.AllowHtmlString = true;
            this.lcItemID.Location = new System.Drawing.Point(124, 232);
            this.lcItemID.Name = "lcItemID";
            this.lcItemID.Size = new System.Drawing.Size(213, 13);
            this.lcItemID.StyleController = this.layoutControl1;
            this.lcItemID.TabIndex = 6;
            this.lcItemID.Click += new System.EventHandler(this.lcItemID_Click);
            //
            // lcTitle
            //
            this.lcTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lcTitle.Location = new System.Drawing.Point(112, 12);
            this.lcTitle.Name = "lcTitle";
            this.lcTitle.Size = new System.Drawing.Size(731, 13);
            this.lcTitle.StyleController = this.layoutControl1;
            this.lcTitle.TabIndex = 6;
            //
            // lcTotalPrice
            //
            this.lcTotalPrice.Location = new System.Drawing.Point(112, 31);
            this.lcTotalPrice.MinimumSize = new System.Drawing.Size(100, 0);
            this.lcTotalPrice.Name = "lcTotalPrice";
            this.lcTotalPrice.Size = new System.Drawing.Size(100, 13);
            this.lcTotalPrice.StyleController = this.layoutControl1;
            this.lcTotalPrice.TabIndex = 6;
            //
            // lcReturns
            //
            this.lcReturns.Location = new System.Drawing.Point(95, 46);
            this.lcReturns.Name = "lcReturns";
            this.lcReturns.Size = new System.Drawing.Size(0, 13);
            this.lcReturns.StyleController = this.layoutControl1;
            this.lcReturns.TabIndex = 6;
            //
            // lcBestOffer
            //
            this.lcBestOffer.Location = new System.Drawing.Point(116, 132);
            this.lcBestOffer.Name = "lcBestOffer";
            this.lcBestOffer.Size = new System.Drawing.Size(0, 13);
            this.lcBestOffer.StyleController = this.layoutControl1;
            this.lcBestOffer.TabIndex = 6;
            //
            // lcFoundTime
            //
            this.lcFoundTime.Location = new System.Drawing.Point(116, 132);
            this.lcFoundTime.Name = "lcFoundTime";
            this.lcFoundTime.Size = new System.Drawing.Size(0, 13);
            this.lcFoundTime.StyleController = this.layoutControl1;
            this.lcFoundTime.TabIndex = 6;
            //
            // lcAutoPay
            //
            this.lcAutoPay.Location = new System.Drawing.Point(116, 132);
            this.lcAutoPay.Name = "lcAutoPay";
            this.lcAutoPay.Size = new System.Drawing.Size(0, 13);
            this.lcAutoPay.StyleController = this.layoutControl1;
            this.lcAutoPay.TabIndex = 6;
            //
            // lcCategoryID
            //
            this.lcCategoryID.Location = new System.Drawing.Point(116, 132);
            this.lcCategoryID.Name = "lcCategoryID";
            this.lcCategoryID.Size = new System.Drawing.Size(0, 13);
            this.lcCategoryID.StyleController = this.layoutControl1;
            this.lcCategoryID.TabIndex = 6;
            //
            // lcCategoryName
            //
            this.lcCategoryName.Location = new System.Drawing.Point(116, 132);
            this.lcCategoryName.Name = "lcCategoryName";
            this.lcCategoryName.Size = new System.Drawing.Size(0, 13);
            this.lcCategoryName.StyleController = this.layoutControl1;
            this.lcCategoryName.TabIndex = 6;
            //
            // lcConditionDescription
            //
            this.lcConditionDescription.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lcConditionDescription.Location = new System.Drawing.Point(121, 114);
            this.lcConditionDescription.Name = "lcConditionDescription";
            this.lcConditionDescription.Size = new System.Drawing.Size(510, 0);
            this.lcConditionDescription.StyleController = this.layoutControl1;
            this.lcConditionDescription.TabIndex = 6;
            //
            // lcCondition
            //
            this.lcCondition.Location = new System.Drawing.Point(124, 351);
            this.lcCondition.Name = "lcCondition";
            this.lcCondition.Size = new System.Drawing.Size(213, 13);
            this.lcCondition.StyleController = this.layoutControl1;
            this.lcCondition.TabIndex = 6;
            //
            // lcLocation
            //
            this.lcLocation.Location = new System.Drawing.Point(124, 249);
            this.lcLocation.Name = "lcLocation";
            this.lcLocation.Size = new System.Drawing.Size(213, 13);
            this.lcLocation.StyleController = this.layoutControl1;
            this.lcLocation.TabIndex = 6;
            //
            // lcFromCountry
            //
            this.lcFromCountry.Appearance.Options.UseTextOptions = true;
            this.lcFromCountry.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.Character;
            this.lcFromCountry.Location = new System.Drawing.Point(124, 266);
            this.lcFromCountry.MaximumSize = new System.Drawing.Size(280, 0);
            this.lcFromCountry.Name = "lcFromCountry";
            this.lcFromCountry.Size = new System.Drawing.Size(213, 13);
            this.lcFromCountry.StyleController = this.layoutControl1;
            this.lcFromCountry.TabIndex = 6;
            //
            // lcToCountry
            //
            this.lcToCountry.Appearance.Options.UseTextOptions = true;
            this.lcToCountry.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.Character;
            this.lcToCountry.Location = new System.Drawing.Point(124, 283);
            this.lcToCountry.Name = "lcToCountry";
            this.lcToCountry.Size = new System.Drawing.Size(213, 13);
            this.lcToCountry.StyleController = this.layoutControl1;
            this.lcToCountry.TabIndex = 6;
            //
            // lcFeedbackRating
            //
            this.lcFeedbackRating.Location = new System.Drawing.Point(124, 334);
            this.lcFeedbackRating.Name = "lcFeedbackRating";
            this.lcFeedbackRating.Size = new System.Drawing.Size(213, 13);
            this.lcFeedbackRating.StyleController = this.layoutControl1;
            this.lcFeedbackRating.TabIndex = 6;
            //
            // lcFeedbackScore
            //
            this.lcFeedbackScore.Location = new System.Drawing.Point(124, 317);
            this.lcFeedbackScore.Name = "lcFeedbackScore";
            this.lcFeedbackScore.Size = new System.Drawing.Size(213, 13);
            this.lcFeedbackScore.StyleController = this.layoutControl1;
            this.lcFeedbackScore.TabIndex = 6;
            //
            // lcItemPrice
            //
            this.lcItemPrice.Location = new System.Drawing.Point(112, 48);
            this.lcItemPrice.MinimumSize = new System.Drawing.Size(100, 0);
            this.lcItemPrice.Name = "lcItemPrice";
            this.lcItemPrice.Size = new System.Drawing.Size(100, 13);
            this.lcItemPrice.StyleController = this.layoutControl1;
            this.lcItemPrice.TabIndex = 6;
            //
            // lcPostedTime
            //
            this.lcPostedTime.Location = new System.Drawing.Point(116, 132);
            this.lcPostedTime.Name = "lcPostedTime";
            this.lcPostedTime.Size = new System.Drawing.Size(0, 13);
            this.lcPostedTime.StyleController = this.layoutControl1;
            this.lcPostedTime.TabIndex = 6;
            //
            // lcQuantity
            //
            this.lcQuantity.Location = new System.Drawing.Point(116, 132);
            this.lcQuantity.Name = "lcQuantity";
            this.lcQuantity.Size = new System.Drawing.Size(0, 13);
            this.lcQuantity.StyleController = this.layoutControl1;
            this.lcQuantity.TabIndex = 6;
            //
            // lcSellerName
            //
            this.lcSellerName.Appearance.Options.UseTextOptions = true;
            this.lcSellerName.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.Character;
            this.lcSellerName.Location = new System.Drawing.Point(124, 300);
            this.lcSellerName.Name = "lcSellerName";
            this.lcSellerName.Size = new System.Drawing.Size(213, 13);
            this.lcSellerName.StyleController = this.layoutControl1;
            this.lcSellerName.TabIndex = 6;
            //
            // lcShipping
            //
            this.lcShipping.Appearance.Options.UseTextOptions = true;
            this.lcShipping.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.lcShipping.Location = new System.Drawing.Point(112, 65);
            this.lcShipping.MinimumSize = new System.Drawing.Size(100, 0);
            this.lcShipping.Name = "lcShipping";
            this.lcShipping.Size = new System.Drawing.Size(100, 13);
            this.lcShipping.StyleController = this.layoutControl1;
            this.lcShipping.TabIndex = 6;
            //
            // lcShippingType
            //
            this.lcShippingType.Location = new System.Drawing.Point(116, 132);
            this.lcShippingType.Name = "lcShippingType";
            this.lcShippingType.Size = new System.Drawing.Size(0, 13);
            this.lcShippingType.StyleController = this.layoutControl1;
            this.lcShippingType.TabIndex = 6;
            //
            // lcShipAdditionalItem
            //
            this.lcShipAdditionalItem.Location = new System.Drawing.Point(339, 132);
            this.lcShipAdditionalItem.Name = "lcShipAdditionalItem";
            this.lcShipAdditionalItem.Size = new System.Drawing.Size(0, 13);
            this.lcShipAdditionalItem.StyleController = this.layoutControl1;
            this.lcShipAdditionalItem.TabIndex = 6;
            //
            // lcSoldTime
            //
            this.lcSoldTime.Location = new System.Drawing.Point(339, 132);
            this.lcSoldTime.Name = "lcSoldTime";
            this.lcSoldTime.Size = new System.Drawing.Size(0, 13);
            this.lcSoldTime.StyleController = this.layoutControl1;
            this.lcSoldTime.TabIndex = 6;
            //
            // lcEbayWebsite
            //
            this.lcEbayWebsite.Location = new System.Drawing.Point(116, 132);
            this.lcEbayWebsite.Name = "lcEbayWebsite";
            this.lcEbayWebsite.Size = new System.Drawing.Size(0, 13);
            this.lcEbayWebsite.StyleController = this.layoutControl1;
            this.lcEbayWebsite.TabIndex = 6;
            //
            // lcPageViews
            //
            this.lcPageViews.Location = new System.Drawing.Point(116, 115);
            this.lcPageViews.Name = "lcPageViews";
            this.lcPageViews.Size = new System.Drawing.Size(0, 13);
            this.lcPageViews.StyleController = this.layoutControl1;
            this.lcPageViews.TabIndex = 6;
            //
            // lcUPC
            //
            this.lcUPC.Location = new System.Drawing.Point(339, 144);
            this.lcUPC.Name = "lcUPC";
            this.lcUPC.Size = new System.Drawing.Size(0, 13);
            this.lcUPC.StyleController = this.layoutControl1;
            this.lcUPC.TabIndex = 6;
            //
            // lcVariation
            //
            this.lcVariation.Location = new System.Drawing.Point(339, 144);
            this.lcVariation.Name = "lcVariation";
            this.lcVariation.Size = new System.Drawing.Size(0, 13);
            this.lcVariation.StyleController = this.layoutControl1;
            this.lcVariation.TabIndex = 6;
            //
            // lcPayment
            //
            this.lcPayment.Appearance.Options.UseTextOptions = true;
            this.lcPayment.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.Character;
            this.lcPayment.Location = new System.Drawing.Point(124, 368);
            this.lcPayment.Name = "lcPayment";
            this.lcPayment.Size = new System.Drawing.Size(54, 13);
            this.lcPayment.StyleController = this.layoutControl1;
            this.lcPayment.TabIndex = 6;
            //
            // lblAvailableCount
            //
            this.lblAvailableCount.Location = new System.Drawing.Point(112, 123);
            this.lblAvailableCount.Name = "lblAvailableCount";
            this.lblAvailableCount.Size = new System.Drawing.Size(731, 13);
            this.lblAvailableCount.StyleController = this.layoutControl1;
            this.lblAvailableCount.TabIndex = 6;
            //
            // lblSold
            //
            this.lblSold.Location = new System.Drawing.Point(112, 140);
            this.lblSold.Name = "lblSold";
            this.lblSold.Size = new System.Drawing.Size(731, 13);
            this.lblSold.StyleController = this.layoutControl1;
            this.lblSold.TabIndex = 6;
            //
            // lblPurchaseResult
            //
            this.lblPurchaseResult.AllowHtmlString = true;
            this.lblPurchaseResult.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lblPurchaseResult.Location = new System.Drawing.Point(112, 181);
            this.lblPurchaseResult.Name = "lblPurchaseResult";
            this.lblPurchaseResult.Size = new System.Drawing.Size(731, 13);
            this.lblPurchaseResult.StyleController = this.layoutControl1;
            this.lblPurchaseResult.TabIndex = 6;
            this.lblPurchaseResult.HyperlinkClick += new DevExpress.Utils.HyperlinkClickEventHandler(this.lblPurchaseResult_HyperlinkClick);
            //
            // lblShippingtype
            //
            this.lblShippingtype.Location = new System.Drawing.Point(124, 385);
            this.lblShippingtype.Name = "lblShippingtype";
            this.lblShippingtype.Size = new System.Drawing.Size(213, 13);
            this.lblShippingtype.StyleController = this.layoutControl1;
            this.lblShippingtype.TabIndex = 6;
            //
            // lblHandlingCosts
            //
            this.lblHandlingCosts.Location = new System.Drawing.Point(124, 402);
            this.lblHandlingCosts.Name = "lblHandlingCosts";
            this.lblHandlingCosts.Size = new System.Drawing.Size(213, 13);
            this.lblHandlingCosts.StyleController = this.layoutControl1;
            this.lblHandlingCosts.TabIndex = 6;
            //
            // lblShippingPackage
            //
            this.lblShippingPackage.Location = new System.Drawing.Point(124, 419);
            this.lblShippingPackage.Name = "lblShippingPackage";
            this.lblShippingPackage.Size = new System.Drawing.Size(213, 13);
            this.lblShippingPackage.StyleController = this.layoutControl1;
            this.lblShippingPackage.TabIndex = 6;
            //
            // lblShippingWeight
            //
            this.lblShippingWeight.Location = new System.Drawing.Point(124, 436);
            this.lblShippingWeight.Name = "lblShippingWeight";
            this.lblShippingWeight.Size = new System.Drawing.Size(213, 13);
            this.lblShippingWeight.StyleController = this.layoutControl1;
            this.lblShippingWeight.TabIndex = 6;
            //
            // rdioShippingOptions
            //
            this.rdioShippingOptions.AutoSizeInLayoutControl = true;
            this.rdioShippingOptions.Location = new System.Drawing.Point(24, 232);
            this.rdioShippingOptions.MenuManager = this.ribbonControl1;
            this.rdioShippingOptions.Name = "rdioShippingOptions";
            this.rdioShippingOptions.Properties.Columns = 1;
            this.rdioShippingOptions.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.rdioShippingOptions.Properties.ItemsLayout = DevExpress.XtraEditors.RadioGroupItemsLayout.Column;
            this.rdioShippingOptions.Properties.SelectedIndexChanged += new System.EventHandler(this.rdioShippingOptions_Properties_SelectedIndexChanged);
            this.rdioShippingOptions.Size = new System.Drawing.Size(313, 10);
            this.rdioShippingOptions.StyleController = this.layoutControl1;
            this.rdioShippingOptions.TabIndex = 19;
            //
            // chkSubstractShipping
            //
            this.chkSubstractShipping.Location = new System.Drawing.Point(124, 321);
            this.chkSubstractShipping.MenuManager = this.ribbonControl1;
            this.chkSubstractShipping.Name = "chkSubstractShipping";
            this.chkSubstractShipping.Properties.Caption = "";
            this.chkSubstractShipping.Size = new System.Drawing.Size(100, 19);
            this.chkSubstractShipping.StyleController = this.layoutControl1;
            this.chkSubstractShipping.TabIndex = 24;
            this.chkSubstractShipping.CheckedChanged += new System.EventHandler(this.ChkSubtractShipping_CheckedChanged);
            //
            // boxMakeOfferMessages
            //
            this.boxMakeOfferMessages.EditValue = "";
            this.boxMakeOfferMessages.Location = new System.Drawing.Point(124, 344);
            this.boxMakeOfferMessages.MenuManager = this.ribbonControl1;
            this.boxMakeOfferMessages.Name = "boxMakeOfferMessages";
            editorButtonImageOptions6.SvgImage = global::uBuyFirst.Properties.Resources.Add;
            this.boxMakeOfferMessages.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo, "", -1, true, true, false, editorButtonImageOptions1, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, serializableAppearanceObject2, serializableAppearanceObject3, serializableAppearanceObject4, "Show messages list", null, null, DevExpress.Utils.ToolTipAnchor.Default),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Plus, "", -1, true, true, false, editorButtonImageOptions6, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject21, serializableAppearanceObject22, serializableAppearanceObject23, serializableAppearanceObject24, "Add message", "Add", null, DevExpress.Utils.ToolTipAnchor.Default),
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Clear, "Remove", -1, true, true, false, editorButtonImageOptions7, new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject25, serializableAppearanceObject26, serializableAppearanceObject27, serializableAppearanceObject28, "Remove message", "Remove", null, DevExpress.Utils.ToolTipAnchor.Default)});
            this.boxMakeOfferMessages.Properties.NullText = "";
            this.boxMakeOfferMessages.Properties.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(this.MakeOfferMessages_ButtonClick);
            this.boxMakeOfferMessages.Size = new System.Drawing.Size(213, 20);
            this.boxMakeOfferMessages.StyleController = this.layoutControl1;
            this.boxMakeOfferMessages.TabIndex = 25;
            this.boxMakeOfferMessages.EditValueChanged += new System.EventHandler(this.BoxMakeOfferMessages_EditValueChanged);
            //
            // lciVariation
            //
            this.lciVariation.Control = this.lcVariation;
            this.lciVariation.CustomizationFormText = "Variation";
            this.lciVariation.Location = new System.Drawing.Point(223, 398);
            this.lciVariation.Name = "lciVariation";
            this.lciVariation.Size = new System.Drawing.Size(224, 17);
            this.lciVariation.Text = "Variation";
            this.lciVariation.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciUPC
            //
            this.lciUPC.Control = this.lcUPC;
            this.lciUPC.CustomizationFormText = "UPC";
            this.lciUPC.Location = new System.Drawing.Point(223, 381);
            this.lciUPC.Name = "lciUPC";
            this.lciUPC.Size = new System.Drawing.Size(224, 17);
            this.lciUPC.Text = "UPC";
            this.lciUPC.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciTerm
            //
            this.lciTerm.Control = this.lcTerm;
            this.lciTerm.CustomizationFormText = "Term";
            this.lciTerm.Location = new System.Drawing.Point(223, 364);
            this.lciTerm.Name = "lciTerm";
            this.lciTerm.Size = new System.Drawing.Size(224, 17);
            this.lciTerm.Text = "Term";
            this.lciTerm.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciAlias
            //
            this.lciAlias.Control = this.lcAlias;
            this.lciAlias.CustomizationFormText = "Alias";
            this.lciAlias.Location = new System.Drawing.Point(223, 381);
            this.lciAlias.Name = "lciAlias";
            this.lciAlias.Size = new System.Drawing.Size(224, 17);
            this.lciAlias.Text = "Alias";
            this.lciAlias.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciSoldTime
            //
            this.lciSoldTime.Control = this.lcSoldTime;
            this.lciSoldTime.CustomizationFormText = "Sold Time";
            this.lciSoldTime.Location = new System.Drawing.Point(223, 347);
            this.lciSoldTime.Name = "lciSoldTime";
            this.lciSoldTime.Size = new System.Drawing.Size(224, 17);
            this.lciSoldTime.Text = "Sold Time";
            this.lciSoldTime.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciShipAdditionalItem
            //
            this.lciShipAdditionalItem.Control = this.lcShipAdditionalItem;
            this.lciShipAdditionalItem.CustomizationFormText = "Ship Additional Item";
            this.lciShipAdditionalItem.Location = new System.Drawing.Point(223, 330);
            this.lciShipAdditionalItem.Name = "lciShipAdditionalItem";
            this.lciShipAdditionalItem.Size = new System.Drawing.Size(224, 17);
            this.lciShipAdditionalItem.Text = "Ship Additional Item";
            this.lciShipAdditionalItem.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciShippingType
            //
            this.lciShippingType.Control = this.lcShippingType;
            this.lciShippingType.CustomizationFormText = "Shipping Type";
            this.lciShippingType.Location = new System.Drawing.Point(0, 330);
            this.lciShippingType.Name = "lciShippingType";
            this.lciShippingType.Size = new System.Drawing.Size(447, 17);
            this.lciShippingType.Text = "Shipping Type";
            this.lciShippingType.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciQuantity
            //
            this.lciQuantity.Control = this.lcQuantity;
            this.lciQuantity.CustomizationFormText = "Quantity";
            this.lciQuantity.Location = new System.Drawing.Point(0, 313);
            this.lciQuantity.Name = "lciQuantity";
            this.lciQuantity.Size = new System.Drawing.Size(447, 17);
            this.lciQuantity.Text = "Quantity";
            this.lciQuantity.TextSize = new System.Drawing.Size(0, 0);
            //
            // lciPageViews
            //
            this.lciPageViews.Control = this.lcPageViews;
            this.lciPageViews.CustomizationFormText = "Page Views";
            this.lciPageViews.Location = new System.Drawing.Point(0, 279);
            this.lciPageViews.Name = "lciPageViews";
            this.lciPageViews.Size = new System.Drawing.Size(447, 17);
            this.lciPageViews.Text = "Page Views";
            this.lciPageViews.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciPostedTime
            //
            this.lciPostedTime.Control = this.lcPostedTime;
            this.lciPostedTime.CustomizationFormText = "Posted Time";
            this.lciPostedTime.Location = new System.Drawing.Point(0, 279);
            this.lciPostedTime.Name = "lciPostedTime";
            this.lciPostedTime.Size = new System.Drawing.Size(447, 17);
            this.lciPostedTime.Text = "Posted Time";
            this.lciPostedTime.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciFoundTime
            //
            this.lciFoundTime.Control = this.lcFoundTime;
            this.lciFoundTime.CustomizationFormText = "Found Time";
            this.lciFoundTime.Location = new System.Drawing.Point(0, 245);
            this.lciFoundTime.Name = "lciFoundTime";
            this.lciFoundTime.Size = new System.Drawing.Size(447, 17);
            this.lciFoundTime.Text = "Found Time";
            this.lciFoundTime.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciEbayWebsite
            //
            this.lciEbayWebsite.Control = this.lcEbayWebsite;
            this.lciEbayWebsite.CustomizationFormText = "Ebay Website";
            this.lciEbayWebsite.Location = new System.Drawing.Point(0, 228);
            this.lciEbayWebsite.Name = "lciEbayWebsite";
            this.lciEbayWebsite.Size = new System.Drawing.Size(447, 17);
            this.lciEbayWebsite.Text = "Ebay Website";
            this.lciEbayWebsite.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciCategoryName
            //
            this.lciCategoryName.Control = this.lcCategoryName;
            this.lciCategoryName.CustomizationFormText = "Category Name";
            this.lciCategoryName.Location = new System.Drawing.Point(0, 187);
            this.lciCategoryName.Name = "lciCategoryName";
            this.lciCategoryName.Size = new System.Drawing.Size(447, 17);
            this.lciCategoryName.Text = "Category Name";
            this.lciCategoryName.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciCategoryID
            //
            this.lciCategoryID.Control = this.lcCategoryID;
            this.lciCategoryID.CustomizationFormText = "Category ID";
            this.lciCategoryID.Location = new System.Drawing.Point(0, 170);
            this.lciCategoryID.Name = "lciCategoryID";
            this.lciCategoryID.Size = new System.Drawing.Size(447, 17);
            this.lciCategoryID.Text = "Category ID";
            this.lciCategoryID.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciBestOffer
            //
            this.lciBestOffer.Control = this.lcBestOffer;
            this.lciBestOffer.CustomizationFormText = "Best Offer";
            this.lciBestOffer.Location = new System.Drawing.Point(0, 153);
            this.lciBestOffer.Name = "lciBestOffer";
            this.lciBestOffer.Size = new System.Drawing.Size(447, 17);
            this.lciBestOffer.Text = "Best Offer";
            this.lciBestOffer.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciAutoPay
            //
            this.lciAutoPay.Control = this.lcAutoPay;
            this.lciAutoPay.CustomizationFormText = "Commit To Buy";
            this.lciAutoPay.Location = new System.Drawing.Point(0, 136);
            this.lciAutoPay.Name = "lciAutoPay";
            this.lciAutoPay.Size = new System.Drawing.Size(447, 17);
            this.lciAutoPay.Text = "Commit To Buy";
            this.lciAutoPay.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciConditionDescription
            //
            this.lciConditionDescription.Control = this.lcConditionDescription;
            this.lciConditionDescription.CustomizationFormText = "Condition Description";
            this.lciConditionDescription.Location = new System.Drawing.Point(5, 102);
            this.lciConditionDescription.Name = "lciConditionDescription";
            this.lciConditionDescription.Size = new System.Drawing.Size(617, 17);
            this.lciConditionDescription.StartNewLine = true;
            this.lciConditionDescription.Text = "Condition Description";
            this.lciConditionDescription.TextSize = new System.Drawing.Size(101, 13);
            //
            // lciReturns
            //
            this.lciReturns.Control = this.lcReturns;
            this.lciReturns.CustomizationFormText = "Returns";
            this.lciReturns.Location = new System.Drawing.Point(0, 34);
            this.lciReturns.Name = "lciReturns";
            this.lciReturns.Size = new System.Drawing.Size(460, 17);
            this.lciReturns.Text = "Returns";
            this.lciReturns.TextSize = new System.Drawing.Size(101, 13);
            //
            // layoutControlGroup1
            //
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciTitle,
            this.layoutControlItem2,
            this.lciEbayAccount,
            this.lciAvailable,
            this.lciTotalPrice,
            this.buttonBuyItNow,
            this.lciItemPrice,
            this.lciPurchaseResult,
            this.simpleSeparator1,
            this.emptySpaceItem2,
            this.emptySpaceItem1,
            this.tabbedControlGroup1,
            this.lciSold,
            this.lciQuantityEdit,
            this.lciShipping,
            this.lcTax});
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.Size = new System.Drawing.Size(855, 573);
            //
            // lciTitle
            //
            this.lciTitle.Control = this.lcTitle;
            this.lciTitle.CustomizationFormText = "Title";
            this.lciTitle.Location = new System.Drawing.Point(0, 0);
            this.lciTitle.Name = "lciTitle";
            this.lciTitle.Size = new System.Drawing.Size(835, 17);
            this.lciTitle.StartNewLine = true;
            this.lciTitle.Text = "Title";
            this.lciTitle.TextSize = new System.Drawing.Size(96, 13);
            //
            // layoutControlItem2
            //
            this.layoutControlItem2.Control = this.pictureEdit1;
            this.layoutControlItem2.Location = new System.Drawing.Point(341, 186);
            this.layoutControlItem2.MinSize = new System.Drawing.Size(100, 100);
            this.layoutControlItem2.Name = "layoutControlItem2";
            this.layoutControlItem2.Size = new System.Drawing.Size(494, 367);
            this.layoutControlItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem2.Text = "Image";
            this.layoutControlItem2.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem2.TextVisible = false;
            //
            // lciEbayAccount
            //
            this.lciEbayAccount.Control = this.lcEbayAccount;
            this.lciEbayAccount.Location = new System.Drawing.Point(0, 145);
            this.lciEbayAccount.Name = "lciEbayAccount";
            this.lciEbayAccount.Size = new System.Drawing.Size(349, 24);
            this.lciEbayAccount.Text = "eBay Account";
            this.lciEbayAccount.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciAvailable
            //
            this.lciAvailable.Control = this.lblAvailableCount;
            this.lciAvailable.CustomizationFormText = "Available";
            this.lciAvailable.Location = new System.Drawing.Point(0, 111);
            this.lciAvailable.Name = "lciAvailable";
            this.lciAvailable.Size = new System.Drawing.Size(835, 17);
            this.lciAvailable.Text = "Available";
            this.lciAvailable.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciTotalPrice
            //
            this.lciTotalPrice.Control = this.lcTotalPrice;
            this.lciTotalPrice.CustomizationFormText = "Total Price";
            this.lciTotalPrice.Location = new System.Drawing.Point(0, 19);
            this.lciTotalPrice.Name = "lciTotalPrice";
            this.lciTotalPrice.Size = new System.Drawing.Size(204, 17);
            this.lciTotalPrice.Text = "Total Price";
            this.lciTotalPrice.TextSize = new System.Drawing.Size(96, 13);
            //
            // buttonBuyItNow
            //
            this.buttonBuyItNow.AllowHide = false;
            this.buttonBuyItNow.Control = this.btnBuyItnow;
            this.buttonBuyItNow.CustomizationFormText = "Buy it now button";
            this.buttonBuyItNow.Location = new System.Drawing.Point(557, 19);
            this.buttonBuyItNow.MinSize = new System.Drawing.Size(68, 26);
            this.buttonBuyItNow.Name = "buttonBuyItNow";
            this.buttonBuyItNow.Padding = new DevExpress.XtraLayout.Utils.Padding(20, 20, 5, 5);
            this.buttonBuyItNow.Size = new System.Drawing.Size(278, 92);
            this.buttonBuyItNow.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.buttonBuyItNow.Text = "Buy";
            this.buttonBuyItNow.TextSize = new System.Drawing.Size(0, 0);
            this.buttonBuyItNow.TextVisible = false;
            //
            // lciItemPrice
            //
            this.lciItemPrice.Control = this.lcItemPrice;
            this.lciItemPrice.CustomizationFormText = "Item Price";
            this.lciItemPrice.Location = new System.Drawing.Point(0, 36);
            this.lciItemPrice.Name = "lciItemPrice";
            this.lciItemPrice.Size = new System.Drawing.Size(204, 17);
            this.lciItemPrice.Text = "Item Price";
            this.lciItemPrice.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciPurchaseResult
            //
            this.lciPurchaseResult.Control = this.lblPurchaseResult;
            this.lciPurchaseResult.CustomizationFormText = "Result";
            this.lciPurchaseResult.Location = new System.Drawing.Point(0, 169);
            this.lciPurchaseResult.Name = "lciPurchaseResult";
            this.lciPurchaseResult.Size = new System.Drawing.Size(835, 17);
            this.lciPurchaseResult.StartNewLine = true;
            this.lciPurchaseResult.Text = "Result";
            this.lciPurchaseResult.TextSize = new System.Drawing.Size(96, 13);
            //
            // simpleSeparator1
            //
            this.simpleSeparator1.AllowHotTrack = false;
            this.simpleSeparator1.Location = new System.Drawing.Point(0, 17);
            this.simpleSeparator1.Name = "simpleSeparator1";
            this.simpleSeparator1.Size = new System.Drawing.Size(835, 2);
            //
            // emptySpaceItem2
            //
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(349, 145);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(486, 24);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            //
            // emptySpaceItem1
            //
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(204, 19);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(353, 92);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            //
            // tabbedControlGroup1
            //
            this.tabbedControlGroup1.Location = new System.Drawing.Point(0, 186);
            this.tabbedControlGroup1.Name = "tabbedControlGroup1";
            this.tabbedControlGroup1.SelectedTabPage = this.layoutControlGroup2;
            this.tabbedControlGroup1.Size = new System.Drawing.Size(341, 367);
            this.tabbedControlGroup1.TabPages.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup2,
            this.lciMakeOffertab,
            this.lciShippingOptionsTab});
            //
            // layoutControlGroup2
            //
            this.layoutControlGroup2.CustomizationFormText = "Group Details";
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciItemID,
            this.lciLocation,
            this.lciFromCountry,
            this.lciToCountry,
            this.lciSellerName,
            this.lciFeedbackScore,
            this.lciFeedbackRating,
            this.lciCondition,
            this.lciPayment,
            this.lciShippingtype2,
            this.lciHandlingCosts,
            this.lciShippingPackage,
            this.lciShippingWeight,
            this.emptySpaceItem3,
            this.lciBids,
            this.lciListingType,
            this.lciAuctionPrice,
            this.lciShippingDays});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(317, 321);
            this.layoutControlGroup2.Text = "Item Details";
            //
            // lciItemID
            //
            this.lciItemID.AllowHtmlStringInCaption = true;
            this.lciItemID.Control = this.lcItemID;
            this.lciItemID.Location = new System.Drawing.Point(0, 0);
            this.lciItemID.Name = "lciItemID";
            this.lciItemID.Size = new System.Drawing.Size(317, 17);
            this.lciItemID.Text = "ItemID";
            this.lciItemID.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciLocation
            //
            this.lciLocation.Control = this.lcLocation;
            this.lciLocation.CustomizationFormText = "Location";
            this.lciLocation.Location = new System.Drawing.Point(0, 17);
            this.lciLocation.Name = "lciLocation";
            this.lciLocation.Size = new System.Drawing.Size(317, 17);
            this.lciLocation.Text = "Location";
            this.lciLocation.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciFromCountry
            //
            this.lciFromCountry.Control = this.lcFromCountry;
            this.lciFromCountry.CustomizationFormText = "From Country";
            this.lciFromCountry.Location = new System.Drawing.Point(0, 34);
            this.lciFromCountry.Name = "lciFromCountry";
            this.lciFromCountry.Size = new System.Drawing.Size(317, 17);
            this.lciFromCountry.Text = "From Country";
            this.lciFromCountry.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciToCountry
            //
            this.lciToCountry.Control = this.lcToCountry;
            this.lciToCountry.CustomizationFormText = "To Country";
            this.lciToCountry.Location = new System.Drawing.Point(0, 51);
            this.lciToCountry.Name = "lciToCountry";
            this.lciToCountry.Size = new System.Drawing.Size(317, 17);
            this.lciToCountry.Text = "To Country";
            this.lciToCountry.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciSellerName
            //
            this.lciSellerName.Control = this.lcSellerName;
            this.lciSellerName.CustomizationFormText = "Seller Name";
            this.lciSellerName.Location = new System.Drawing.Point(0, 68);
            this.lciSellerName.Name = "lciSellerName";
            this.lciSellerName.Size = new System.Drawing.Size(317, 17);
            this.lciSellerName.Text = "Seller Name";
            this.lciSellerName.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciFeedbackScore
            //
            this.lciFeedbackScore.Control = this.lcFeedbackScore;
            this.lciFeedbackScore.CustomizationFormText = "Feedback Score";
            this.lciFeedbackScore.Location = new System.Drawing.Point(0, 85);
            this.lciFeedbackScore.Name = "lciFeedbackScore";
            this.lciFeedbackScore.Size = new System.Drawing.Size(317, 17);
            this.lciFeedbackScore.Text = "Feedback Count";
            this.lciFeedbackScore.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciFeedbackRating
            //
            this.lciFeedbackRating.Control = this.lcFeedbackRating;
            this.lciFeedbackRating.CustomizationFormText = "Feedback Rating";
            this.lciFeedbackRating.Location = new System.Drawing.Point(0, 102);
            this.lciFeedbackRating.Name = "lciFeedbackRating";
            this.lciFeedbackRating.Size = new System.Drawing.Size(317, 17);
            this.lciFeedbackRating.Text = "Feedback %";
            this.lciFeedbackRating.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciCondition
            //
            this.lciCondition.Control = this.lcCondition;
            this.lciCondition.CustomizationFormText = "Condition";
            this.lciCondition.Location = new System.Drawing.Point(0, 119);
            this.lciCondition.Name = "lciCondition";
            this.lciCondition.Size = new System.Drawing.Size(317, 17);
            this.lciCondition.Text = "Condition";
            this.lciCondition.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciPayment
            //
            this.lciPayment.Control = this.lcPayment;
            this.lciPayment.CustomizationFormText = "Payment";
            this.lciPayment.Location = new System.Drawing.Point(0, 136);
            this.lciPayment.Name = "lciPayment";
            this.lciPayment.Size = new System.Drawing.Size(158, 17);
            this.lciPayment.Text = "Payment";
            this.lciPayment.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShippingtype2
            //
            this.lciShippingtype2.Control = this.lblShippingtype;
            this.lciShippingtype2.CustomizationFormText = "Shipping type";
            this.lciShippingtype2.Location = new System.Drawing.Point(0, 153);
            this.lciShippingtype2.Name = "lciShippingtype2";
            this.lciShippingtype2.Size = new System.Drawing.Size(317, 17);
            this.lciShippingtype2.Text = "Shipping type";
            this.lciShippingtype2.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciHandlingCosts
            //
            this.lciHandlingCosts.Control = this.lblHandlingCosts;
            this.lciHandlingCosts.CustomizationFormText = "Handling Costs";
            this.lciHandlingCosts.Location = new System.Drawing.Point(0, 170);
            this.lciHandlingCosts.Name = "lciHandlingCosts";
            this.lciHandlingCosts.Size = new System.Drawing.Size(317, 17);
            this.lciHandlingCosts.Text = "Handling Costs";
            this.lciHandlingCosts.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShippingPackage
            //
            this.lciShippingPackage.Control = this.lblShippingPackage;
            this.lciShippingPackage.CustomizationFormText = "Shipping Package";
            this.lciShippingPackage.Location = new System.Drawing.Point(0, 187);
            this.lciShippingPackage.Name = "lciShippingPackage";
            this.lciShippingPackage.Size = new System.Drawing.Size(317, 17);
            this.lciShippingPackage.Text = "Shipping Package";
            this.lciShippingPackage.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShippingWeight
            //
            this.lciShippingWeight.Control = this.lblShippingWeight;
            this.lciShippingWeight.CustomizationFormText = "Shipping Weight";
            this.lciShippingWeight.Location = new System.Drawing.Point(0, 204);
            this.lciShippingWeight.Name = "lciShippingWeight";
            this.lciShippingWeight.Size = new System.Drawing.Size(317, 17);
            this.lciShippingWeight.Text = "Shipping Weight";
            this.lciShippingWeight.TextSize = new System.Drawing.Size(96, 13);
            //
            // emptySpaceItem3
            //
            this.emptySpaceItem3.AllowHotTrack = false;
            this.emptySpaceItem3.Location = new System.Drawing.Point(0, 272);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(317, 49);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            //
            // lciBids
            //
            this.lciBids.Control = this.lcBids;
            this.lciBids.CustomizationFormText = "Bids";
            this.lciBids.Location = new System.Drawing.Point(0, 221);
            this.lciBids.Name = "lciBids";
            this.lciBids.Size = new System.Drawing.Size(317, 17);
            this.lciBids.Text = "Bids";
            this.lciBids.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciListingType
            //
            this.lciListingType.Control = this.lcListingType;
            this.lciListingType.Location = new System.Drawing.Point(0, 238);
            this.lciListingType.Name = "lciListingType";
            this.lciListingType.Size = new System.Drawing.Size(317, 17);
            this.lciListingType.Text = "Listing Type";
            this.lciListingType.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciAuctionPrice
            //
            this.lciAuctionPrice.Control = this.lcAuctionPrice;
            this.lciAuctionPrice.Location = new System.Drawing.Point(0, 255);
            this.lciAuctionPrice.Name = "lciAuctionPrice";
            this.lciAuctionPrice.Size = new System.Drawing.Size(317, 17);
            this.lciAuctionPrice.Text = "Auction Price";
            this.lciAuctionPrice.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShippingDays
            //
            this.lciShippingDays.Control = this.lcShippingDays;
            this.lciShippingDays.CustomizationFormText = "Shipping Days";
            this.lciShippingDays.Location = new System.Drawing.Point(158, 136);
            this.lciShippingDays.Name = "lciShippingDays";
            this.lciShippingDays.Size = new System.Drawing.Size(159, 17);
            this.lciShippingDays.Text = "Shipping Days";
            this.lciShippingDays.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciMakeOffertab
            //
            this.lciMakeOffertab.AllowHide = false;
            this.lciMakeOffertab.CustomizationFormText = "Make Offer";
            this.lciMakeOffertab.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciBestOfferSellerMessage,
            this.lciBestOfferQuantity,
            this.lciBestOfferButton,
            this.lciBestOfferPricePerItem,
            this.lciItemPricePercent,
            this.lciOfferTotalPrice,
            this.lciSubstractShipping,
            this.lciMakeOfferMessages});
            this.lciMakeOffertab.Location = new System.Drawing.Point(0, 0);
            this.lciMakeOffertab.Name = "lciMakeOffertab";
            this.lciMakeOffertab.Size = new System.Drawing.Size(317, 321);
            this.lciMakeOffertab.Text = "Make Offer";
            //
            // lciBestOfferSellerMessage
            //
            this.lciBestOfferSellerMessage.AppearanceItemCaption.Options.UseTextOptions = true;
            this.lciBestOfferSellerMessage.AppearanceItemCaption.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.lciBestOfferSellerMessage.Control = this.memoEditBestOfferMessage;
            this.lciBestOfferSellerMessage.CustomizationFormText = "Message to seller";
            this.lciBestOfferSellerMessage.Location = new System.Drawing.Point(0, 136);
            this.lciBestOfferSellerMessage.MinSize = new System.Drawing.Size(110, 20);
            this.lciBestOfferSellerMessage.Name = "lciBestOfferSellerMessage";
            this.lciBestOfferSellerMessage.Size = new System.Drawing.Size(317, 185);
            this.lciBestOfferSellerMessage.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.lciBestOfferSellerMessage.Text = "Message to seller";
            this.lciBestOfferSellerMessage.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciBestOfferQuantity
            //
            this.lciBestOfferQuantity.Control = this.spinEditBestOfferItemQuantity;
            this.lciBestOfferQuantity.CustomizationFormText = "Best Offer Quantity";
            this.lciBestOfferQuantity.Location = new System.Drawing.Point(0, 65);
            this.lciBestOfferQuantity.Name = "lciBestOfferQuantity";
            this.lciBestOfferQuantity.Size = new System.Drawing.Size(204, 24);
            this.lciBestOfferQuantity.Text = "Offer Quantity";
            this.lciBestOfferQuantity.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciBestOfferButton
            //
            this.lciBestOfferButton.AllowHide = false;
            this.lciBestOfferButton.Control = this.simpleButtonBestOffer;
            this.lciBestOfferButton.CustomizationFormText = "Make Offer Button";
            this.lciBestOfferButton.Location = new System.Drawing.Point(204, 17);
            this.lciBestOfferButton.MinSize = new System.Drawing.Size(113, 32);
            this.lciBestOfferButton.Name = "lciBestOfferButton";
            this.lciBestOfferButton.Padding = new DevExpress.XtraLayout.Utils.Padding(2, 2, 15, 15);
            this.lciBestOfferButton.Size = new System.Drawing.Size(113, 95);
            this.lciBestOfferButton.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.lciBestOfferButton.Text = "Make Offer";
            this.lciBestOfferButton.TextSize = new System.Drawing.Size(0, 0);
            this.lciBestOfferButton.TextVisible = false;
            //
            // lciBestOfferPricePerItem
            //
            this.lciBestOfferPricePerItem.Control = this.spinEditBestOfferPricePerItem;
            this.lciBestOfferPricePerItem.CustomizationFormText = "Best Offer Price";
            this.lciBestOfferPricePerItem.Location = new System.Drawing.Point(0, 17);
            this.lciBestOfferPricePerItem.Name = "lciBestOfferPricePerItem";
            this.lciBestOfferPricePerItem.Size = new System.Drawing.Size(204, 24);
            this.lciBestOfferPricePerItem.Text = "Offer Price Per Item";
            this.lciBestOfferPricePerItem.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciItemPricePercent
            //
            this.lciItemPricePercent.Control = this.spinEditOfferPercentage;
            this.lciItemPricePercent.CustomizationFormText = "Offer percentage ";
            this.lciItemPricePercent.Location = new System.Drawing.Point(0, 41);
            this.lciItemPricePercent.Name = "lciItemPricePercent";
            this.lciItemPricePercent.Size = new System.Drawing.Size(204, 24);
            this.lciItemPricePercent.Text = "Offer percentage";
            this.lciItemPricePercent.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciOfferTotalPrice
            //
            this.lciOfferTotalPrice.Control = this.lcOfferTotalPrice;
            this.lciOfferTotalPrice.CustomizationFormText = "Offer Total Price";
            this.lciOfferTotalPrice.Location = new System.Drawing.Point(0, 0);
            this.lciOfferTotalPrice.Name = "lciOfferTotalPrice";
            this.lciOfferTotalPrice.Size = new System.Drawing.Size(317, 17);
            this.lciOfferTotalPrice.Text = "Offer total price";
            this.lciOfferTotalPrice.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciSubstractShipping
            //
            this.lciSubstractShipping.Control = this.chkSubstractShipping;
            this.lciSubstractShipping.CustomizationFormText = "Substract Shipping";
            this.lciSubstractShipping.Location = new System.Drawing.Point(0, 89);
            this.lciSubstractShipping.Name = "lciSubstractShipping";
            this.lciSubstractShipping.Size = new System.Drawing.Size(204, 23);
            this.lciSubstractShipping.Text = "Substract shipping";
            this.lciSubstractShipping.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciMakeOfferMessages
            //
            this.lciMakeOfferMessages.Control = this.boxMakeOfferMessages;
            this.lciMakeOfferMessages.CustomizationFormText = "Make Offer Messages";
            this.lciMakeOfferMessages.Location = new System.Drawing.Point(0, 112);
            this.lciMakeOfferMessages.Name = "lciMakeOfferMessages";
            this.lciMakeOfferMessages.Size = new System.Drawing.Size(317, 24);
            this.lciMakeOfferMessages.Text = "Messages";
            this.lciMakeOfferMessages.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShippingOptionsTab
            //
            this.lciShippingOptionsTab.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciShippingOptionsRadioGroup});
            this.lciShippingOptionsTab.Location = new System.Drawing.Point(0, 0);
            this.lciShippingOptionsTab.Name = "lciShippingOptionsTab";
            this.lciShippingOptionsTab.Size = new System.Drawing.Size(317, 321);
            this.lciShippingOptionsTab.Text = "Shipping Options";
            this.lciShippingOptionsTab.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
            //
            // lciShippingOptionsRadioGroup
            //
            this.lciShippingOptionsRadioGroup.Control = this.rdioShippingOptions;
            this.lciShippingOptionsRadioGroup.CustomizationFormText = "Shipping options";
            this.lciShippingOptionsRadioGroup.Location = new System.Drawing.Point(0, 0);
            this.lciShippingOptionsRadioGroup.Name = "lciShippingOptionsRadioGroup";
            this.lciShippingOptionsRadioGroup.Size = new System.Drawing.Size(317, 321);
            this.lciShippingOptionsRadioGroup.Text = "Shipping options";
            this.lciShippingOptionsRadioGroup.TextSize = new System.Drawing.Size(0, 0);
            this.lciShippingOptionsRadioGroup.TextVisible = false;
            //
            // lciSold
            //
            this.lciSold.Control = this.lblSold;
            this.lciSold.CustomizationFormText = "Sold";
            this.lciSold.Location = new System.Drawing.Point(0, 128);
            this.lciSold.Name = "lciSold";
            this.lciSold.Size = new System.Drawing.Size(835, 17);
            this.lciSold.Text = "Sold";
            this.lciSold.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciQuantityEdit
            //
            this.lciQuantityEdit.Control = this.spinEditBuyNowQuantity;
            this.lciQuantityEdit.CustomizationFormText = "Buy It Now Quantity";
            this.lciQuantityEdit.Location = new System.Drawing.Point(0, 87);
            this.lciQuantityEdit.Name = "lciQuantityEdit";
            this.lciQuantityEdit.Size = new System.Drawing.Size(204, 24);
            this.lciQuantityEdit.Text = "Quantity";
            this.lciQuantityEdit.TextSize = new System.Drawing.Size(96, 13);
            //
            // lciShipping
            //
            this.lciShipping.Control = this.lcShipping;
            this.lciShipping.CustomizationFormText = "Shipping";
            this.lciShipping.Location = new System.Drawing.Point(0, 53);
            this.lciShipping.Name = "lciShipping";
            this.lciShipping.Size = new System.Drawing.Size(204, 17);
            this.lciShipping.StartNewLine = true;
            this.lciShipping.Text = "Shipping";
            this.lciShipping.TextSize = new System.Drawing.Size(96, 13);
            //
            // lcTax
            //
            this.lcTax.Control = this.lciTax;
            this.lcTax.CustomizationFormText = "Tax";
            this.lcTax.Location = new System.Drawing.Point(0, 70);
            this.lcTax.Name = "lcTax";
            this.lcTax.Size = new System.Drawing.Size(204, 17);
            this.lcTax.Text = "Tax";
            this.lcTax.TextSize = new System.Drawing.Size(96, 13);
            //
            // popupMenuItemDetails
            //
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemCustomizeLayout);
            this.popupMenuItemDetails.ItemLinks.Add(this.ResetLayout);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemIncreaseFont);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemDecreaseFont);
            this.popupMenuItemDetails.ItemLinks.Add(this.barButtonItemHelp);
            this.popupMenuItemDetails.Name = "popupMenuItemDetails";
            this.popupMenuItemDetails.Ribbon = this.ribbonControl1;
            //
            // linkeBayPrivacyPolicy
            //
            this.linkeBayPrivacyPolicy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.linkeBayPrivacyPolicy.Cursor = System.Windows.Forms.Cursors.Hand;
            this.linkeBayPrivacyPolicy.Location = new System.Drawing.Point(604, 592);
            this.linkeBayPrivacyPolicy.Name = "linkeBayPrivacyPolicy";
            this.linkeBayPrivacyPolicy.Size = new System.Drawing.Size(92, 13);
            this.linkeBayPrivacyPolicy.TabIndex = 32;
            this.linkeBayPrivacyPolicy.Tag = "http://pages.ebay.com/help/policies/privacy-policy.html";
            this.linkeBayPrivacyPolicy.Text = "eBay Privacy Policy";
            this.linkeBayPrivacyPolicy.Visible = false;
            //
            // linkeBayUserAgreement
            //
            this.linkeBayUserAgreement.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.linkeBayUserAgreement.Cursor = System.Windows.Forms.Cursors.Hand;
            this.linkeBayUserAgreement.Location = new System.Drawing.Point(604, 611);
            this.linkeBayUserAgreement.Name = "linkeBayUserAgreement";
            this.linkeBayUserAgreement.Size = new System.Drawing.Size(105, 13);
            this.linkeBayUserAgreement.TabIndex = 31;
            this.linkeBayUserAgreement.Tag = "http://pages.ebay.com/help/policies/user-agreement.html";
            this.linkeBayUserAgreement.Text = "eBay User Agreement";
            this.linkeBayUserAgreement.Visible = false;
            //
            // pictureEditLogo
            //
            this.pictureEditLogo.Cursor = System.Windows.Forms.Cursors.Default;
            this.pictureEditLogo.EditValue = global::uBuyFirst.Properties.Resources.ebay;
            this.pictureEditLogo.Location = new System.Drawing.Point(691, 580);
            this.pictureEditLogo.MenuManager = this.ribbonControl1;
            this.pictureEditLogo.Name = "pictureEditLogo";
            this.pictureEditLogo.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.pictureEditLogo.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Auto;
            this.pictureEditLogo.Size = new System.Drawing.Size(128, 38);
            this.pictureEditLogo.TabIndex = 33;
            this.pictureEditLogo.Visible = false;
            //
            // FormBid
            //
            this.AcceptButton = this.btnBuyItnow;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(855, 630);
            this.Controls.Add(this.pictureEditLogo);
            this.Controls.Add(this.linkeBayPrivacyPolicy);
            this.Controls.Add(this.linkeBayUserAgreement);
            this.Controls.Add(this.layoutControl1);
            this.Controls.Add(this.grpShipping);
            this.Controls.Add(this.grpBestOffer);
            this.Controls.Add(this.grpAuction);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormBid.IconOptions.Icon")));
            this.KeyPreview = true;
            this.Name = "FormBid";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Purchase Preview";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormBid_FormClosing);
            this.Load += new System.EventHandler(this.FormBid_Load);
            this.Shown += new System.EventHandler(this.FormBid_Shown);
            this.LocationChanged += new System.EventHandler(this.FormBid_LocationChanged);
            this.SizeChanged += new System.EventHandler(this.FormBid_SizeChanged);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormClose_OnESC);
            ((System.ComponentModel.ISupportInitialize)(this.numPrice)).EndInit();
            this.grpAuction.ResumeLayout(false);
            this.grpAuction.PerformLayout();
            this.grpBestOffer.ResumeLayout(false);
            this.grpBestOffer.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numBestOfferAmount)).EndInit();
            this.grpShipping.ResumeLayout(false);
            this.grpShipping.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.spinEditOfferPercentage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoEditBestOfferMessage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBestOfferItemQuantity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBestOfferPricePerItem.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditBuyNowQuantity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcEbayAccount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdioShippingOptions.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSubstractShipping.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxMakeOfferMessages.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciVariation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciUPC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTerm)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAlias)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSoldTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipAdditionalItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPageViews)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostedTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFoundTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayWebsite)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCategoryID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOffer)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAutoPay)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciConditionDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciReturns)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTitle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciEbayAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAvailable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciTotalPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.buttonBuyItNow)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPurchaseResult)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabbedControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciLocation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFromCountry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciToCountry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSellerName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackScore)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFeedbackRating)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCondition)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPayment)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingtype2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciHandlingCosts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingPackage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingWeight)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBids)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciListingType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAuctionPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingDays)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciMakeOffertab)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferSellerMessage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciBestOfferPricePerItem)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciItemPricePercent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciOfferTotalPrice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSubstractShipping)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciMakeOfferMessages)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingOptionsTab)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShippingOptionsRadioGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciSold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciQuantityEdit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciShipping)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lcTax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuItemDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureEditLogo.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private NumericUpDown numPrice;
        private Label lblCurrentBid;
        private Label lblCurrentBidNum;
        private Label lblBidsNum;
        private Panel grpAuction;
        private Label lblValShipping;
        private Panel grpBestOffer;
        private Label lblBestOffer;
        private Button btnBestOffer;
        private Label lblValBidsNumberForBO;
        private NumericUpDown numBestOfferAmount;
        private Label lblValCurrentPriceForBestOffer;
        private ListView LstShipSvc;
        private ColumnHeader ClmService;
        private ColumnHeader ClmCost;
        private ColumnHeader ClmAddedCost;
        private ColumnHeader ClmShipLocation;
        private Panel grpShipping;
        private LinkLabel linkLblShipping;
        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        public LayoutControl layoutControl1;
        private PictureEdit pictureEdit1;
        private SimpleButton btnBuyItnow;
        private ComboBoxEdit lcEbayAccount;
        private LabelControl lcItemID;
        private LabelControl lcTerm;
        private LabelControl lcAlias;
        private LabelControl lcTitle;
        private LabelControl lcTotalPrice;
        private LabelControl lcCondition;
        private LabelControl lcReturns;
        private LabelControl lcBestOffer;
        private LabelControl lcFoundTime;
        private LabelControl lcLocation;
        private LabelControl lcFromCountry;
        private LabelControl lcToCountry;
        private LabelControl lcAutoPay;
        private LabelControl lcCategoryID;
        private LabelControl lcCategoryName;
        private LabelControl lcConditionDescription;
        private LabelControl lcFeedbackRating;
        private LabelControl lcFeedbackScore;
        private LabelControl lcItemPrice;
        private LabelControl lcPostedTime;
        private LabelControl lcQuantity;
        private LabelControl lcSellerName;
        private LabelControl lcShipping;
        private LabelControl lcShippingType;
        private LabelControl lcShipAdditionalItem;
        private LabelControl lcSoldTime;
        private LabelControl lcEbayWebsite;
        private LabelControl lcPageViews;
        private LabelControl lcUPC;
        private LabelControl lcVariation;
        private LayoutControlItem lciVariation;
        private LayoutControlItem lciUPC;
        private LayoutControlItem lciTerm;
        private LayoutControlItem lciAlias;
        private LayoutControlItem lciSoldTime;
        private LayoutControlItem lciShipAdditionalItem;
        private LayoutControlItem lciShippingType;
        private LayoutControlItem lciQuantity;
        private LayoutControlItem lciPageViews;
        private LayoutControlItem lciPostedTime;
        private LayoutControlItem lciFoundTime;
        private LayoutControlItem lciEbayWebsite;
        private LayoutControlItem lciCategoryName;
        private LayoutControlItem lciCategoryID;
        private LayoutControlItem lciBestOffer;
        private LayoutControlItem lciAutoPay;
        private LayoutControlGroup layoutControlGroup1;
        private LayoutControlItem lciTitle;
        private LayoutControlItem lciTotalPrice;
        private LayoutControlItem lciReturns;
        private LayoutControlItem lciShipping;
        private LayoutControlItem lciItemPrice;
        private LayoutControlItem lciConditionDescription;
        private LayoutControlItem lciEbayAccount;
        private LayoutControlItem layoutControlItem2;
        private LayoutControlItem buttonBuyItNow;
        private SpinEdit spinEditBuyNowQuantity;
        private LayoutControlItem lciQuantityEdit;
        private LabelControl lcPayment;
        private LabelControl lblAvailableCount;
        private LabelControl lblSold;
        private LayoutControlItem lciAvailable;
        private LayoutControlItem lciSold;
        private LabelControl lblPurchaseResult;
        private LayoutControlItem lciPurchaseResult;
        private LabelControl lblShippingtype;
        private LabelControl lblHandlingCosts;
        private LabelControl lblShippingPackage;
        private LabelControl lblShippingWeight;
        private SimpleSeparator simpleSeparator1;
        private EmptySpaceItem emptySpaceItem2;
        private BarButtonItem barButtonItemAppearance;
        private PopupMenu popupMenuItemDetails;
        private BarButtonItem barButtonItemCustomizeLayout;
        private BarButtonItem ResetLayout;
        private BarButtonItem barButtonItemIncreaseFont;
        private BarButtonItem barButtonItemDecreaseFont;
        private EmptySpaceItem emptySpaceItem1;
        private SimpleButton simpleButtonBestOffer;
        private TabbedControlGroup tabbedControlGroup1;
        private LayoutControlGroup lciMakeOffertab;
        private LayoutControlItem lciBestOfferButton;
        private LayoutControlGroup layoutControlGroup2;
        private LayoutControlItem lciItemID;
        private LayoutControlItem lciLocation;
        private LayoutControlItem lciFromCountry;
        private LayoutControlItem lciToCountry;
        private LayoutControlItem lciSellerName;
        private LayoutControlItem lciFeedbackScore;
        private LayoutControlItem lciFeedbackRating;
        private LayoutControlItem lciCondition;
        private LayoutControlItem lciPayment;
        private LayoutControlItem lciShippingtype2;
        private LayoutControlItem lciHandlingCosts;
        private LayoutControlItem lciShippingPackage;
        private LayoutControlItem lciShippingWeight;
        private EmptySpaceItem emptySpaceItem3;
        private SpinEdit spinEditBestOfferPricePerItem;
        private LayoutControlItem lciBestOfferPricePerItem;
        private MemoEdit memoEditBestOfferMessage;
        private SpinEdit spinEditBestOfferItemQuantity;
        private LayoutControlItem lciBestOfferQuantity;
        private LayoutControlItem lciBestOfferSellerMessage;
        private SpinEdit spinEditOfferPercentage;
        private LayoutControlItem lciItemPricePercent;
        private LabelControl lcOfferTotalPrice;
        private LayoutControlItem lciOfferTotalPrice;
        private BarButtonItem barButtonItemHelp;
        private HyperlinkLabelControl linkeBayPrivacyPolicy;
        private HyperlinkLabelControl linkeBayUserAgreement;
        private PictureEdit pictureEditLogo;
        private LabelControl lciTax;
        private LayoutControlItem lcTax;
        private RadioGroup rdioShippingOptions;
        private LayoutControlGroup lciShippingOptionsTab;
        private LayoutControlItem lciShippingOptionsRadioGroup;
        private LabelControl lcListingType;
        private LabelControl lcBids;
        private LayoutControlItem lciBids;
        private LayoutControlItem lciListingType;
        private LabelControl lcAuctionPrice;
        private LayoutControlItem lciAuctionPrice;
        private CheckEdit chkSubstractShipping;
        private LayoutControlItem lciSubstractShipping;
        private LookUpEdit boxMakeOfferMessages;
        private LayoutControlItem lciMakeOfferMessages;
        private LabelControl lcShippingDays;
        private LayoutControlItem lciShippingDays;
    }
}
