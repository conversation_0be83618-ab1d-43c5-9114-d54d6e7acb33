# Technical Architecture

## 🏗️ System Architecture Overview

### Current Architecture
```
QueryList (Root)
├── List<Keyword2Find> ChildrenCore
    ├── Keyword2Find (Level 0)
    │   ├── Properties (Alias, Keywords, etc.)
    │   └── List<ChildTerm> ChildrenCore
    │       └── ChildTerm (Level 1)
    └── ...
```

### Target Architecture with Folders
```
QueryList (Root)
├── List<KeywordFolder> Folders
    ├── KeywordFolder (Level 0)
    │   ├── Properties (Name, Id, etc.)
    │   ├── List<KeywordFolder> Children (nested folders)
    │   └── List<Keyword2Find> Keywords
    │       ├── Keyword2Find (Level 1)
    │       │   ├── Properties (Alias, Keywords, etc.)
    │       │   └── List<ChildTerm> ChildrenCore
    │       │       └── ChildTerm (Level 2)
    │       └── ...
    └── ...
```

## 📊 Data Model Design

### KeywordFolder Class
```csharp
[Serializable]
[XmlInclude(typeof(Keyword2Find))]
public class KeywordFolder : IVirtualTreeListData
{
    // Core Properties
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; }
    public bool IsExpanded { get; set; } = true;
    
    // Hierarchy
    [XmlIgnore]
    public KeywordFolder ParentFolder { get; set; }
    public List<KeywordFolder> Children { get; set; } = new List<KeywordFolder>();
    public List<Keyword2Find> Keywords { get; set; } = new List<Keyword2Find>();
    
    // TreeList Integration
    public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info);
    public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info);
    public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info);
    
    // Utility Methods
    public string GetFullPath();
    public bool CanAcceptDrop(object draggedItem);
    public void AcceptDrop(object draggedItem, int position);
}
```

### Modified QueryList Class
```csharp
public class QueryList : TreeList.IVirtualTreeListData, IXmlSerializable
{
    // New folder-based structure
    public readonly List<KeywordFolder> Folders = new List<KeywordFolder>();
    
    // Backward compatibility property
    [XmlIgnore]
    public List<Keyword2Find> ChildrenCore 
    { 
        get { return GetAllKeywords(); }
    }
    
    // TreeList Integration
    public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
    {
        info.Children = Folders; // Return folders instead of keywords
    }
    
    // Helper Methods
    private List<Keyword2Find> GetAllKeywords();
    public void MigrateFromFlatStructure(List<Keyword2Find> existingKeywords);
}
```

### Enhanced Keyword2Find Class
```csharp
public class Keyword2Find : TreeList.IVirtualTreeListData
{
    // Existing properties remain unchanged
    
    // New folder relationship
    [XmlIgnore]
    public KeywordFolder ParentFolder { get; set; }
    
    // Modified parent reference for backward compatibility
    [XmlIgnore]
    public QueryList ParentCore 
    { 
        get { return ParentFolder?.GetRootQueryList(); }
        set { /* Handle legacy assignment */ }
    }
}
```

## 🔄 Event Handling Architecture

### Current Event Handling (Level-Based)
```csharp
// Problematic: Hardcoded level checks
if (node.Level == 0) // Keyword
    // Handle keyword logic
else if (node.Level == 1) // ChildTerm
    // Handle child term logic
```

### New Event Handling (Type-Based)
```csharp
// Flexible: Type-based logic
var dataRecord = treeList1.GetDataRecordByNode(node);
switch (dataRecord)
{
    case KeywordFolder folder:
        // Handle folder logic
        break;
    case Keyword2Find keyword:
        // Handle keyword logic
        break;
    case ChildTerm childTerm:
        // Handle child term logic
        break;
}
```

### Drag & Drop Architecture
```csharp
// Modern DevExpress approach
DragDropManager.Default.DragOver += OnDragOver;
DragDropManager.Default.DragDrop += OnDragDrop;

private void OnDragDrop(object sender, DragDropEventArgs e)
{
    var draggedItems = e.GetData<IList<TreeListNode>>();
    var targetNode = GetTargetNode(e.Location);
    var targetData = treeList1.GetDataRecordByNode(targetNode);
    
    foreach (var draggedNode in draggedItems)
    {
        var draggedData = treeList1.GetDataRecordByNode(draggedNode);
        ProcessDrop(draggedData, targetData, e.InsertType);
    }
}
```

## 💾 Serialization Strategy

### XML Serialization Structure
```xml
<QueryList>
  <Folders>
    <KeywordFolder>
      <Id>folder-1</Id>
      <Name>Electronics</Name>
      <IsExpanded>true</IsExpanded>
      <Children>
        <KeywordFolder>
          <Id>folder-2</Id>
          <Name>Mobile Phones</Name>
          <Keywords>
            <Keyword2Find>
              <Id>kw-1</Id>
              <Alias>iPhone Search</Alias>
              <!-- ... other properties ... -->
            </Keyword2Find>
          </Keywords>
        </KeywordFolder>
      </Children>
    </KeywordFolder>
  </Folders>
</QueryList>
```

### Migration Strategy
```csharp
public void LoadConfigFile(string configPath)
{
    var settings = Serializator.DeserializeSettings(configPath);
    
    // Check if this is an old format (flat keyword list)
    if (settings.ListboxKeywords != null && _ebaySearches.Folders.Count == 0)
    {
        var oldKeywords = Serializator.Stream2Object<List<Keyword2Find>>(settings.ListboxKeywords);
        if (oldKeywords?.Any() == true)
        {
            _ebaySearches.MigrateFromFlatStructure(oldKeywords);
        }
    }
    
    // Continue with normal loading...
}
```

## 🎨 UI Architecture

### TreeList Level Mapping
```
Level 0: KeywordFolder (Folders)
Level 1: Keyword2Find (Keywords within folders)
Level 2: ChildTerm (Sub-searches within keywords)
```

### Context Menu Strategy
```csharp
private void treeList1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
{
    var dataRecord = treeList1.GetDataRecordByNode(e.Node);
    
    switch (dataRecord)
    {
        case KeywordFolder folder:
            AddFolderMenuItems(e.Menu, folder);
            break;
        case Keyword2Find keyword:
            AddKeywordMenuItems(e.Menu, keyword);
            break;
        case ChildTerm childTerm:
            AddChildTermMenuItems(e.Menu, childTerm);
            break;
    }
}
```

### Cell Styling Strategy
```csharp
private void treeList1_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
{
    var dataRecord = treeList1.GetDataRecordByNode(e.Node);
    
    switch (dataRecord)
    {
        case KeywordFolder folder:
            ApplyFolderStyling(e);
            break;
        case Keyword2Find keyword:
            ApplyKeywordStyling(e);
            break;
        case ChildTerm childTerm:
            ApplyChildTermStyling(e);
            break;
    }
}
```

## 📤 CSV Export Architecture

### Export Strategy
```csharp
public static void ExportSearchesToFile(List<Keyword2Find> searchTermList)
{
    var header = new List<string>
    {
        "Id",
        "Folder Path",      // New: Human-readable folder hierarchy
        "Sub Search Id",
        "eBay Search Alias",
        // ... existing headers
    };
    
    foreach (var keyword in searchTermList)
    {
        var folderPath = BuildFolderPath(keyword.ParentFolder);
        // Export keyword with folder path
        // Export child terms with same folder path
    }
}

private static string BuildFolderPath(KeywordFolder folder)
{
    if (folder == null) return "";
    
    var pathParts = new List<string>();
    var current = folder;
    
    while (current != null)
    {
        pathParts.Insert(0, current.Name);
        current = current.ParentFolder;
    }
    
    return string.Join(" > ", pathParts);
}
```

### Import Strategy
```csharp
private static KeywordFolder FindOrCreateFolderFromPath(string folderPath)
{
    if (string.IsNullOrEmpty(folderPath)) return null;
    
    var pathParts = folderPath.Split(new[] { " > " }, StringSplitOptions.RemoveEmptyEntries);
    KeywordFolder currentFolder = null;
    
    foreach (var folderName in pathParts)
    {
        currentFolder = FindOrCreateChildFolder(currentFolder, folderName.Trim());
    }
    
    return currentFolder;
}
```

## 🔒 Design Principles

### 1. Backward Compatibility First
- All existing functionality must continue working
- Gradual migration approach
- Fallback mechanisms for edge cases

### 2. Type-Safe Operations
- Replace level-based logic with type-based logic
- Leverage C# type system for safety
- Polymorphic behavior where appropriate

### 3. DevExpress Best Practices
- Use modern DragDropManager instead of legacy events
- Follow DevExpress TreeList patterns
- Leverage built-in TreeList capabilities

### 4. Performance Considerations
- Lazy loading for large folder structures
- Efficient tree traversal algorithms
- Minimal memory overhead

### 5. User Experience Focus
- Intuitive drag & drop behavior
- Clear visual feedback
- Consistent with existing UI patterns

## 🧪 Testing Architecture

### Unit Testing Strategy
- Test folder hierarchy operations
- Test CSV export/import with folders
- Test migration from flat structure
- Test edge cases and error conditions

### Integration Testing Strategy
- Test TreeList integration
- Test serialization/deserialization
- Test drag & drop operations
- Test performance with large datasets

### User Acceptance Testing
- Test common user workflows
- Test backward compatibility scenarios
- Test error recovery scenarios
- Test usability with real data
