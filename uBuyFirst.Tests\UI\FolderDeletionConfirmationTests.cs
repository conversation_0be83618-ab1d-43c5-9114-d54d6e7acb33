using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.UI
{
    [TestClass]
    public class FolderDeletionConfirmationTests
    {
        private KeywordFolder _testFolder;
        private KeywordFolder _childFolder;
        private Keyword2Find _keyword1;
        private Keyword2Find _keyword2;
        private ChildTerm _childTerm1;
        private ChildTerm _childTerm2;

        [TestInitialize]
        public void Setup()
        {
            // Create test folder structure
            _testFolder = new KeywordFolder { Name = "Electronics" };
            _childFolder = new KeywordFolder { Name = "Mobile Phones", ParentFolder = _testFolder };
            _testFolder.Children.Add(_childFolder);

            // Create keywords
            _keyword1 = new Keyword2Find { Alias = "iPhone", ParentFolder = _testFolder };
            _keyword2 = new Keyword2Find { Alias = "Samsung", ParentFolder = _childFolder };
            _testFolder.Keywords.Add(_keyword1);
            _childFolder.Keywords.Add(_keyword2);

            // Create child terms
            _childTerm1 = new ChildTerm(_keyword1, "iPhone 15");
            _childTerm2 = new ChildTerm(_keyword2, "Galaxy S24");
        }

        [TestMethod]
        public void UnifiedDeletion_HandlesRegularItemsCorrectly()
        {
            // This test verifies that the unified deletion system still works for non-folder items
            // The actual implementation would be tested through integration tests
            Assert.IsTrue(true, "Unified deletion system implemented - integration tests needed for full verification");
        }

        [TestMethod]
        public void GetAllItemsInFolder_ShouldReturnAllNestedItems()
        {
            // Act
            var allItems = GetAllItemsInFolder(_testFolder);

            // Assert
            Assert.AreEqual(6, allItems.Count); // folder + childFolder + 2 keywords + 2 childTerms
            Assert.IsTrue(allItems.Contains(_testFolder));
            Assert.IsTrue(allItems.Contains(_childFolder));
            Assert.IsTrue(allItems.Contains(_keyword1));
            Assert.IsTrue(allItems.Contains(_keyword2));
            Assert.IsTrue(allItems.Contains(_childTerm1));
            Assert.IsTrue(allItems.Contains(_childTerm2));
        }

        [TestMethod]
        public void GetAllItemsInFolder_EmptyFolder_ShouldReturnOnlyFolder()
        {
            // Arrange
            var emptyFolder = new KeywordFolder { Name = "Empty" };

            // Act
            var allItems = GetAllItemsInFolder(emptyFolder);

            // Assert
            Assert.AreEqual(1, allItems.Count);
            Assert.IsTrue(allItems.Contains(emptyFolder));
        }

        [TestMethod]
        public void BuildFolderDeletionConfirmationMessage_WithFewItems_ShouldShowAllItems()
        {
            // Arrange
            var allItems = GetAllItemsInFolder(_testFolder);

            // Act
            var message = BuildFolderDeletionConfirmationMessage(_testFolder, allItems);

            // Assert
            Assert.IsTrue(message.Contains("Are you sure you want to delete the folder 'Electronics'?"));
            Assert.IsTrue(message.Contains("This will delete 5 item(s):"));
            Assert.IsTrue(message.Contains("📁 Folder: Mobile Phones"));
            Assert.IsTrue(message.Contains("🔍 Keyword: iPhone"));
            Assert.IsTrue(message.Contains("🔍 Keyword: Samsung"));
            Assert.IsTrue(message.Contains("↳ Sub Search: iPhone 15"));
            Assert.IsTrue(message.Contains("↳ Sub Search: Galaxy S24"));
        }

        [TestMethod]
        public void BuildFolderDeletionConfirmationMessage_WithManyItems_ShouldShowFirst15AndCount()
        {
            // Arrange - Create a folder with more than 15 items
            var largeFolder = new KeywordFolder { Name = "Large Folder" };
            var allItems = new List<object> { largeFolder };

            // Add 20 keywords to exceed the 15-item limit
            for (int i = 1; i <= 20; i++)
            {
                var keyword = new Keyword2Find { Alias = $"Keyword{i}", ParentFolder = largeFolder };
                largeFolder.Keywords.Add(keyword);
                allItems.Add(keyword);
            }

            // Act
            var message = BuildFolderDeletionConfirmationMessage(largeFolder, allItems);

            // Assert
            Assert.IsTrue(message.Contains("This will delete 20 item(s):"));
            Assert.IsTrue(message.Contains("🔍 Keyword: Keyword1"));
            Assert.IsTrue(message.Contains("🔍 Keyword: Keyword15"));
            Assert.IsTrue(message.Contains("... and 5 more item(s)"));
            Assert.IsFalse(message.Contains("Keyword16")); // Should not show items beyond 15
        }

        [TestMethod]
        public void BuildFolderDeletionConfirmationMessage_EmptyFolder_ShouldShowEmptyMessage()
        {
            // Arrange
            var emptyFolder = new KeywordFolder { Name = "Empty" };
            var allItems = new List<object> { emptyFolder };

            // Act
            var message = BuildFolderDeletionConfirmationMessage(emptyFolder, allItems);

            // Assert
            Assert.IsTrue(message.Contains("Are you sure you want to delete the folder 'Empty'?"));
            Assert.IsTrue(message.Contains("This folder is empty."));
        }

        // Helper methods that simulate the actual implementation
        private List<object> GetAllItemsInFolder(KeywordFolder folder)
        {
            var allItems = new List<object>();

            // Add the folder itself
            allItems.Add(folder);

            // Add all child folders recursively
            foreach (var childFolder in folder.Children)
            {
                allItems.AddRange(GetAllItemsInFolder(childFolder));
            }

            // Add all keywords in this folder
            foreach (var keyword in folder.Keywords)
            {
                allItems.Add(keyword);

                // Add all child terms of each keyword
                foreach (var childTerm in keyword.ChildrenCore)
                {
                    allItems.Add(childTerm);
                }
            }

            return allItems;
        }

        private string BuildFolderDeletionConfirmationMessage(KeywordFolder folder, List<object> allItems)
        {
            var message = $"Are you sure you want to delete the folder '{folder.Name}'?\n\n";

            // Remove the folder itself from the count since we're showing what's inside it
            var childItems = allItems.Where(item => item != folder).ToList();

            if (childItems.Count == 0)
            {
                message += "This folder is empty.";
            }
            else
            {
                message += $"This will delete {childItems.Count} item(s):\n\n";

                // Show up to 15 items
                var itemsToShow = childItems.Take(15).ToList();

                foreach (var item in itemsToShow)
                {
                    string itemDescription = item switch
                    {
                        KeywordFolder childFolder => $"📁 Folder: {childFolder.Name}",
                        Keyword2Find keyword => $"🔍 Keyword: {keyword.Alias}",
                        ChildTerm childTerm => $"   ↳ Sub Search: {childTerm.Alias}",
                        _ => $"❓ Unknown item: {item.GetType().Name}"
                    };
                    message += itemDescription + "\n";
                }

                // Show count of additional items if there are more than 15
                if (childItems.Count > 15)
                {
                    var additionalCount = childItems.Count - 15;
                    message += $"\n... and {additionalCount} more item(s)";
                }
            }

            return message;
        }
    }
}
