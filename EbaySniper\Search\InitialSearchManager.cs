using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace uBuyFirst.Search
{
    /// <summary>
    /// Manages initial search completion detection with reliable, configurable logic
    /// Replaces the problematic completion detection in SearchService.WaitForInitialSearch2()
    /// </summary>
    public class InitialSearchManager
    {
        private readonly IInitialSearchProgressTracker _progressTracker;
        private readonly InitialSearchCompletionCriteria _criteria;
        private readonly RequestQueueManager _queueManager;

        /// <summary>
        /// Gets the current progress tracker
        /// </summary>
        public IInitialSearchProgressTracker ProgressTracker => _progressTracker;

        /// <summary>
        /// Gets the completion criteria being used
        /// </summary>
        public InitialSearchCompletionCriteria Criteria => _criteria;

        /// <summary>
        /// Event fired when initial search completes
        /// </summary>
        public event EventHandler<InitialSearchCompletedEventArgs> InitialSearchCompleted;

        public InitialSearchManager(
            IInitialSearchProgressTracker progressTracker = null,
            InitialSearchCompletionCriteria criteria = null,
            RequestQueueManager queueManager = null)
        {
            _progressTracker = progressTracker ?? new InitialSearchProgressTracker();
            _criteria = criteria ?? InitialSearchCompletionCriteria.CreateDefault();
            _queueManager = queueManager ?? RequestQueueManager.Instance;
        }

        /// <summary>
        /// Initializes the manager for a new initial search
        /// </summary>
        /// <param name="isDualPhaseEnabled">Whether dual-phase search is enabled</param>
        public void Initialize(bool isDualPhaseEnabled)
        {
            // Update criteria based on current settings
            _criteria.UpdateTargetFromSettings();

            // Initialize progress tracking
            _progressTracker.Initialize(isDualPhaseEnabled);

            Debug.WriteLine($"InitialSearchManager initialized: {_criteria.GetCriteriaDescription()}");
        }

        /// <summary>
        /// Checks if the initial search should be considered complete
        /// </summary>
        /// <returns>True if initial search is complete</returns>
        public bool IsInitialSearchComplete()
        {
            var progress = _progressTracker.Progress;

            // Check timeout - always complete if we've run too long
            if (progress.ElapsedTime >= _criteria.MaximumRunTime)
            {
                Debug.WriteLine($"Initial search complete: Timeout reached ({progress.ElapsedTime:mm\\:ss})");
                return true;
            }

            // Don't complete too early - must run for minimum time
            if (progress.ElapsedTime < _criteria.MinimumRunTime)
            {
                return false;
            }

            // Check if all required phases are complete
            if (_criteria.RequireAllPhasesComplete && !_progressTracker.AreAllPhasesComplete)
            {
                return false;
            }

            // Smart completion logic - complete if we haven't found items in a while
            if (_criteria.UseSmartCompletion)
            {
                // If no items found for the specified timeout period
                if (progress.TimeSinceLastItem >= _criteria.NoNewItemsTimeout)
                {
                    Debug.WriteLine($"Initial search complete: No new items for {progress.TimeSinceLastItem:mm\\:ss}");
                    return true;
                }

                // If all requests are complete and we've waited the specified time
                if (progress.TimeSinceAllRequestsComplete.HasValue &&
                    progress.TimeSinceAllRequestsComplete.Value >= _criteria.AllRequestsCompleteTimeout)
                {
                    Debug.WriteLine($"Initial search complete: All requests done for {progress.TimeSinceAllRequestsComplete.Value:mm\\:ss}");
                    return true;
                }
            }

            // Original logic - check if we've processed enough items
            var hasProcessedEnoughItems = progress.ItemsProcessed >= _criteria.TargetItemsProcessed;

            // Check if request queue is stable (optional)
            var isQueueStable = !_criteria.RequireStableQueue ||
                               _queueManager.GeneralItemQueueCount <= _criteria.MaxQueueSize;

            var isComplete = hasProcessedEnoughItems && isQueueStable;

            if (isComplete)
            {
                Debug.WriteLine($"Initial search complete: Items={progress.ItemsProcessed}/{_criteria.TargetItemsProcessed}, " +
                               $"Queue={_queueManager.GeneralItemQueueCount}, Time={progress.ElapsedTime:mm\\:ss}");
            }

            return isComplete;
        }

        /// <summary>
        /// Waits for the initial search to complete with improved logic
        /// Replaces SearchService.WaitForInitialSearch2()
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when initial search is done</returns>
        public async Task WaitForInitialSearchCompletionAsync(CancellationToken cancellationToken)
        {
            const int checkIntervalMs = 1000;
            var lastProgressReport = DateTime.UtcNow;
            const int progressReportIntervalSeconds = 10;

            while (!cancellationToken.IsCancellationRequested)
            {
                // Check if we're complete
                if (IsInitialSearchComplete())
                {
                    // Give a brief settling period
                    await Task.Delay(2000, cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);

                    // Fire completion event
                    OnInitialSearchCompleted();
                    return;
                }

                // Periodic progress reporting for debugging
                if (DateTime.UtcNow - lastProgressReport >= TimeSpan.FromSeconds(progressReportIntervalSeconds))
                {
                    Debug.WriteLine($"Initial search progress: {_progressTracker.GetProgressSummary()}");
                    lastProgressReport = DateTime.UtcNow;
                }

                // Wait before next check
                await Task.Delay(checkIntervalMs, cancellationToken).ContinueWith(_ => { }).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Gets detailed completion status for debugging
        /// </summary>
        /// <returns>Detailed status information</returns>
        public string GetCompletionStatus()
        {
            var progress = _progressTracker.Progress;
            var queueCount = _queueManager.GeneralItemQueueCount;

            return $"Completion Status:\n" +
                   $"  Progress: {_progressTracker.GetProgressSummary()}\n" +
                   $"  Criteria: {_criteria.GetCriteriaDescription()}\n" +
                   $"  Queue: {queueCount} (max: {_criteria.MaxQueueSize})\n" +
                   $"  Complete: {IsInitialSearchComplete()}";
        }

        /// <summary>
        /// Fires the InitialSearchCompleted event
        /// </summary>
        private void OnInitialSearchCompleted()
        {
            var args = new InitialSearchCompletedEventArgs
            {
                Progress = _progressTracker.Progress,
                CompletionReason = DetermineCompletionReason()
            };

            InitialSearchCompleted?.Invoke(this, args);
        }

        /// <summary>
        /// Determines why the initial search completed
        /// </summary>
        private string DetermineCompletionReason()
        {
            var progress = _progressTracker.Progress;

            if (progress.ElapsedTime >= _criteria.MaximumRunTime)
                return "Timeout";

            if (progress.ItemsProcessed >= _criteria.TargetItemsProcessed)
                return "Target reached";

            return "Other";
        }
    }

    /// <summary>
    /// Event arguments for initial search completion
    /// </summary>
    public class InitialSearchCompletedEventArgs : EventArgs
    {
        public InitialSearchProgress Progress { get; set; }
        public string CompletionReason { get; set; }
    }
}
