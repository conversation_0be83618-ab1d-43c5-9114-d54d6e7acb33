using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Purchasing
{
    [TestClass]
    public class CreditCardCheckoutTests
    {
        [TestMethod]
        public void CaptchaDetection_WithSessionIdError_ShouldDetectCaptcha()
        {
            // Arrange
            var captchaErrorMessage = "Session ID not found in session page HTML.";

            // Act
            var isCaptchaError = CaptchaCooldownManager.IsCaptchaError(captchaErrorMessage);

            // Assert
            Assert.IsTrue(isCaptchaError, "Should detect captcha error message");
        }

        [TestMethod]
        public void CaptchaDetection_WithNormalError_ShouldNotDetectCaptcha()
        {
            // Arrange
            var normalErrorMessage = "Payment failed due to insufficient funds.";

            // Act
            var isCaptchaError = CaptchaCooldownManager.IsCaptchaError(normalErrorMessage);

            // Assert
            Assert.IsFalse(isCaptchaError, "Should not detect captcha for normal error messages");
        }

        [TestMethod]
        public void CaptchaDetection_WithNullError_ShouldNotDetectCaptcha()
        {
            // Arrange
            string nullErrorMessage = null;

            // Act
            var isCaptchaError = CaptchaCooldownManager.IsCaptchaError(nullErrorMessage);

            // Assert
            Assert.IsFalse(isCaptchaError, "Should not detect captcha for null error message");
        }

        [TestMethod]
        public void CaptchaDetection_WithEmptyError_ShouldNotDetectCaptcha()
        {
            // Arrange
            var emptyErrorMessage = "";

            // Act
            var isCaptchaError = CaptchaCooldownManager.IsCaptchaError(emptyErrorMessage);

            // Assert
            Assert.IsFalse(isCaptchaError, "Should not detect captcha for empty error message");
        }
    }
}
