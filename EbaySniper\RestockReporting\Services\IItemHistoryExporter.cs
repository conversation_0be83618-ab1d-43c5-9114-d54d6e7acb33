using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.RestockReporting.Models;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// Service interface for exporting item processing history
    /// </summary>
    public interface IItemHistoryExporter
    {
        /// <summary>
        /// Exports item processing history to CSV format
        /// </summary>
        /// <param name="startDate">Start date for export (inclusive)</param>
        /// <param name="endDate">End date for export (inclusive)</param>
        /// <param name="outputPath">Full path where CSV file should be saved</param>
        /// <returns>Path to the created CSV file</returns>
        Task<string> ExportToCsvAsync(DateTime startDate, DateTime endDate, string outputPath);

        /// <summary>
        /// Loads item processing history from JSON files for a date range
        /// </summary>
        /// <param name="startDate">Start date for loading (inclusive)</param>
        /// <param name="endDate">End date for loading (inclusive)</param>
        /// <returns>Collection of item processing contexts ordered by timestamp</returns>
        Task<IEnumerable<ItemProcessingContext>> LoadHistoryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets the count of available history records for a date range
        /// </summary>
        /// <param name="startDate">Start date for counting (inclusive)</param>
        /// <param name="endDate">End date for counting (inclusive)</param>
        /// <returns>Number of available records</returns>
        Task<int> GetHistoryCountAsync(DateTime startDate, DateTime endDate);
    }
}
