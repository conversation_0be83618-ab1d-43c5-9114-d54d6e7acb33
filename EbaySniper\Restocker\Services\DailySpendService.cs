﻿using System;
using System.Linq;
using uBuyFirst.Prefs;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service for tracking and managing daily spending limits for restock purchases
    /// </summary>
    public class DailySpendService : IDailySpendService
    {
        private readonly object _lock = new();
        private const int DataRetentionDays = 30;

        /// <summary>
        /// Gets the total amount spent today in USD
        /// </summary>
        public decimal GetTodaySpent()
        {
            lock (_lock)
            {
                var today = DateTime.Today;
                if (UserSettings.DailySpendHistory.TryGetValue(today, out var spent))
                    return spent;
                return 0m;
            }
        }

        /// <summary>
        /// Gets the current daily spending limit in USD
        /// </summary>
        public decimal GetDailyLimit()
        {
            return UserSettings.DailySpendLimit;
        }

        /// <summary>
        /// Sets the daily spending limit in USD
        /// </summary>
        public void SetDailyLimit(decimal limit)
        {
            if (limit < 0)
            {
                 return;
            }

            UserSettings.DailySpendLimit = limit;
        }

        /// <summary>
        /// Checks if a purchase amount can be made without exceeding the daily limit
        /// </summary>
        public bool CanPurchase(decimal amountUsd)
        {
            if (amountUsd < 0)
            {
                return false;
            }

            lock (_lock)
            {
                var todaySpent = GetTodaySpent();
                var dailyLimit = GetDailyLimit();
                var wouldExceedLimit = (todaySpent + amountUsd) > dailyLimit;

                System.Diagnostics.Debug.WriteLine("Purchase check - Amount: {0:C}, Today spent: {1:C}, Limit: {2:C}, Can purchase: {3}",
                    amountUsd, todaySpent, dailyLimit, !wouldExceedLimit);

                return !wouldExceedLimit;
            }
        }

        /// <summary>
        /// Gets the remaining budget for today in USD
        /// </summary>
        public decimal GetRemainingBudget()
        {
            lock (_lock)
            {
                var todaySpent = GetTodaySpent();
                var dailyLimit = GetDailyLimit();
                var remaining = Math.Max(0, dailyLimit - todaySpent);

                return remaining;
            }
        }

        /// <summary>
        /// Records a purchase amount for today
        /// </summary>
        public void RecordPurchase(decimal amountUsd)
        {
            if (amountUsd <= 0)
            {
                return;
            }

            lock (_lock)
            {
                try
                {
                    var today = DateTime.Today;

                    // Add to today's total
                    var currentSpent = UserSettings.DailySpendHistory.TryGetValue(today, out var value) ? value : 0m;
                    UserSettings.DailySpendHistory[today] = currentSpent + amountUsd;

                    // Cleanup old records periodically (every 10th purchase)
                    if (UserSettings.DailySpendHistory.Count > DataRetentionDays &&
                        UserSettings.DailySpendHistory.Count % 10 == 0)
                    {
                        CleanupOldRecords();
                    }
                }
                catch (Exception ex)
                {
                    Form1.Log.Error(ex, "Error recording purchase amount: {0}", amountUsd);
                }
            }
        }

        /// <summary>
        /// Cleans up old spend records to prevent unlimited data growth
        /// </summary>
        public void CleanupOldRecords()
        {
            lock (_lock)
            {
                try
                {
                    var cutoffDate = DateTime.Today.AddDays(-DataRetentionDays);
                    var keysToRemove = UserSettings.DailySpendHistory.Keys
                        .Where(date => date < cutoffDate)
                        .ToList();

                    foreach (var key in keysToRemove)
                    {
                        UserSettings.DailySpendHistory.Remove(key);
                    }
                }
                catch (Exception ex)
                {
                    Form1.Log.Error(ex, "Error cleaning up old spend records");
                }
            }
        }

        /// <summary>
        /// Gets the spend amount for a specific date
        /// </summary>
        public decimal GetSpentOnDate(DateTime date)
        {
            lock (_lock)
            {
                var dateKey = date.Date; // Ensure we use date only, not time
                if (UserSettings.DailySpendHistory.TryGetValue(dateKey, out var onDate) is true)
                    return onDate;
                return 0m;
            }
        }
    }
}
