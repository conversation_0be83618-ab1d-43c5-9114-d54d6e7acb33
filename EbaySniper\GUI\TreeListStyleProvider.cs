﻿using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Skins;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;
using uBuyFirst.UI.Validation;

namespace uBuyFirst.GUI
{
    /// <summary>
    /// Provides styling functionality for TreeList controls, specifically for the keywords TreeList.
    /// Handles row selection emphasis, cell focus highlighting, and node type-specific styling.
    /// </summary>
    public static class TreeListStyleProvider
    {
        /// <summary>
        /// Main entry point for applying all TreeList cell styling.
        /// This method coordinates all styling aspects including validation warnings,
        /// row selection emphasis, cell focus highlighting, and enabled/disabled states.
        /// </summary>
        /// <param name="e">The styling event arguments</param>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="validationCoordinator">The validation coordinator for warning styling</param>
        public static void ApplyNodeCellStyle(GetCustomNodeCellStyleEventArgs e, TreeList treeList, TreeListValidationCoordinator validationCoordinator)
        {
            var dataRecord = treeList.GetDataRecordByNode(e.Node);

            // Apply special styling for folders
            if (dataRecord is KeywordFolder)
            {
                ApplyFolderBasicStyling(e, treeList);
            }
            // Apply styling for child terms (non-keyword, non-folder items)
            else if (dataRecord is ChildTerm && !IsEditableChildTermColumn(e.Column.FieldName))
            {
                ApplyChildTermBasicStyling(e, treeList);
            }

            // Apply special background color for Filter column on keywords (not folders or child terms)
            if (dataRecord is Keyword2Find && e.Column.FieldName == "Filter")
            {
                e.Appearance.BackColor = Color.GhostWhite;
            }

            // Apply warning styling for cells with validation issues
            if (validationCoordinator?.HasWarnings(treeList, e.Node, e.Column.FieldName) == true)
            {
                ValidationStyleProvider.ApplyWarningStyle(e.Appearance);
            }

            // Handle selection emphasis - this should be applied before enabled/disabled state
            if (e.Node.Selected)
            {
                ApplySelectionEmphasis(e, dataRecord, treeList);
            }

            // Handle cell focus emphasis - this should be applied AFTER row selection to take priority
            if (e.Node == treeList.FocusedNode && e.Column == treeList.FocusedColumn)
            {
                ApplyCellFocusEmphasis(e, treeList);
            }

            // Handle enabled/disabled visual state - this should be the final styling decision
            if (!e.Node.Checked)
            {
                ApplyDisabledStateStyling(e, dataRecord, treeList);
            }
            // If checked (enabled), the selection emphasis from ApplySelectionEmphasis will be preserved
        }

        /// <summary>
        /// Applies basic styling for folder nodes (font, color, background)
        /// </summary>
        private static void ApplyFolderBasicStyling(GetCustomNodeCellStyleEventArgs e, TreeList treeList)
        {
            // Make folder names larger font size in addition to bold
            var currentFont = e.Appearance.Font ?? treeList.Font;
            var folderFont = new Font(currentFont.FontFamily, currentFont.Size + 1, FontStyle.Bold);
            e.Appearance.Font = folderFont;
            e.Appearance.ForeColor = Color.DarkBlue;

            // Apply different background colors for bulk-editable vs non-editable columns
            if (IsBulkEditableColumn(e.Column.FieldName))
            {
                // Slightly different background for bulk-editable columns
                e.Appearance.BackColor = Color.FromArgb(255, 255, 200); // Light yellow tint for editable

                // Add subtle border to indicate editability
                e.Appearance.BorderColor = Color.Black;
                e.Appearance.Options.UseBorderColor = true;
            }
            else
            {
                // Standard folder background for non-editable columns
                e.Appearance.BackColor = Color.FromArgb(255, 255, 200);// Very light yellow tint

                // Remove borders for non-editable columns
                e.Appearance.BorderColor = Color.Transparent;
                e.Appearance.Options.UseBorderColor = true;
            }
        }

        /// <summary>
        /// Applies basic styling for child term nodes
        /// </summary>
        private static void ApplyChildTermBasicStyling(GetCustomNodeCellStyleEventArgs e, TreeList treeList)
        {
            var skin = CommonSkins.GetSkin(treeList.LookAndFeel);
            var skinElement = skin[CommonSkins.SkinScrollButton];
            if (skinElement != null)
            {
                e.Appearance.BackColor = skinElement.Color.BackColor;
            }
        }

        /// <summary>
        /// Checks if a column is editable for child terms
        /// </summary>
        private static bool IsEditableChildTermColumn(string fieldName)
        {
            return fieldName == "Alias" ||
                   fieldName == "Keywords" ||
                   fieldName == "Price Min" ||
                   fieldName == "Price Max" ||
                   fieldName == "Search in description" ||
                   fieldName == "Category ID" ||
                   fieldName == "Condition" ||
                   fieldName == "Filter";
        }

        /// <summary>
        /// Determines if a column supports bulk editing for folders
        /// </summary>
        private static bool IsBulkEditableColumn(string columnFieldName)
        {
            return columnFieldName switch
            {
                "Price Min" => true,
                "Price Max" => true,
                "Threads" => true,
                "Search in description" => true,
                "Located in" => true,
                "Ships to" => true,
                "Seller type" => true,
                "Site" => true,
                "View" => true,
                "Condition" => true,
                "Category ID" => true,
                "ListingType" => true,
                "Sellers" => true,
                "Ship Zipcode" => true,
                "Interval" => true,
                _ => false
            };
        }

        /// <summary>
        /// Applies enhanced visual emphasis for selected rows in the TreeList
        /// </summary>
        /// <param name="e">The styling event arguments</param>
        /// <param name="dataRecord">The data record associated with the node</param>
        /// <param name="treeList">The TreeList control</param>
        private static void ApplySelectionEmphasis(GetCustomNodeCellStyleEventArgs e, object dataRecord, TreeList treeList)
        {
            // Get the current skin for consistent theming
            var skin = CommonSkins.GetSkin(treeList.LookAndFeel);

            // Apply different emphasis based on node type
            if (dataRecord is KeywordFolder)
            {
                // Folders get a more prominent selection emphasis
                ApplyFolderSelectionEmphasis(e, skin, treeList);
            }
            else if (dataRecord is Keyword2Find)
            {
                // Keywords get standard selection emphasis
                ApplyKeywordSelectionEmphasis(e, skin, treeList);
            }
            else if (dataRecord is ChildTerm)
            {
                // Child terms get subtle selection emphasis
                ApplyChildTermSelectionEmphasis(e, skin, treeList);
            }
        }

        /// <summary>
        /// Applies selection emphasis specifically for folder nodes
        /// </summary>
        private static void ApplyFolderSelectionEmphasis(GetCustomNodeCellStyleEventArgs e, Skin skin, TreeList treeList)
        {
            // Check if TreeList has focus for different emphasis levels
            bool treeListHasFocus = treeList.Focused || treeList.ContainsFocus;

            if (treeListHasFocus)
            {
                // Strong emphasis when TreeList is focused
                e.Appearance.BackColor = Color.FromArgb(51, 153, 255); // Bright blue
                e.Appearance.ForeColor = Color.White; // White text for contrast

                // Add a prominent border with gradient effect
                e.Appearance.BorderColor = Color.FromArgb(30, 120, 200); // Darker blue border
                e.Appearance.Options.UseBorderColor = true;
            }
            else
            {
                // Subtle emphasis when TreeList is not focused
                e.Appearance.BackColor = Color.FromArgb(200, 220, 240); // Light blue-gray
                e.Appearance.ForeColor = Color.Black;

                // Subtle border
                e.Appearance.BorderColor = Color.FromArgb(150, 180, 210);
                e.Appearance.Options.UseBorderColor = true;
            }

            // Make the font bold and slightly larger for emphasis
            var currentFont = e.Appearance.Font ?? treeList.Font;
            var emphasisFont = new Font(currentFont.FontFamily, currentFont.Size + 1, FontStyle.Bold);
            e.Appearance.Font = emphasisFont;
        }

        /// <summary>
        /// Applies selection emphasis specifically for keyword nodes
        /// </summary>
        private static void ApplyKeywordSelectionEmphasis(GetCustomNodeCellStyleEventArgs e, Skin skin, TreeList treeList)
        {
            // Check if TreeList has focus for different emphasis levels
            bool treeListHasFocus = treeList.Focused || treeList.ContainsFocus;

            if (treeListHasFocus)
            {
                // Moderate emphasis when TreeList is focused
                e.Appearance.BackColor = Color.FromArgb(135, 206, 250); // Light sky blue
                e.Appearance.ForeColor = Color.Black; // Keep text readable

                // Add a noticeable border
                e.Appearance.BorderColor = Color.FromArgb(70, 130, 180); // Steel blue border
                e.Appearance.Options.UseBorderColor = true;
            }
            else
            {
                // Subtle emphasis when TreeList is not focused
                e.Appearance.BackColor = Color.FromArgb(230, 240, 250); // Very light blue
                e.Appearance.ForeColor = Color.Black;

                // Subtle border
                e.Appearance.BorderColor = Color.FromArgb(180, 200, 220);
                e.Appearance.Options.UseBorderColor = true;
            }

            // Make the font bold for emphasis
            var currentFont = e.Appearance.Font ?? treeList.Font;
            var emphasisFont = new Font(currentFont, FontStyle.Bold);
            e.Appearance.Font = emphasisFont;
        }

        /// <summary>
        /// Applies selection emphasis specifically for child term nodes
        /// </summary>
        private static void ApplyChildTermSelectionEmphasis(GetCustomNodeCellStyleEventArgs e, Skin skin, TreeList treeList)
        {
            // Check if TreeList has focus for different emphasis levels
            bool treeListHasFocus = treeList.Focused || treeList.ContainsFocus;

            if (treeListHasFocus)
            {
                // Subtle but noticeable emphasis when TreeList is focused
                e.Appearance.BackColor = Color.FromArgb(240, 248, 255); // Alice blue
                e.Appearance.ForeColor = Color.Black;

                // Add a subtle border
                e.Appearance.BorderColor = Color.FromArgb(176, 196, 222); // Light steel blue border
                e.Appearance.Options.UseBorderColor = true;
            }
            else
            {
                // Very subtle emphasis when TreeList is not focused
                e.Appearance.BackColor = Color.FromArgb(248, 250, 252); // Very light gray-blue
                e.Appearance.ForeColor = Color.Black;

                // Very subtle border
                e.Appearance.BorderColor = Color.FromArgb(220, 230, 240);
                e.Appearance.Options.UseBorderColor = true;
            }

            // Make the font slightly bold
            var currentFont = e.Appearance.Font ?? treeList.Font;
            var emphasisFont = new Font(currentFont, FontStyle.Bold);
            e.Appearance.Font = emphasisFont;
        }

        /// <summary>
        /// Applies simple visual emphasis for the currently focused cell
        /// </summary>
        /// <param name="e">The styling event arguments</param>
        /// <param name="treeList"></param>
        private static void ApplyCellFocusEmphasis(GetCustomNodeCellStyleEventArgs e, TreeList treeList)
        {
            // Apply a subtle background color to highlight the focused cell
            e.Appearance.BackColor = Color.FromArgb(189, 237, 255); // Light blue background
            // Ensure text is visible by setting font color to black for proper contrast
            e.Appearance.ForeColor = Color.Black;
            var currentFont = e.Appearance.Font ?? treeList.Font;
            var emphasisFont = new Font(currentFont, FontStyle.Underline);
            e.Appearance.Font = emphasisFont;
        }

        /// <summary>
        /// Applies styling for disabled/unchecked nodes
        /// </summary>
        private static void ApplyDisabledStateStyling(GetCustomNodeCellStyleEventArgs e, object dataRecord, TreeList treeList)
        {
            // Use italic text for disabled/inactive keywords
            if (dataRecord is Keyword2Find || dataRecord is ChildTerm)
            {
                var currentFont = e.Appearance.Font ?? treeList.Font;
                var italicFont = new Font(currentFont, currentFont.Style | FontStyle.Italic);
                e.Appearance.Font = italicFont;
            }

            if (e.Node.Selected)
            {
                // Selected but disabled - use darker grey to show selection
                var skin = CommonSkins.GetSkin(treeList.LookAndFeel);
                var skinElement = skin[CommonSkins.SkinGroupPanel];
                if (skinElement != null)
                {
                    var color = skinElement.Color.BackColor;
                    e.Appearance.BackColor = ControlPaint.Dark(color, -0.3f);
                }
            }
            else
            {
                // Disabled and not selected - use light grey
                var skin = CommonSkins.GetSkin(treeList.LookAndFeel);
                var skinElement = skin[CommonSkins.SkinGroupPanel];
                if (skinElement != null)
                {
                    var color = skinElement.Color.BackColor;
                    e.Appearance.BackColor = ControlPaint.Dark(color, -0.1f);
                }
            }
        }
    }
}
