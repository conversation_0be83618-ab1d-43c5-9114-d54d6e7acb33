using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Other;
using uBuyFirst.Search;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class FolderPersistenceTests
    {
        private QueryList _queryList;
        private string _testSettingsPath;

        [TestInitialize]
        public void Setup()
        {
            _queryList = new QueryList();
            _testSettingsPath = Path.Combine(Path.GetTempPath(), "test_folder_persistence");
            Directory.CreateDirectory(_testSettingsPath);
        }

        [TestCleanup]
        public void Cleanup()
        {
            if (Directory.Exists(_testSettingsPath))
            {
                Directory.Delete(_testSettingsPath, true);
            }
        }

        [TestMethod]
        public void FolderPath_Property_UpdatesWhenParentFolderChanges()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Electronics" };
            var keyword = new Keyword2Find { Alias = "Test Keyword" };

            // Act
            keyword.ParentFolder = folder;

            // Assert
            Assert.AreEqual("Electronics", keyword.FolderPath);
        }

        [TestMethod]
        public void FolderPath_Property_HandlesNestedFolders()
        {
            // Arrange
            var rootFolder = new KeywordFolder { Name = "Electronics" };
            var childFolder = new KeywordFolder { Name = "Mobile Phones", ParentFolder = rootFolder };
            rootFolder.Children.Add(childFolder);

            var keyword = new Keyword2Find { Alias = "Test Keyword" };

            // Act
            keyword.ParentFolder = childFolder;

            // Assert
            Assert.AreEqual("Electronics > Mobile Phones", keyword.FolderPath);
        }

        [TestMethod]
        public void QueryList_Serialization_PreservesFolderStructure()
        {
            // Arrange
            var rootFolder = new KeywordFolder
            {
                Name = "Electronics",
                Id = "electronics-folder"
            };

            var childFolder = new KeywordFolder
            {
                Name = "Mobile Phones",
                Id = "mobile-folder",
                ParentFolder = rootFolder
            };

            rootFolder.Children.Add(childFolder);

            var keyword1 = new Keyword2Find
            {
                Alias = "iPhone",
                Kws = "iPhone 15",
                ParentFolder = rootFolder
            };

            var keyword2 = new Keyword2Find
            {
                Alias = "Samsung",
                Kws = "Samsung Galaxy",
                ParentFolder = childFolder
            };

            rootFolder.Keywords.Add(keyword1);
            childFolder.Keywords.Add(keyword2);
            _queryList.Folders.Add(rootFolder);

            // Act - Serialize
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(QueryList));
            var stream = new MemoryStream();
            serializer.Serialize(stream, _queryList);

            // Act - Deserialize
            stream.Position = 0;
            var deserializedQueryList = (QueryList)serializer.Deserialize(stream);

            // Assert
            Assert.AreEqual(1, deserializedQueryList.Folders.Count);

            var deserializedRootFolder = deserializedQueryList.Folders[0];
            Assert.AreEqual("Electronics", deserializedRootFolder.Name);
            Assert.AreEqual("electronics-folder", deserializedRootFolder.Id);
            Assert.AreEqual(1, deserializedRootFolder.Children.Count);
            Assert.AreEqual(1, deserializedRootFolder.Keywords.Count);

            var deserializedChildFolder = deserializedRootFolder.Children[0];
            Assert.AreEqual("Mobile Phones", deserializedChildFolder.Name);
            Assert.AreEqual("mobile-folder", deserializedChildFolder.Id);
            Assert.AreEqual(1, deserializedChildFolder.Keywords.Count);

            var deserializedKeyword1 = deserializedRootFolder.Keywords[0];
            Assert.AreEqual("iPhone", deserializedKeyword1.Alias);
            Assert.AreEqual("iPhone 15", deserializedKeyword1.Kws);

            var deserializedKeyword2 = deserializedChildFolder.Keywords[0];
            Assert.AreEqual("Samsung", deserializedKeyword2.Alias);
            Assert.AreEqual("Samsung Galaxy", deserializedKeyword2.Kws);
        }

        [TestMethod]
        public void QueryList_FindFolderByPath_FindsCorrectFolder()
        {
            // Arrange
            var rootFolder = new KeywordFolder { Name = "Electronics" };
            var childFolder = new KeywordFolder
            {
                Name = "Mobile Phones",
                ParentFolder = rootFolder
            };
            rootFolder.Children.Add(childFolder);
            _queryList.Folders.Add(rootFolder);

            // Act & Assert
            var foundRoot = _queryList.FindFolderByPath("Electronics");
            Assert.IsNotNull(foundRoot);
            Assert.AreEqual("Electronics", foundRoot.Name);

            var foundChild = _queryList.FindFolderByPath("Electronics > Mobile Phones");
            Assert.IsNotNull(foundChild);
            Assert.AreEqual("Mobile Phones", foundChild.Name);

            var notFound = _queryList.FindFolderByPath("NonExistent");
            Assert.IsNull(notFound);
        }

        [TestMethod]
        public void QueryList_MigrateFromFlatStructure_CreatesDefaultFolder()
        {
            // Arrange
            var keywords = new List<Keyword2Find>
            {
                new Keyword2Find { Alias = "Test1", Kws = "keyword1" },
                new Keyword2Find { Alias = "Test2", Kws = "keyword2" }
            };

            // Act
            _queryList.MigrateFromFlatStructure(keywords);

            // Assert
            Assert.AreEqual(1, _queryList.Folders.Count);
            var defaultFolder = _queryList.Folders[0];
            Assert.AreEqual("Default", defaultFolder.Name);
            Assert.AreEqual(2, defaultFolder.Keywords.Count);

            foreach (var keyword in keywords)
            {
                Assert.AreEqual(defaultFolder, keyword.ParentFolder);
            }
        }

        [TestMethod]
        public void QueryList_ChildrenCore_ReturnsAllKeywords()
        {
            // Arrange
            var folder1 = new KeywordFolder { Name = "Folder1" };
            var folder2 = new KeywordFolder { Name = "Folder2" };

            var keyword1 = new Keyword2Find { Alias = "Keyword1" };
            var keyword2 = new Keyword2Find { Alias = "Keyword2" };
            var keyword3 = new Keyword2Find { Alias = "Keyword3" };

            folder1.Keywords.Add(keyword1);
            folder1.Keywords.Add(keyword2);
            folder2.Keywords.Add(keyword3);

            _queryList.Folders.Add(folder1);
            _queryList.Folders.Add(folder2);

            // Act
            var allKeywords = _queryList.ChildrenCore;

            // Assert
            Assert.AreEqual(3, allKeywords.Count);
            Assert.IsTrue(allKeywords.Contains(keyword1));
            Assert.IsTrue(allKeywords.Contains(keyword2));
            Assert.IsTrue(allKeywords.Contains(keyword3));
        }

        [TestMethod]
        public void KeywordFolder_IsExpanded_DefaultsToTrue()
        {
            // Arrange & Act
            var folder = new KeywordFolder { Name = "Test Folder" };

            // Assert
            Assert.IsTrue(folder.IsExpanded, "New folders should default to expanded state");
        }

        [TestMethod]
        public void KeywordFolder_IsExpanded_CanBeSetToFalse()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Test Folder" };

            // Act
            folder.IsExpanded = false;

            // Assert
            Assert.IsFalse(folder.IsExpanded, "Folder expansion state should be settable");
        }

        [TestMethod]
        public void QueryList_Serialization_PreservesExpansionState()
        {
            // Arrange
            var expandedFolder = new KeywordFolder
            {
                Name = "Expanded Folder",
                Id = "expanded-folder",
                IsExpanded = true
            };

            var collapsedFolder = new KeywordFolder
            {
                Name = "Collapsed Folder",
                Id = "collapsed-folder",
                IsExpanded = false
            };

            var keyword1 = new Keyword2Find
            {
                Alias = "Test1",
                Kws = "test keyword 1",
                ParentFolder = expandedFolder
            };

            var keyword2 = new Keyword2Find
            {
                Alias = "Test2",
                Kws = "test keyword 2",
                ParentFolder = collapsedFolder
            };

            expandedFolder.Keywords.Add(keyword1);
            collapsedFolder.Keywords.Add(keyword2);
            _queryList.Folders.Add(expandedFolder);
            _queryList.Folders.Add(collapsedFolder);

            // Act - Serialize and Deserialize
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(QueryList));
            var stream = new MemoryStream();
            serializer.Serialize(stream, _queryList);

            stream.Position = 0;
            var deserializedQueryList = (QueryList)serializer.Deserialize(stream);

            // Assert
            Assert.AreEqual(2, deserializedQueryList.Folders.Count);

            var deserializedExpandedFolder = deserializedQueryList.Folders.FirstOrDefault(f => f.Name == "Expanded Folder");
            var deserializedCollapsedFolder = deserializedQueryList.Folders.FirstOrDefault(f => f.Name == "Collapsed Folder");

            Assert.IsNotNull(deserializedExpandedFolder, "Expanded folder should be deserialized");
            Assert.IsNotNull(deserializedCollapsedFolder, "Collapsed folder should be deserialized");

            Assert.IsTrue(deserializedExpandedFolder.IsExpanded, "Expanded folder should remain expanded after serialization");
            Assert.IsFalse(deserializedCollapsedFolder.IsExpanded, "Collapsed folder should remain collapsed after serialization");
        }

        [TestMethod]
        public void QueryList_Serialization_PreservesNestedFolderExpansionState()
        {
            // Arrange
            var rootFolder = new KeywordFolder
            {
                Name = "Root Folder",
                Id = "root-folder",
                IsExpanded = true
            };

            var childFolder1 = new KeywordFolder
            {
                Name = "Child Folder 1",
                Id = "child-folder-1",
                IsExpanded = false,
                ParentFolder = rootFolder
            };

            var childFolder2 = new KeywordFolder
            {
                Name = "Child Folder 2",
                Id = "child-folder-2",
                IsExpanded = true,
                ParentFolder = rootFolder
            };

            rootFolder.Children.Add(childFolder1);
            rootFolder.Children.Add(childFolder2);
            _queryList.Folders.Add(rootFolder);

            // Act - Serialize and Deserialize
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(QueryList));
            var stream = new MemoryStream();
            serializer.Serialize(stream, _queryList);

            stream.Position = 0;
            var deserializedQueryList = (QueryList)serializer.Deserialize(stream);

            // Assert
            Assert.AreEqual(1, deserializedQueryList.Folders.Count);

            var deserializedRootFolder = deserializedQueryList.Folders[0];
            Assert.IsTrue(deserializedRootFolder.IsExpanded, "Root folder should remain expanded");
            Assert.AreEqual(2, deserializedRootFolder.Children.Count);

            var deserializedChild1 = deserializedRootFolder.Children.FirstOrDefault(f => f.Name == "Child Folder 1");
            var deserializedChild2 = deserializedRootFolder.Children.FirstOrDefault(f => f.Name == "Child Folder 2");

            Assert.IsNotNull(deserializedChild1, "Child folder 1 should be deserialized");
            Assert.IsNotNull(deserializedChild2, "Child folder 2 should be deserialized");

            Assert.IsFalse(deserializedChild1.IsExpanded, "Child folder 1 should remain collapsed");
            Assert.IsTrue(deserializedChild2.IsExpanded, "Child folder 2 should remain expanded");
        }

        [TestMethod]
        public void QueryList_SerializationFormat_ContainsFoldersElement()
        {
            // Arrange
            var folder = new KeywordFolder
            {
                Name = "Test Folder",
                Id = "test-folder",
                IsExpanded = false
            };

            var keyword = new Keyword2Find
            {
                Alias = "Test Keyword",
                Kws = "test search",
                ParentFolder = folder
            };

            folder.Keywords.Add(keyword);
            _queryList.Folders.Add(folder);

            // Act - Serialize to XML string
            var serializer = new System.Xml.Serialization.XmlSerializer(typeof(QueryList));
            var stream = new MemoryStream();
            serializer.Serialize(stream, _queryList);

            stream.Position = 0;
            var xmlContent = new StreamReader(stream).ReadToEnd();

            // Assert - Check that the XML contains the new Folders element
            Assert.IsTrue(xmlContent.Contains("<Folders>"), "Serialized XML should contain <Folders> element");
            Assert.IsTrue(xmlContent.Contains("<KeywordFolder>"), "Serialized XML should contain <KeywordFolder> element");
            Assert.IsTrue(xmlContent.Contains("<IsExpanded>false</IsExpanded>"), "Serialized XML should contain IsExpanded property");

            // Also check backward compatibility - should still contain Keywords element
            Assert.IsTrue(xmlContent.Contains("<Keywords>"), "Serialized XML should contain <Keywords> element for backward compatibility");
        }
    }
}
