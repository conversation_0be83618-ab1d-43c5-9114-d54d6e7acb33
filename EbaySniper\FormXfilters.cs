﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Filtering;
using uBuyFirst.Auth;
using uBuyFirst.Properties;
using uBuyFirst.Tools;
using uBuyFirst.Filters;

namespace uBuyFirst
{
    public partial class FormXfilters : RibbonForm, IFormDataAccessor
    {
        public XFilterClass CurrentGridRule { get; set; }
        public DataTable DataTable { get; set; }
        public BindingList<EbayAccount> EbayAccountsList { get; set; }
        private MemoEdit _expressionEdit;
        public List<string> Keywords { get; set; }
        private string _lastUsedColumn;
        private ClauseType _lastUsedOperation;
        public Point FiltersFormLocation { get; set; }

        // New action system fields
        private List<IFilterAction> _availableActions;
        private IFilterAction _currentAction;
        private IFilterActionUIConfigurator _currentUIConfigurator;

        // Temporary fields to hold changes until OK is clicked
        private IFilterAction _pendingAction;
        private IFilterActionUIConfigurator _pendingUIConfigurator;
        private IFilterAction _originalAction;
        private string _originalActionIdentifier;
        public FormXfilters()
        {
            InitializeComponent();

            // Add FormClosing event handler to restore original action on cancel
            this.FormClosing += FormXfilters_FormClosing;
        }

        private void FormXfilters_FormClosing(object sender, FormClosingEventArgs e)
        {
            // If dialog is being cancelled (not OK), restore original action
            if (DialogResult != DialogResult.OK && CurrentGridRule != null)
            {
                // Restore original action if it was changed
                if (_originalAction != null || !string.IsNullOrEmpty(_originalActionIdentifier))
                {
                    CurrentGridRule.ActionHandler = _originalAction;
                    CurrentGridRule.ActionIdentifier = _originalActionIdentifier;
                }
            }
        }

        private void FormXfilters_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize available actions
                _availableActions = FilterActionFactory.GetAllAvailableActions().ToList();

                // Add dynamic actions (like Buy with accounts)
                if (EbayAccountsList != null)
                {
                    foreach (var account in EbayAccountsList.Where(a => a != null))
                    {
                        var buyAction = new BuyWithAccountAction { AccountUsername = account.UserName };
                        _availableActions.Add(buyAction);
                    }
                }

                // Populate action dropdown
                boxAction.Properties.Items.Clear();
                boxAction.Properties.Items.AddRange(_availableActions.Select(a => a.DisplayName).ToArray());

                // Load current filter
                LoadCurrentFilter();
                boxKeyword.Properties.Items.Add("All keywords");
                boxKeyword.Properties.Items.AddRange(Keywords);
                boxColumns.Text = CurrentGridRule.FormatColumn;
                txtAlias.Text = CurrentGridRule.Alias;
                boxColumns.Properties.Items.Clear();
                var columnsList = new List<string>();
                foreach (DataColumn column in DataTable.Columns)
                    if (column.Caption != "Description" && column.Caption != "Blob" && column.Caption != "Thumbnail" && column.Caption != "Term")
                        columnsList.Add(column.Caption);
                columnsList.Sort();
                boxColumns.Properties.Items.AddRange(columnsList);

                _expressionEdit = gridPanel.MyForm.Controls.Find("expressionEdit", true).FirstOrDefault() as MemoEdit;
                if (_expressionEdit != null)
                {
                    _expressionEdit.Text = ((FormatConditionRuleExpression)CurrentGridRule.GridFormatRule.Rule).Expression;
                }

                propertyGridControl1.SelectedObject = ((FormatConditionRuleExpression)CurrentGridRule.GridFormatRule.Rule).Appearance;
                filterEditorControl1.FilterCriteria = null;
                filterEditorControl1.SourceControl = null;
                filterEditorControl1.SourceControl = DataTable;
                filterEditorControl1.FilterCriteria = CurrentGridRule.FilterCriteria;
                using var colBlob = filterEditorControl1.FilterColumns.GetFilterColumnByCaption("Blob");
                if (colBlob != null)
                    filterEditorControl1.FilterColumns.Remove(colBlob);

                using var colSource = filterEditorControl1.FilterColumns.GetFilterColumnByCaption("Source");
                if (colSource != null)
                    filterEditorControl1.FilterColumns.Remove(colSource);

                //Deprecate Term column Nudge user to use Alias instead.
                using var colTerm = filterEditorControl1.FilterColumns.GetFilterColumnByCaption("Term");
                if (colSource != null)
                    filterEditorControl1.FilterColumns.Remove(colTerm);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FormXfilters_Load: ", ex);
            }
        }

        private void LoadCurrentFilter()
        {
            if (CurrentGridRule?.ActionHandler != null)
            {
                _currentAction = CurrentGridRule.ActionHandler;
                _currentUIConfigurator = FilterActionUIRegistry.GetUIConfigurator(_currentAction.ActionTypeIdentifier);

                // Store original values for rollback on cancel
                _originalAction = _currentAction;
                _originalActionIdentifier = CurrentGridRule.ActionIdentifier;

                boxAction.SelectedItem = _currentAction.DisplayName;

                if (_currentUIConfigurator != null)
                {
                    ApplyUIConfiguration(_currentUIConfigurator.GetUIConfiguration());
                    _currentUIConfigurator.LoadDataFromFilter(CurrentGridRule, this);
                }
            }
            else if (!string.IsNullOrEmpty(CurrentGridRule?.Action))
            {
                // Handle legacy actions
                var legacyAction = FilterActionFactory.CreateFromLegacyAction(CurrentGridRule.Action);
                if (legacyAction != null)
                {
                    // Store original values before modifying
                    _originalAction = CurrentGridRule.ActionHandler;
                    _originalActionIdentifier = CurrentGridRule.ActionIdentifier;

                    CurrentGridRule.ActionHandler = legacyAction;
                    CurrentGridRule.ActionIdentifier = legacyAction.ActionTypeIdentifier;

                    // Load action-specific data for actions that have it
                    if (legacyAction is BuyWithAccountAction buyAction)
                    {
                        buyAction.LoadFromFilter(CurrentGridRule);
                    }
                    else if (legacyAction is SendToWebhookAction webhookAction)
                    {
                        webhookAction.LoadFromFilter(CurrentGridRule);
                    }

                    LoadCurrentFilter(); // Recursive call to handle the now-loaded action
                }
                else
                {
                    // Fallback to legacy string selection
                    _originalAction = null;
                    _originalActionIdentifier = CurrentGridRule?.ActionIdentifier;
                    boxAction.SelectedItem = CurrentGridRule.Action;
                }
            }
            else
            {
                // No action set - store null as original
                _originalAction = null;
                _originalActionIdentifier = null;
            }
        }

        private void btnAccept_Click(object sender, EventArgs e)
        {
            // Apply pending action changes to CurrentGridRule
            if (_pendingAction != null && CurrentGridRule != null)
            {
                CurrentGridRule.ActionHandler = _pendingAction;
                CurrentGridRule.ActionIdentifier = _pendingAction.ActionTypeIdentifier;
                _currentAction = _pendingAction;
                _currentUIConfigurator = _pendingUIConfigurator;
            }

            // Validate and save using the UI configurator and action
            if (_currentAction != null && CurrentGridRule != null)
            {
                if (!_currentAction.ValidateConfiguration(CurrentGridRule, out var errorMessage))
                {
                    XtraMessageBox.Show($"Configuration error: {errorMessage}");
                    return;
                }

                // Save UI-specific data through configurator
                if (_currentUIConfigurator != null)
                {
                    _currentUIConfigurator.SaveDataToFilter(CurrentGridRule, this);
                }

                // Save action-specific data for actions that have it
                if (_currentAction is BuyWithAccountAction buyAction)
                {
                    buyAction.SaveToFilter(CurrentGridRule);
                }
                else if (_currentAction is SendToWebhookAction webhookAction)
                {
                    webhookAction.SaveToFilter(CurrentGridRule);
                }
            }

            // Save common properties
            CurrentGridRule.Action = boxAction.Text;
            CurrentGridRule.FormatColumn = boxColumns.Text;
            CurrentGridRule.Alias = txtAlias.Text;
            CurrentGridRule.FilterCriteria = filterEditorControl1.FilterCriteria;
            CurrentGridRule.SerializeAppearance();
            CurrentGridRule.Expression = filterEditorControl1.FilterCriteria?.ToString();

            if (!filterEditorControl1.IsFilterCriteriaValid)
            {
                XtraMessageBox.Show(En_US.FormXfilters_btnAccept_Click_Couldn_t_validate_the_condition_rule_Try_to_change_it_);
                return;
            }

            if (!dxValidationProvider1.Validate())
            {
                return;
            }

            if (!Form1.LicenseUtility.CurrentLimits.FiltersEnabled)
            {
                XtraMessageBox.Show("Filters aren't available with your current license. Please upgrade to a paid license.");
            }
            DialogResult = DialogResult.OK;
            Close();
        }

        private void filterEditorControl1_FilterChanged(object sender, FilterChangedEventArgs e)
        {
            switch (e.Action)
            {
                case FilterChangedAction.AddNode:
                    {
                        if (e.CurrentNode is not ClauseNode node)
                        {
                            return;
                        }

                        if (string.IsNullOrEmpty(_lastUsedColumn))
                        {
                            node.FirstOperand.PropertyName = "Title";
                            node.Operation = ClauseType.Contains;
                        }
                        else
                        {
                            node.FirstOperand.PropertyName = _lastUsedColumn;
                            node.ValidateAdditionalOperands();
                            try
                            {
                                if (_lastUsedOperation is ClauseType.Between or ClauseType.NotBetween or ClauseType.InRange or ClauseType.NotInRange)
                                    node.Operation = ClauseType.Contains;
                                else
                                    node.Operation = _lastUsedOperation;
                            }
                            catch (Exception exception)
                            {
                                Console.WriteLine(exception);
                            }
                        }

                        return;
                    }
                case FilterChangedAction.FieldNameChange:
                    {
                        if (e.CurrentNode is ClauseNode node)
                        {
                            _lastUsedColumn = node.FirstOperand.PropertyName;
                        }

                        break;
                    }
            }

            if (e is { Action: FilterChangedAction.OperationChanged, CurrentNode: ClauseNode })
            {
                _lastUsedOperation = ((ClauseNode)e.CurrentNode).Operation;
            }

            try
            {
                if (e.CurrentNode == null || (e.CurrentNode.GetType() != typeof(ClauseNode) && e.CurrentNode.GetType() != typeof(ClauseNodeEx)))
                    return;

                var node = (ClauseNode)e.CurrentNode;

                if (node.Operation != ClauseType.AnyOf && node.Operation != ClauseType.NoneOf)
                    return;

                char[] separators = { ';', ',' };
                var newCriteriaOps = new List<CriteriaOperator>();
                foreach (var op in node.AdditionalOperands)
                {
                    if (op.GetType() != typeof(OperandValue))
                        continue;

                    var valOp = (OperandValue)op;
                    if (valOp.Value == null || valOp.Value.GetType() != typeof(string))
                        continue;

                    var val = (string)valOp.Value;
                    var arr = val.Split(separators);
                    if (arr.Length <= 1)
                    {
                        continue;
                    }

                    if (XtraMessageBox.Show(this, "Perform string separation?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                        return;

                    var killSpaces = XtraMessageBox.Show(this, "Strip space characters?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes;
                    valOp.Value = killSpaces ? arr[0].Replace(" ", "") : arr[0];
                    for (var i = 1; i < arr.Length; i++)
                    {
                        newCriteriaOps.Add(new OperandValue(killSpaces ? arr[i].Replace(" ", "") : arr[i]));
                    }
                }

                foreach (var op in newCriteriaOps)
                {
                    node.AdditionalOperands.Add(op);
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FilterChanged: ", ex);
            }
        }

        private void boxAction_SelectedIndexChanged(object sender, EventArgs e)
        {
            var selectedDisplayName = boxAction.Text;
            _pendingAction = _availableActions?.FirstOrDefault(a => a.DisplayName == selectedDisplayName);

            if (_pendingAction != null)
            {
                // Get the UI configurator for this action
                _pendingUIConfigurator = FilterActionUIRegistry.GetUIConfigurator(_pendingAction.ActionTypeIdentifier);

                if (_pendingUIConfigurator != null)
                {
                    // Apply UI configuration declaratively (this is just UI changes, not data changes)
                    var uiConfig = _pendingUIConfigurator.GetUIConfiguration();
                    ApplyUIConfiguration(uiConfig);

                    // Load data from filter for UI display purposes only
                    // Don't modify CurrentGridRule yet - wait for OK button
                    if (CurrentGridRule != null)
                    {
                        // Create a temporary copy to load data for display without modifying the original
                        var tempRule = new XFilterClass
                        {
                            ActionHandler = _pendingAction,
                            ActionIdentifier = _pendingAction.ActionTypeIdentifier,
                            // Copy other relevant properties for data loading
                            Action = CurrentGridRule.Action,
                            Alias = CurrentGridRule.Alias,
                            FilterCriteria = CurrentGridRule.FilterCriteria
                        };

                        _pendingUIConfigurator.LoadDataFromFilter(tempRule, this);

                        // Load action-specific data for actions that have it (for display only)
                        if (_pendingAction is BuyWithAccountAction buyAction)
                        {
                            buyAction.LoadFromFilter(CurrentGridRule); // Load from original for display
                        }
                        else if (_pendingAction is SendToWebhookAction webhookAction)
                        {
                            webhookAction.LoadFromFilter(CurrentGridRule); // Load from original for display
                        }
                    }
                }
            }
            else
            {
                // Clear pending changes and reset UI to default state
                _pendingAction = null;
                _pendingUIConfigurator = null;
                HideColumnSelection();
                HideFormatControls();
            }
        }

        private void txtAlias_TextChanged(object sender, EventArgs e)
        {
            dxValidationProvider1.Validate();
        }

        private void FormXfilters_LocationChanged(object sender, EventArgs e)
        {
            FiltersFormLocation = Location;
        }

        private void FormXfilters_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
        {
            var process = new Process();
            process.StartInfo.FileName = "https://ubuyfirst.com/09-local-filter-conditions/";
            try
            {
                process.Start();
            }
            catch
            {
                // ignored
            }
        }

        #region UI Configuration Methods

        private void ApplyUIConfiguration(FilterUIConfiguration config)
        {
            // Simple, declarative UI updates - no complex logic
            if (config.ShowColumnSelection)
            {
                ShowColumnSelection();
            }
            else
            {
                HideColumnSelection();
            }

            if (config.ShowFormatControls)
            {
                ShowFormatControls();
            }
            else
            {
                HideFormatControls();
            }

            // Apply additional control visibility
            foreach (var controlName in config.AdditionalControlsToShow)
            {
                var control = GetControlByName(controlName);
                if (control != null) control.Visible = true;
            }

            foreach (var controlName in config.AdditionalControlsToHide)
            {
                var control = GetControlByName(controlName);
                if (control != null) control.Visible = false;
            }
        }

        public void ShowFormatControls()
        {
            propertyGridControl1.Visible = true;
            lblFormatStyle.Visible = true;
        }

        public void HideFormatControls()
        {
            propertyGridControl1.Visible = false;
            lblFormatStyle.Visible = false;
        }

        public void ShowColumnSelection()
        {
            boxColumns.Visible = true;
            lblFormatColumn.Visible = true;
        }

        public void HideColumnSelection()
        {
            boxColumns.Visible = false;
            lblFormatColumn.Visible = false;
        }

        private Control GetControlByName(string controlName)
        {
            // Use reflection to get the control by name
            var field = GetType().GetField(controlName,
                System.Reflection.BindingFlags.NonPublic |
                System.Reflection.BindingFlags.Instance);

            return field?.GetValue(this) as Control;
        }

        #endregion

        #region IFormDataAccessor Implementation

        public string GetSelectedColumn() => boxColumns.Text;
        public void SetSelectedColumn(string column) => boxColumns.Text = column;

        public T GetControlValue<T>(string controlName)
        {
            var control = GetControlByName(controlName);
            if (control == null) return default(T);

            try
            {
                object value = control switch
                {
                    ComboBoxEdit comboBoxEdit => comboBoxEdit.SelectedItem,
                    TextEdit textEdit => textEdit.Text,
                    CheckEdit checkEdit => checkEdit.Checked,
                    _ => null
                };

                if (value is T typedValue)
                    return typedValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }

        public void SetControlValue(string controlName, object value)
        {
            var control = GetControlByName(controlName);
            if (control == null) return;

            try
            {
                switch (control)
                {
                    case ComboBoxEdit comboBoxEdit:
                        comboBoxEdit.SelectedItem = value;
                        break;
                    case TextEdit textEdit:
                        textEdit.Text = value?.ToString() ?? "";
                        break;

                    case CheckEdit checkEdit:
                        checkEdit.Checked = value is bool b && b;
                        break;
                    default:
                        // Handle other control types as needed
                        break;
                }
            }
            catch
            {
                // Ignore errors in setting control values
            }
        }

        #endregion
    }
}
