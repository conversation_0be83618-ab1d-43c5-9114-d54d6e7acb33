﻿using System.Drawing;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Provides styling information for validation warnings in TreeList
    /// </summary>
    public class ValidationStyleProvider
    {
        /// <summary>
        /// Gets warning colors consistent with existing application color scheme
        /// </summary>
        /// <returns>Warning background color</returns>
        public static Color GetWarningBackgroundColor()
        {
            // Use light orange for warnings to distinguish from folder yellow styling
            // Provides clear visual distinction while maintaining good readability
            return Color.FromArgb(255, 220, 200); // Light orange, distinct from folder yellow
        }

        /// <summary>
        /// Gets warning text color for contrast with warning background
        /// </summary>
        /// <returns>Warning text color</returns>
        public static Color GetWarningTextColor()
        {
            // Use dark orange for warning text to provide good contrast
            return Color.FromArgb(204, 102, 0); // Dark orange
        }

        /// <summary>
        /// Gets warning border color for cell highlighting
        /// </summary>
        /// <returns>Warning border color</returns>
        public static Color GetWarningBorderColor()
        {
            // Use orange color for warning borders to complement the light orange background
            return Color.FromArgb(255, 165, 0); // Orange border to match the orange theme
        }

        /// <summary>
        /// Applies warning styling to a cell appearance
        /// </summary>
        /// <param name="appearance">The appearance object to modify</param>
        public static void ApplyWarningStyle(DevExpress.Utils.AppearanceObject appearance)
        {
            if (appearance == null) return;

            // Apply warning background color
            appearance.BackColor = GetWarningBackgroundColor();

            // Apply warning border
            appearance.BorderColor = GetWarningBorderColor();
            appearance.Options.UseBorderColor = true;

            // Make text slightly darker for better contrast
            appearance.ForeColor = GetWarningTextColor();
        }

        /// <summary>
        /// Checks if the current appearance has warning styling applied
        /// </summary>
        /// <param name="appearance">The appearance to check</param>
        /// <returns>True if warning styling is applied</returns>
        public static bool HasWarningStyle(DevExpress.Utils.AppearanceObject appearance)
        {
            if (appearance == null) return false;

            return appearance.BackColor == GetWarningBackgroundColor() &&
                   appearance.BorderColor == GetWarningBorderColor() &&
                   appearance.ForeColor == GetWarningTextColor();
        }

        /// <summary>
        /// Removes warning styling from a cell appearance
        /// </summary>
        /// <param name="appearance">The appearance object to modify</param>
        public static void RemoveWarningStyle(DevExpress.Utils.AppearanceObject appearance)
        {
            if (appearance == null) return;

            // Reset to default colors (transparent/empty)
            appearance.BackColor = Color.Empty;
            appearance.BorderColor = Color.Empty;
            appearance.ForeColor = Color.Empty;
            appearance.Options.UseBorderColor = false;
        }
    }
}
