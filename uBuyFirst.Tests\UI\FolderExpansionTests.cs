using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;
using System.Collections.Generic;

namespace uBuyFirst.Tests.UI
{
    /// <summary>
    /// Tests for folder expansion behavior to ensure that creating new folders
    /// doesn't expand all existing folders, but only expands the necessary parent path
    /// </summary>
    [TestClass]
    public class FolderExpansionTests
    {
        private QueryList _queryList;

        [TestInitialize]
        public void Setup()
        {
            _queryList = new QueryList();
        }

        [TestMethod]
        public void EnsureFolderPathExpanded_RootFolder_NoExpansionNeeded()
        {
            // Arrange
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root Folder",
                IsExpanded = false // Start collapsed
            };
            _queryList.Folders.Add(rootFolder);

            // Act - Creating a root folder should not require any expansion
            // (This simulates what happens in CreateNewRootFolder)
            
            // Assert - Root folder expansion state should remain unchanged
            Assert.IsFalse(rootFolder.IsExpanded, "Root folder expansion state should not change");
        }

        [TestMethod]
        public void EnsureFolderPathExpanded_SubFolder_ParentShouldExpand()
        {
            // Arrange
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root Folder",
                IsExpanded = false // Start collapsed
            };
            
            var childFolder = new KeywordFolder 
            { 
                Name = "Child Folder",
                ParentFolder = rootFolder,
                IsExpanded = true // New folders default to expanded
            };
            
            rootFolder.Children.Add(childFolder);
            _queryList.Folders.Add(rootFolder);

            // Act - Simulate the logic from EnsureFolderPathExpanded
            // When creating a subfolder, its parent path should be expanded
            var current = childFolder.ParentFolder;
            var pathToExpand = new List<KeywordFolder>();
            
            while (current != null)
            {
                pathToExpand.Insert(0, current);
                current = current.ParentFolder;
            }

            // Simulate expanding the path
            foreach (var folderInPath in pathToExpand)
            {
                folderInPath.IsExpanded = true;
            }

            // Assert
            Assert.IsTrue(rootFolder.IsExpanded, "Parent folder should be expanded to make child visible");
            Assert.IsTrue(childFolder.IsExpanded, "New child folder should remain expanded");
        }

        [TestMethod]
        public void EnsureFolderPathExpanded_DeepNesting_AllParentsShouldExpand()
        {
            // Arrange
            var rootFolder = new KeywordFolder 
            { 
                Name = "Root",
                IsExpanded = false
            };
            
            var level1Folder = new KeywordFolder 
            { 
                Name = "Level 1",
                ParentFolder = rootFolder,
                IsExpanded = false
            };
            
            var level2Folder = new KeywordFolder 
            { 
                Name = "Level 2",
                ParentFolder = level1Folder,
                IsExpanded = false
            };
            
            var newFolder = new KeywordFolder 
            { 
                Name = "New Folder",
                ParentFolder = level2Folder,
                IsExpanded = true
            };

            // Build hierarchy
            rootFolder.Children.Add(level1Folder);
            level1Folder.Children.Add(level2Folder);
            level2Folder.Children.Add(newFolder);
            _queryList.Folders.Add(rootFolder);

            // Act - Simulate EnsureFolderPathExpanded logic
            var current = newFolder.ParentFolder;
            var pathToExpand = new List<KeywordFolder>();
            
            while (current != null)
            {
                pathToExpand.Insert(0, current);
                current = current.ParentFolder;
            }

            foreach (var folderInPath in pathToExpand)
            {
                folderInPath.IsExpanded = true;
            }

            // Assert
            Assert.IsTrue(rootFolder.IsExpanded, "Root folder should be expanded");
            Assert.IsTrue(level1Folder.IsExpanded, "Level 1 folder should be expanded");
            Assert.IsTrue(level2Folder.IsExpanded, "Level 2 folder should be expanded");
            Assert.IsTrue(newFolder.IsExpanded, "New folder should remain expanded");
        }

        [TestMethod]
        public void FolderCreation_ShouldNotAffectUnrelatedFolders()
        {
            // Arrange
            var folder1 = new KeywordFolder 
            { 
                Name = "Unrelated Folder 1",
                IsExpanded = false
            };
            
            var folder2 = new KeywordFolder 
            { 
                Name = "Unrelated Folder 2",
                IsExpanded = false
            };
            
            var parentFolder = new KeywordFolder 
            { 
                Name = "Parent Folder",
                IsExpanded = false
            };

            _queryList.Folders.Add(folder1);
            _queryList.Folders.Add(folder2);
            _queryList.Folders.Add(parentFolder);

            // Act - Create a new folder under parentFolder
            var newFolder = new KeywordFolder 
            { 
                Name = "New Folder",
                ParentFolder = parentFolder,
                IsExpanded = true
            };
            parentFolder.Children.Add(newFolder);

            // Simulate selective expansion (only expand parent path)
            var current = newFolder.ParentFolder;
            while (current != null)
            {
                current.IsExpanded = true;
                current = current.ParentFolder;
            }

            // Assert
            Assert.IsFalse(folder1.IsExpanded, "Unrelated folder 1 should remain collapsed");
            Assert.IsFalse(folder2.IsExpanded, "Unrelated folder 2 should remain collapsed");
            Assert.IsTrue(parentFolder.IsExpanded, "Parent folder should be expanded");
            Assert.IsTrue(newFolder.IsExpanded, "New folder should be expanded");
        }

        [TestMethod]
        public void FolderExpansion_PreservesUserCollapsedState()
        {
            // Arrange - User has manually collapsed some folders
            var userCollapsedFolder = new KeywordFolder 
            { 
                Name = "User Collapsed Folder",
                IsExpanded = false // User manually collapsed this
            };
            
            var userExpandedFolder = new KeywordFolder 
            { 
                Name = "User Expanded Folder",
                IsExpanded = true // User manually expanded this
            };

            _queryList.Folders.Add(userCollapsedFolder);
            _queryList.Folders.Add(userExpandedFolder);

            // Act - Create a new root folder (should not affect existing folders)
            var newRootFolder = new KeywordFolder 
            { 
                Name = "New Root Folder",
                IsExpanded = true
            };
            _queryList.Folders.Add(newRootFolder);

            // Assert - User's expansion preferences should be preserved
            Assert.IsFalse(userCollapsedFolder.IsExpanded, "User's collapsed folder should remain collapsed");
            Assert.IsTrue(userExpandedFolder.IsExpanded, "User's expanded folder should remain expanded");
            Assert.IsTrue(newRootFolder.IsExpanded, "New root folder should be expanded");
        }
    }
}
