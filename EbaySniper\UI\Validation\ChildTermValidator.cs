using System.Text.RegularExpressions;
using uBuyFirst.SubSearch;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Validator for ChildTerm objects
    /// </summary>
    public class ChildTermValidator : ITreeListValidator<ChildTerm>, ITreeListValidator
    {
        /// <summary>
        /// Validates a ChildTerm object and returns validation results
        /// </summary>
        /// <param name="item">The ChildTerm to validate</param>
        /// <param name="context">Validation context containing additional information</param>
        /// <returns>Validation result containing any warnings</returns>
        public ValidationResult Validate(ChildTerm item, ValidationContext context)
        {
            if (item == null)
                return ValidationResult.Success();

            var result = new ValidationResult();

            ValidateAlias(item, result);
            ValidateKeywords(item, result);
            ValidatePrices(item, result);
            ValidateCategories(item, result);

            return result;
        }

        /// <summary>
        /// Non-generic validation method
        /// </summary>
        public ValidationResult Validate(object item, ValidationContext context)
        {
            if (item is ChildTerm childTerm)
                return Validate(childTerm, context);

            return ValidationResult.Success();
        }

        /// <summary>
        /// Determines if this validator can handle the specified item
        /// </summary>
        public bool CanValidate(object item)
        {
            return item is ChildTerm;
        }

        private void ValidateAlias(ChildTerm item, ValidationResult result)
        {
            if (string.IsNullOrEmpty(item.Alias))
            {
                result.AddWarning("Alias", $"Alias field should not be empty. [{item.Keywords}]");
            }
        }

        private void ValidateKeywords(ChildTerm item, ValidationResult result)
        {
            if (string.IsNullOrEmpty(item.Keywords))
            {
                result.AddWarning("Keywords", $"Keywords field should not be empty. [{item.Alias}]");
            }
        }

        private void ValidatePrices(ChildTerm item, ValidationResult result)
        {
            var priceMin = item.PriceMin;
            var priceMax = item.PriceMax;

            if (priceMin < 0.01 || priceMin > 10000000)
            {
                result.AddWarning("Price Min", $"Valid price range is 0.01 - 10,000,000. [{item.Alias}]");
            }

            if (priceMax < 0.01 || priceMax > 10000000)
            {
                result.AddWarning("Price Max", $"Valid price range is 0.01 - 10,000,000. [{item.Alias}]");
            }
        }

        private void ValidateCategories(ChildTerm item, ValidationResult result)
        {
            if (item.CategoryIDs.Length > 20)
            {
                result.AddWarning("Category ID", $"Maximum number of categories for Sub Search is 20. [{item.Alias}]");
            }

            if (!Regex.IsMatch(string.Join(",", item.CategoryIDs), "^[0-9,\\s]*$", RegexOptions.None))
            {
                result.AddWarning("Category ID", $"Category field should be empty OR contain only comma separated numbers. [{item.Alias}]");
            }
        }
    }
}
