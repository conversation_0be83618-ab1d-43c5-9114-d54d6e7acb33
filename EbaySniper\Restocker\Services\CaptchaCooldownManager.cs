﻿using System;
using System.Diagnostics;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Represents the type of cooldown currently active
    /// </summary>
    public enum CooldownType
    {
        None,
        Captcha,
        Logout
    }

    /// <summary>
    /// Result of automated captcha solving attempt
    /// </summary>
    public class AutomatedCaptchaResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }

        public static AutomatedCaptchaResult Success()
        {
            return new AutomatedCaptchaResult { IsSuccess = true };
        }

        public static AutomatedCaptchaResult Failure(string errorMessage)
        {
            return new AutomatedCaptchaResult { IsSuccess = false, ErrorMessage = errorMessage };
        }
    }

    /// <summary>
    /// Manages captcha and logout detection and cooldown periods for restock operations.
    /// When a captcha or logout is detected, all restock purchasing is paused for 5 minutes.
    /// </summary>
    public static class CaptchaCooldownManager
    {
        private static DateTime? _cooldownStartTime;
        private static CooldownType _cooldownType = CooldownType.None;
        private static readonly TimeSpan CooldownDuration = TimeSpan.FromMinutes(1);
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets whether restock operations are currently in cooldown due to captcha or logout detection
        /// </summary>
        public static bool IsInCooldown
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return false;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    if (elapsed >= CooldownDuration)
                    {
                        // Cooldown period has expired, clear it
                        var cooldownTypeText = _cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                        _cooldownStartTime = null;
                        _cooldownType = CooldownType.None;
                        Debug.WriteLine($"Restock {cooldownTypeText} cooldown period has expired. Resuming auto purchasing.");
                        return false;
                    }

                    return true;
                }
            }
        }

        /// <summary>
        /// Gets the remaining cooldown time, or null if not in cooldown
        /// </summary>
        public static TimeSpan? RemainingCooldownTime
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return null;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    var remaining = CooldownDuration - elapsed;

                    return remaining > TimeSpan.Zero ? remaining : null;
                }
            }
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations due to captcha detection
        /// </summary>
        public static void StartCooldown()
        {
            StartCooldown(CooldownType.Captcha);
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations due to logout detection
        /// </summary>
        public static void StartLogoutCooldown()
        {
            StartCooldown(CooldownType.Logout);
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations
        /// </summary>
        /// <param name="cooldownType">The type of cooldown to start</param>
        private static void StartCooldown(CooldownType cooldownType)
        {
            lock (_lock)
            {
                _cooldownStartTime = DateTime.UtcNow;
                _cooldownType = cooldownType;
                var cooldownTypeText = cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                Debug.WriteLine($"Restock {cooldownTypeText} cooldown started at {_cooldownStartTime.Value:HH:mm:ss UTC}. Auto purchasing paused for 5 minutes.");
            }
        }

        /// <summary>
        /// Manually clears the cooldown period (for testing or manual override)
        /// </summary>
        public static void ClearCooldown()
        {
            lock (_lock)
            {
                var cooldownTypeText = _cooldownType == CooldownType.Captcha ? "captcha" :
                                      _cooldownType == CooldownType.Logout ? "logout" : "unknown";
                _cooldownStartTime = null;
                _cooldownType = CooldownType.None;
                Debug.WriteLine($"Restock {cooldownTypeText} cooldown manually cleared. Auto purchasing resumed.");
            }
        }

        /// <summary>
        /// Gets the current cooldown type
        /// </summary>
        /// <returns>The type of cooldown currently active, or None if not in cooldown</returns>
        public static CooldownType GetCooldownType()
        {
            lock (_lock)
            {
                return IsInCooldown ? _cooldownType : CooldownType.None;
            }
        }

        /// <summary>
        /// Checks if the given error message indicates a captcha scenario
        /// </summary>
        /// <param name="errorMessage">The error message to check</param>
        /// <returns>True if the error indicates a captcha scenario</returns>
        public static bool IsCaptchaError(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return false;

            if (errorMessage == Config.CaptchaUrl)
                return true;

            return false;
        }

        /// <summary>
        /// Checks if the given error message or response content indicates a logout scenario
        /// </summary>
        /// <param name="content">The error message or response content to check</param>
        /// <returns>True if the content indicates a logout scenario (signin redirect)</returns>
        public static bool IsLogoutError(string content)
        {
            if (string.IsNullOrEmpty(content))
                return false;

            if (content == Config.EbaySignInUrl)
            {
                return true;
            }

            // Check for our specific logout error message
            if (content.IndexOf("User logged out - signin redirect detected", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for eBay signin redirect URL pattern (most common)
            if (content.IndexOf("signin.ebay.com/ws/eBayISAPI.dll?SignIn", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for other logout indicators in HTML content
            if (content.IndexOf("signin.ebay.com", StringComparison.OrdinalIgnoreCase) >= 0 &&
                content.IndexOf("SignIn", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            // Check for "Not logged in" or similar messages
            if (content.IndexOf("Not logged in", StringComparison.OrdinalIgnoreCase) >= 0)
                return true;

            return false;
        }

        /// <summary>
        /// Gets a user-friendly status message about the current cooldown state
        /// </summary>
        public static string GetStatusMessage()
        {
            if (!IsInCooldown)
                return "Restock auto purchasing is active";

            var remaining = RemainingCooldownTime;
            if (remaining.HasValue)
            {
                var minutes = (int)remaining.Value.TotalMinutes;
                var seconds = remaining.Value.Seconds;

                var reason = _cooldownType == CooldownType.Captcha ? "captcha" : "logout";
                return $"Restock auto purchasing paused due to {reason}. Resuming in {minutes}m {seconds}s";
            }

            return "Restock auto purchasing is active";
        }

        /// <summary>
        /// Attempts to automatically solve a captcha using PicToText service
        /// </summary>
        /// <param name="dataList">The data list item that triggered the captcha</param>
        /// <returns>Result of the automated captcha solving attempt</returns>
        private static async Task<AutomatedCaptchaResult> TryAutomatedCaptchaSolvingAsync(DataList dataList)
        {
            try
            {
                Debug.WriteLine($"Attempting automated captcha solving for item {dataList.ItemID}");

                // Get cookies for the captcha request
                var domains = new[] { $".{dataList.EBaySite.Domain}" };
                var cookieContainer = CookieManager.GetCookies(domains);

                if (cookieContainer == null || cookieContainer.Count == 0)
                {
                    return AutomatedCaptchaResult.Failure("No cookies available for captcha solving");
                }

                string? srtParameter = null;
                if (!string.IsNullOrEmpty(NetTools.LastCaptchaUrl))
                {

                    // Download captcha page and extract srt parameter from HTML

                    srtParameter = await NetTools.ExtractSrtFromCaptchaPageAsync(NetTools.LastCaptchaUrl, cookieContainer);
                    if (!string.IsNullOrEmpty(srtParameter))
                    {

                        Debug.WriteLine($"Successfully extracted and stored srt parameter from captcha page");
                    }
                    else
                    {
                        Debug.WriteLine("Failed to extract srt parameter from captcha page");
                    }
                }

                // Initialize PicToText service with API key
                const string apiKey = "6addf951917c6bf7afd054e5b8745679"; // From PicToText.cs
                var picToText = new uBuyFirst.PicToText.PicToText(apiKey);

                string hCaptchaResponse;
                try
                {
                    // Solve the hCaptcha
                    var captchaUrl = "https://www.ebay.com/splashui/captcha";
                    var siteKey = "195eeb9f-8f50-4a9c-abfc-a78ceaa3cdde"; // From PicToText.cs

                    Debug.WriteLine("Solving hCaptcha...");
                    hCaptchaResponse = await picToText.SolveHCaptchaAsync(captchaUrl, siteKey);
                }
                finally
                {
                    // Manually dispose since PicToText has Dispose method but doesn't implement IDisposable
                    picToText?.Dispose();
                }

                if (string.IsNullOrEmpty(hCaptchaResponse))
                {
                    return AutomatedCaptchaResult.Failure("Failed to solve hCaptcha - empty response");
                }

                Debug.WriteLine($"hCaptcha solved successfully, token length: {hCaptchaResponse.Length}");

                // Submit the solved captcha
                var submissionService = new uBuyFirst.Restocker.Services.CaptchaSubmissionService();

                // Use the srt parameter extracted from the Location header in FetchUrlUsingCookiesAsync
                var ruParameter = "https%3A%2F%2Fpay.ebay.com"; // Return URL

                if (string.IsNullOrEmpty(srtParameter))
                {
                    Debug.WriteLine("No srt parameter available - captcha submission cannot proceed");
                    return AutomatedCaptchaResult.Failure("No srt parameter extracted from Location header");
                }

                var submissionResult = await submissionService.SubmitCaptchaAsync(
                    srtParameter,
                    ruParameter,
                    hCaptchaResponse,
                    cookieContainer);

                if (submissionResult.IsSuccess)
                {
                    Debug.WriteLine("Captcha submission successful - automated resolution complete");
                    return AutomatedCaptchaResult.Success();
                }
                else
                {
                    Debug.WriteLine($"Captcha submission failed: {submissionResult.ErrorMessage}");
                    return AutomatedCaptchaResult.Failure($"Captcha submission failed: {submissionResult.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error during automated captcha solving: {ex.Message}");
                return AutomatedCaptchaResult.Failure($"Exception during automated captcha solving: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles captcha detection by attempting automated solving first, then falling back to manual resolution.
        /// This unified method serves both manual purchase and automated restock scenarios.
        /// </summary>
        /// <param name="dataList">The data list item that triggered the captcha</param>
        /// <param name="errorMessage">The error message that indicated captcha detection</param>
        /// <param name="startCooldown">Whether to start the 5-minute cooldown period (true for automated restock, false for manual purchases)</param>
        public static async Task HandleCaptchaDetectionAsync(DataList dataList, string errorMessage, bool startCooldown = true)
        {
            try
            {
                Debug.WriteLine($"Captcha detected for item {dataList.ItemID}: {errorMessage}");

                // For automated restock scenarios, attempt automated captcha solving first
                if (UserSettings.RestockMode != RestockModeEnum.Disabled)
                {
                    var automatedResult = await TryAutomatedCaptchaSolvingAsync(dataList);

                    if (automatedResult.IsSuccess)
                    {
                        Debug.WriteLine($"Automated captcha solving successful for item {dataList.ItemID}");
                        // Don't start cooldown if automated solving succeeded
                        return;
                    }
                    else
                    {
                        Debug.WriteLine($"Automated captcha solving failed for item {dataList.ItemID}: {automatedResult.ErrorMessage}");
                        // Fall back to manual resolution
                    }
                }

                // Start the 5-minute cooldown period for automated restock scenarios (manual fallback)
                if (startCooldown)
                {
                    StartCooldown();
                }

                // Open Firefox with captcha URL - use a generic eBay captcha URL since we don't have the specific one
                var captchaUrl = "https://www.ebay.com/splashui/captcha";
                Browser.LaunchFirefox(captchaUrl);

                // Show alert notification to user
                await ShowCaptchaTrayAlertAsync();

                var cooldownMessage = startCooldown ? " and 5-minute cooldown started" : "";
                Debug.WriteLine($"Captcha detected for item {dataList.ItemID}. Firefox opened{cooldownMessage}.");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling captcha detection: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows a tray alert notification about captcha detection
        /// </summary>
        private static async Task ShowCaptchaTrayAlertAsync()
        {
            try
            {
                var form1 = Form1.Instance;
                if (form1 == null) return;

                // Check if we need to invoke on the UI thread
                if (form1.InvokeRequired)
                {
                    // Use TaskCompletionSource to make the synchronous Invoke call awaitable
                    var tcs = new TaskCompletionSource<bool>();

                    form1.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            form1.ShowCaptchaTrayAlert();
                            tcs.SetResult(true);
                        }
                        catch (Exception ex)
                        {
                            tcs.SetException(ex);
                        }
                    }));

                    // Wait for the UI thread operation to complete
                    await tcs.Task;
                }
                else
                {
                    // Already on UI thread, call directly
                    form1.ShowCaptchaTrayAlert();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing captcha tray alert: {ex.Message}");
            }
        }
    }
}
