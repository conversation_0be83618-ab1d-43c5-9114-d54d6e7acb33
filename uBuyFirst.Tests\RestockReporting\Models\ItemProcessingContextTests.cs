using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Newtonsoft.Json;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.RestockReporting.Models
{
    [TestClass]
    public class ItemProcessingContextTests
    {
        [TestMethod]
        public void ItemProcessingContext_DefaultConstructor_SetsDefaultValues()
        {
            // Act
            var context = new ItemProcessingContext();

            // Assert
            Assert.IsNotNull(context);
            Assert.AreEqual(DateTime.MinValue, context.Timestamp);
            Assert.IsNull(context.Outcome);
            Assert.IsNull(context.Reason);
            Assert.IsNull(context.ItemData);
            Assert.IsNull(context.KeywordState);
            Assert.IsNull(context.FilterRule);
            Assert.IsNull(context.TransactionResult);
        }

        [TestMethod]
        public void ItemProcessingContext_WithCompleteData_SerializesToJson()
        {
            // Arrange
            var context = CreateCompleteItemProcessingContext();

            // Act
            var json = JsonConvert.SerializeObject(context, Formatting.Indented);

            // Assert
            Assert.IsNotNull(json);
            Assert.IsTrue(json.Contains("\"outcome\": \"purchased\""));
            Assert.IsTrue(json.Contains("\"itemId\": \"123456789\""));
            Assert.IsTrue(json.Contains("\"keywordId\": \"kw-test-123\""));
            Assert.IsTrue(json.Contains("\"filterAlias\": \"Test Filter\""));
            Assert.IsTrue(json.Contains("\"success\": true"));
        }

        [TestMethod]
        public void ItemProcessingContext_JsonRoundTrip_PreservesAllData()
        {
            // Arrange
            var originalContext = CreateCompleteItemProcessingContext();

            // Act
            var json = JsonConvert.SerializeObject(originalContext);
            var deserializedContext = JsonConvert.DeserializeObject<ItemProcessingContext>(json);

            // Assert
            Assert.IsNotNull(deserializedContext);
            Assert.AreEqual(originalContext.Timestamp, deserializedContext.Timestamp);
            Assert.AreEqual(originalContext.Outcome, deserializedContext.Outcome);
            Assert.AreEqual(originalContext.Reason, deserializedContext.Reason);

            // Verify ItemData
            Assert.IsNotNull(deserializedContext.ItemData);
            Assert.AreEqual(originalContext.ItemData.ItemId, deserializedContext.ItemData.ItemId);
            Assert.AreEqual(originalContext.ItemData.Title, deserializedContext.ItemData.Title);
            Assert.AreEqual(originalContext.ItemData.CurrentPrice, deserializedContext.ItemData.CurrentPrice);

            // Verify KeywordState
            Assert.IsNotNull(deserializedContext.KeywordState);
            Assert.AreEqual(originalContext.KeywordState.KeywordId, deserializedContext.KeywordState.KeywordId);
            Assert.AreEqual(originalContext.KeywordState.Alias, deserializedContext.KeywordState.Alias);

            // Verify FilterRule
            Assert.IsNotNull(deserializedContext.FilterRule);
            Assert.AreEqual(originalContext.FilterRule.FilterAlias, deserializedContext.FilterRule.FilterAlias);
            Assert.AreEqual(originalContext.FilterRule.Matched, deserializedContext.FilterRule.Matched);

            // Verify TransactionResult
            Assert.IsNotNull(deserializedContext.TransactionResult);
            Assert.AreEqual(originalContext.TransactionResult.Success, deserializedContext.TransactionResult.Success);
        }

        [TestMethod]
        public void ItemHistoryData_WithAllProperties_SerializesCorrectly()
        {
            // Arrange
            var itemData = CreateCompleteItemHistoryData();

            // Act
            var json = JsonConvert.SerializeObject(itemData, Formatting.Indented);

            // Assert
            Assert.IsNotNull(json);
            Assert.IsTrue(json.Contains("\"itemId\": \"123456789\""));
            Assert.IsTrue(json.Contains("\"title\": \"Test iPhone Case\""));
            Assert.IsTrue(json.Contains("\"currentPrice\": 25.99"));
            Assert.IsTrue(json.Contains("\"condition\": \"New\""));
            Assert.IsTrue(json.Contains("\"seller\": \"test-seller\""));
        }

        [TestMethod]
        public void FilterRuleContext_WithEvaluationData_SerializesCorrectly()
        {
            // Arrange
            var filterRule = new FilterRuleContext
            {
                FilterAlias = "Test Restock Filter",
                Expression = "Price <= 40 AND Condition = 'New'",
                Matched = true,
                EvaluationResult = "Filter matched - proceeding with purchase",
                EvaluatedAt = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc)
            };

            // Act
            var json = JsonConvert.SerializeObject(filterRule, Formatting.Indented);

            // Assert
            Assert.IsNotNull(json);
            Assert.IsTrue(json.Contains("\"filterAlias\": \"Test Restock Filter\""));
            Assert.IsTrue(json.Contains("\"matched\": true"));
            Assert.IsTrue(json.Contains("\"evaluationResult\": \"Filter matched - proceeding with purchase\""));
        }

        [TestMethod]
        public void TransactionResult_SuccessfulPurchase_SerializesCorrectly()
        {
            // Arrange
            var transaction = new TransactionResult
            {
                Attempted = true,
                Success = true,
                ErrorMessage = null,
                PurchasePrice = 25.99m,
                Quantity = 2,
                CompletedAt = new DateTime(2024, 12, 19, 14, 30, 45, DateTimeKind.Utc)
            };

            // Act
            var json = JsonConvert.SerializeObject(transaction, Formatting.Indented);

            // Assert
            Assert.IsNotNull(json);
            Assert.IsTrue(json.Contains("\"attempted\": true"));
            Assert.IsTrue(json.Contains("\"success\": true"));

            Assert.IsTrue(json.Contains("\"purchasePrice\": 25.99"));
            Assert.IsTrue(json.Contains("\"quantity\": 2"));
        }

        [TestMethod]
        public void TransactionResult_FailedPurchase_SerializesCorrectly()
        {
            // Arrange
            var transaction = new TransactionResult
            {
                Attempted = true,
                Success = false,
                ErrorMessage = "Insufficient funds",
                PurchasePrice = null,
                Quantity = null,
                CompletedAt = null
            };

            // Act
            var json = JsonConvert.SerializeObject(transaction, Formatting.Indented);

            // Assert
            Assert.IsNotNull(json);
            Assert.IsTrue(json.Contains("\"attempted\": true"));
            Assert.IsTrue(json.Contains("\"success\": false"));
            Assert.IsTrue(json.Contains("\"errorMessage\": \"Insufficient funds\""));
        }

        private ItemProcessingContext CreateCompleteItemProcessingContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "purchased",
                Reason = "Successfully purchased 2 items for $25.99 each",
                ItemData = CreateCompleteItemHistoryData(),
                KeywordState = CreateCompleteKeywordSnapshot(),
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Filter",
                    Expression = "Price <= 40",
                    Matched = true,
                    EvaluationResult = "Filter matched",
                    EvaluatedAt = new DateTime(2024, 12, 19, 14, 30, 20, DateTimeKind.Utc)
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    PurchasePrice = 25.99m,
                    Quantity = 2,
                    CompletedAt = new DateTime(2024, 12, 19, 14, 30, 45, DateTimeKind.Utc)
                }
            };
        }

        private ItemHistoryData CreateCompleteItemHistoryData()
        {
            return new ItemHistoryData
            {
                ItemId = "123456789",
                Title = "Test iPhone Case",
                CurrentPrice = 25.99m,
                Condition = "New",
                Seller = "test-seller",
                ShippingCost = 5.99m,
                Location = "United States",
                EndTime = new DateTime(2024, 12, 20, 10, 0, 0, DateTimeKind.Utc),
                ItemPrice = 25.99m,
                AuctionPrice = null,
                QuantityAvailable = 5,
                ListingType = "FixedPrice",
                ItemUrl = "https://ebay.com/itm/123456789",
                ImageUrl = "https://i.ebayimg.com/test.jpg"
            };
        }

        private KeywordSnapshot CreateCompleteKeywordSnapshot()
        {
            return new KeywordSnapshot
            {
                KeywordId = "kw-test-123",
                Alias = "iPhone Cases",
                JobId = "JOB-001",
                Keywords = "iphone,case,protective",
                RequiredQuantity = 10,
                PurchasedQuantity = 6,
                PriceMin = 15.0,
                PriceMax = 40.0,
                Condition = new[] { "New", "Used" },
                Sellers = "seller1;seller2",
                SellerType = "Include",
                CapturedAt = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc)
            };
        }
    }
}
