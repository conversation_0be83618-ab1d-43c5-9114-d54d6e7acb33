using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class CaptchaSubmissionServiceTests
    {
        private CaptchaSubmissionService _service;

        [TestInitialize]
        public void Setup()
        {
            _service = new CaptchaSubmissionService();
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithNullSrtParameter_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync(null, "https://pay.ebay.com", "test-token", cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("SRT parameter is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithEmptySrtParameter_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync("", "https://pay.ebay.com", "test-token", cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("SRT parameter is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithNullRuParameter_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync("test-srt", null, "test-token", cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("RU parameter is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithEmptyRuParameter_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync("test-srt", "", "test-token", cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("RU parameter is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithNullHCaptchaResponse_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync("test-srt", "https://pay.ebay.com", null, cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("hCaptcha response is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithEmptyHCaptchaResponse_ShouldReturnFailure()
        {
            // Arrange
            var cookieContainer = new CookieContainer();
            
            // Act
            var result = await _service.SubmitCaptchaAsync("test-srt", "https://pay.ebay.com", "", cookieContainer);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("hCaptcha response is required", result.ErrorMessage);
        }

        [TestMethod]
        public async Task SubmitCaptchaAsync_WithNullCookieContainer_ShouldReturnFailure()
        {
            // Act
            var result = await _service.SubmitCaptchaAsync("test-srt", "https://pay.ebay.com", "test-token", null);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual("Cookie container is required", result.ErrorMessage);
        }

        [TestMethod]
        public void CaptchaSubmissionResult_Success_ShouldCreateSuccessResult()
        {
            // Arrange
            var responseContent = "success response";
            
            // Act
            var result = CaptchaSubmissionService.CaptchaSubmissionResult.Success(responseContent);
            
            // Assert
            Assert.IsTrue(result.IsSuccess);
            Assert.AreEqual(responseContent, result.ResponseContent);
            Assert.IsNull(result.ErrorMessage);
        }

        [TestMethod]
        public void CaptchaSubmissionResult_Failure_ShouldCreateFailureResult()
        {
            // Arrange
            var errorMessage = "Test error";
            var statusCode = HttpStatusCode.BadRequest;
            
            // Act
            var result = CaptchaSubmissionService.CaptchaSubmissionResult.Failure(errorMessage, statusCode);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual(errorMessage, result.ErrorMessage);
            Assert.AreEqual(statusCode, result.StatusCode);
        }

        [TestMethod]
        public void CaptchaSubmissionResult_FailureWithoutStatusCode_ShouldCreateFailureResult()
        {
            // Arrange
            var errorMessage = "Test error";
            
            // Act
            var result = CaptchaSubmissionService.CaptchaSubmissionResult.Failure(errorMessage);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual(errorMessage, result.ErrorMessage);
            Assert.IsNull(result.StatusCode);
        }
    }
}
