using System.Collections.Generic;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service interface for accessing keyword data in a testable, loosely-coupled way
    /// </summary>
    public interface IKeywordDataService
    {
        /// <summary>
        /// Gets a keyword by its unique identifier
        /// </summary>
        /// <param name="keywordId">The unique identifier of the keyword</param>
        /// <returns>The keyword if found, null otherwise</returns>
        Keyword2Find GetKeywordById(string keywordId);

        /// <summary>
        /// Gets multiple keywords by their unique identifiers
        /// </summary>
        /// <param name="keywordIds">Collection of keyword identifiers</param>
        /// <returns>List of found keywords (may be fewer than requested if some IDs are invalid)</returns>
        List<Keyword2Find> GetKeywordsByIds(IEnumerable<string> keywordIds);

        /// <summary>
        /// Gets all available keywords
        /// </summary>
        /// <returns>List of all keywords</returns>
        List<Keyword2Find> GetAllKeywords();
    }
}
