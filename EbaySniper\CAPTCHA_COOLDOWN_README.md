# Captcha Detection and Cooldown System

## Overview

This system automatically detects captcha scenarios during restock operations and implements a 5-minute cooldown period to prevent further purchase attempts while the user resolves the captcha.

## How It Works

### 1. Captcha Detection
- The system monitors purchase failures for the specific error message: "Session ID not found in session page HTML"
- This error typically indicates that e<PERSON>ay has presented a captcha challenge
- Detection is case-insensitive to handle variations in error message formatting

### 2. Automatic Response
When a captcha is detected, the system automatically:
1. **Opens Browser**: Launches the default browser with eBay's captcha page
2. **Shows Tray Alert**: Displays a notification informing the user that auto purchasing has been paused
3. **Starts Cooldown**: Initiates a 5-minute cooldown period during which all restock purchases are blocked

### 3. Cooldown Management
- **Duration**: 5 minutes from the time captcha is detected
- **Scope**: Affects all restock operations globally (not per-item or per-keyword)
- **Auto-Resume**: After 5 minutes, restock operations automatically resume
- **Manual Override**: Cooldown can be manually cleared if needed

## Implementation Details

### Key Components

#### CaptchaCooldownManager
- **Location**: `EbaySniper/Restocker/Services/CaptchaCooldownManager.cs`
- **Purpose**: Static service managing cooldown state and captcha detection
- **Key Methods**:
  - `IsInCooldown`: Checks if currently in cooldown period
  - `StartCooldown()`: Initiates 5-minute cooldown
  - `IsCaptchaError(string)`: Detects captcha-related error messages
  - `GetStatusMessage()`: Returns user-friendly status information

#### PurchaseExecutionService (Modified)
- **Location**: `EbaySniper/Restocker/Services/PurchaseExecutionService.cs`
- **Changes**:
  - Added cooldown check before attempting purchases
  - Added captcha detection after purchase failures
  - Integrated browser opening and tray alert functionality

#### Form1.Alerts (Modified)
- **Location**: `EbaySniper/Form1.Alerts.cs`
- **Changes**:
  - Added `ShowCaptchaTrayAlert()` method for displaying captcha notifications

### Integration Points

1. **Early Cooldown Check**: Before acquiring the restock semaphore, the system checks if a cooldown is active
2. **Post-Purchase Analysis**: After a purchase fails, the error message is analyzed for captcha indicators
3. **Thread-Safe Operations**: All UI operations (browser opening, tray alerts) are properly marshaled to the UI thread

## Usage

### Automatic Operation
The system operates automatically without user intervention:
- Restock operations continue normally until a captcha is detected
- Upon captcha detection, the system automatically pauses and notifies the user
- After 5 minutes, operations resume automatically

### Testing
The implementation includes comprehensive unit tests located in:
- **Test File**: `uBuyFirst.Tests/Restocker/Services/CaptchaCooldownManagerTests.cs`
- **Test Coverage**: Error detection, cooldown logic, status messages, and state management

### Manual Cooldown Management
If needed, you can manually control the cooldown:
```csharp
// Check cooldown status
bool inCooldown = CaptchaCooldownManager.IsInCooldown;

// Get remaining time
TimeSpan? remaining = CaptchaCooldownManager.RemainingCooldownTime;

// Get status message
string status = CaptchaCooldownManager.GetStatusMessage();

// Manually clear cooldown (if needed)
CaptchaCooldownManager.ClearCooldown();
```

## Error Handling

- **Browser Opening Failures**: If the browser fails to open, the error is logged but doesn't prevent cooldown activation
- **Tray Alert Failures**: If tray alerts fail to display, the error is logged but doesn't affect cooldown functionality
- **Thread Safety**: All operations are designed to be thread-safe and handle cross-thread scenarios

## Logging

The system uses `System.Diagnostics.Debug.WriteLine()` for logging:
- Cooldown start/end events
- Captcha detection events
- Error conditions

## Configuration

Currently, the system uses these fixed settings:
- **Cooldown Duration**: 5 minutes
- **Captcha URL**: `https://www.ebay.com/splashui/captcha`
- **Error Pattern**: "Session ID not found in session page HTML"

These could be made configurable in future versions if needed.

## Testing

The implementation includes comprehensive unit tests:
- **Test Location**: `uBuyFirst.Tests/Restocker/Services/CaptchaCooldownManagerTests.cs`
- **Test Coverage**:
  - Captcha error pattern matching (case-sensitive and case-insensitive)
  - Cooldown state management and timing logic
  - Status message generation
  - Thread-safe operations
  - Edge cases (null/empty inputs, cooldown expiration)

## Future Enhancements

Potential improvements could include:
- Configurable cooldown duration
- Different cooldown periods for different error types
- Integration with user settings for notification preferences
- Metrics tracking for captcha frequency
- More sophisticated captcha URL detection from actual responses
