﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Search;
using uBuyFirst.Services;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.Services
{
    /// <summary>
    /// Tests to verify the corrected expansion behavior where:
    /// - Creating keywords expands the selected folder (not its parent path)
    /// - Creating child terms expands the selected keyword (not its folder path)
    /// </summary>
    [TestClass]
    public class CorrectedExpansionBehaviorTests
    {
        private Mock<TreeList> _mockTreeList;
        private QueryList _queryList;
        private Mock<Func<object, TreeListNode>> _mockFindNodeByDataRecord;
        private Mock<Action> _mockRefreshAllNodeStates;
        private Mock<Action<KeywordFolder>> _mockEnsureFolderPathExpanded;
        private FolderOperationsService _service;

        [TestInitialize]
        public void Setup()
        {
            _mockTreeList = new Mock<TreeList>();
            _queryList = new QueryList();
            _mockFindNodeByDataRecord = new Mock<Func<object, TreeListNode>>();
            _mockRefreshAllNodeStates = new Mock<Action>();
            _mockEnsureFolderPathExpanded = new Mock<Action<KeywordFolder>>();

            _service = new FolderOperationsService(
                _mockTreeList.Object,
                _queryList,
                _mockFindNodeByDataRecord.Object,
                _mockRefreshAllNodeStates.Object,
                _mockEnsureFolderPathExpanded.Object);
        }

        [TestMethod]
        public void UserScenario_SelectFolderCreateKeyword_ShouldExpandSelectedFolder()
        {
            // Arrange - User selects a visible folder (its parents must already be expanded)
            var selectedFolder = new KeywordFolder { Name = "Selected Folder" };
            var keyword = new Keyword2Find { Alias = "New Keyword", ParentFolder = selectedFolder };
            
            var mockFolderNode = new Mock<TreeListNode>();
            mockFolderNode.SetupProperty(n => n.Expanded, false); // Folder is collapsed
            _mockFindNodeByDataRecord.Setup(f => f(selectedFolder)).Returns(mockFolderNode.Object);

            // Act - Create keyword in the selected folder
            _service.EnsureItemVisible(keyword);

            // Assert - The selected folder should be expanded to show the new keyword
            Assert.IsTrue(mockFolderNode.Object.Expanded, "Selected folder should be expanded to show new keyword");
            _mockFindNodeByDataRecord.Verify(f => f(selectedFolder), Times.Once, "Should find the selected folder node");
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never, "Should not expand parent paths");
        }

        [TestMethod]
        public void UserScenario_SelectKeywordCreateChildTerm_ShouldExpandSelectedKeyword()
        {
            // Arrange - User selects a visible keyword (its folder must already be expanded)
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var selectedKeyword = new Keyword2Find { Alias = "Selected Keyword", ParentFolder = parentFolder };
            var childTerm = new ChildTerm(selectedKeyword, "New Child Term");
            
            var mockKeywordNode = new Mock<TreeListNode>();
            mockKeywordNode.SetupProperty(n => n.Expanded, false); // Keyword is collapsed
            _mockFindNodeByDataRecord.Setup(f => f(selectedKeyword)).Returns(mockKeywordNode.Object);

            // Act - Create child term under the selected keyword
            _service.EnsureItemVisible(childTerm);

            // Assert - The selected keyword should be expanded to show the new child term
            Assert.IsTrue(mockKeywordNode.Object.Expanded, "Selected keyword should be expanded to show new child term");
            _mockFindNodeByDataRecord.Verify(f => f(selectedKeyword), Times.Once, "Should find the selected keyword node");
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never, "Should not expand folder paths");
        }

        [TestMethod]
        public void UserScenario_CreateFolderInFolder_ShouldExpandParentPath()
        {
            // Arrange - Creating a folder still needs parent path expansion (existing behavior)
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var newFolder = new KeywordFolder { Name = "New Folder", ParentFolder = parentFolder };

            // Act - Create folder (this uses the existing folder creation logic)
            _service.EnsureItemVisible(newFolder);

            // Assert - Should use the existing parent path expansion logic
            _mockEnsureFolderPathExpanded.Verify(e => e(newFolder), Times.Once, "Should expand parent path for folder creation");
            _mockFindNodeByDataRecord.Verify(f => f(It.IsAny<object>()), Times.Never, "Should not use direct node expansion for folders");
        }

        [TestMethod]
        public void EdgeCase_KeywordWithoutFolder_ShouldNotExpand()
        {
            // Arrange - Keyword without parent folder (root level keyword)
            var keyword = new Keyword2Find { Alias = "Root Keyword", ParentFolder = null };

            // Act
            _service.EnsureItemVisible(keyword);

            // Assert - Should not attempt any expansion
            _mockFindNodeByDataRecord.Verify(f => f(It.IsAny<object>()), Times.Never);
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EdgeCase_ChildTermWithoutParent_ShouldNotExpand()
        {
            // Arrange - Child term without parent keyword
            var childTerm = new ChildTerm(null, "Orphan Child Term");

            // Act
            _service.EnsureItemVisible(childTerm);

            // Assert - Should not attempt any expansion
            _mockFindNodeByDataRecord.Verify(f => f(It.IsAny<object>()), Times.Never);
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EdgeCase_NodeNotFound_ShouldNotCrash()
        {
            // Arrange - Scenario where node cannot be found
            var folder = new KeywordFolder { Name = "Missing Folder" };
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = folder };
            
            _mockFindNodeByDataRecord.Setup(f => f(folder)).Returns((TreeListNode)null);

            // Act - Should not crash when node is not found
            _service.EnsureItemVisible(keyword);

            // Assert - Should attempt to find the node but handle null gracefully
            _mockFindNodeByDataRecord.Verify(f => f(folder), Times.Once);
        }

        [TestMethod]
        public void Performance_AlreadyExpandedNode_ShouldNotModify()
        {
            // Arrange - Node is already expanded
            var folder = new KeywordFolder { Name = "Already Expanded Folder" };
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = folder };
            
            var mockFolderNode = new Mock<TreeListNode>();
            mockFolderNode.SetupProperty(n => n.Expanded, true); // Already expanded
            _mockFindNodeByDataRecord.Setup(f => f(folder)).Returns(mockFolderNode.Object);

            // Act
            _service.EnsureItemVisible(keyword);

            // Assert - Should check expansion state but not modify it
            _mockFindNodeByDataRecord.Verify(f => f(folder), Times.Once);
            Assert.IsTrue(mockFolderNode.Object.Expanded, "Node should remain expanded");
            // The Expanded property should not be set again since it's already true
        }
    }
}
