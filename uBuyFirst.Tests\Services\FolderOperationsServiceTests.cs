using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Search;
using uBuyFirst.Services;
using uBuyFirst.SubSearch;

namespace uBuyFirst.Tests.Services
{
    /// <summary>
    /// Tests for the FolderOperationsService to ensure proper folder creation logic
    /// </summary>
    [TestClass]
    public class FolderOperationsServiceTests
    {
        private Mock<TreeList> _mockTreeList;
        private QueryList _queryList;
        private Mock<Func<object, TreeListNode>> _mockFindNodeByDataRecord;
        private Mock<Action> _mockRefreshAllNodeStates;
        private Mock<Action<KeywordFolder>> _mockEnsureFolderPathExpanded;
        private FolderOperationsService _service;

        [TestInitialize]
        public void Setup()
        {
            _mockTreeList = new Mock<TreeList>();
            _queryList = new QueryList();
            _mockFindNodeByDataRecord = new Mock<Func<object, TreeListNode>>();
            _mockRefreshAllNodeStates = new Mock<Action>();
            _mockEnsureFolderPathExpanded = new Mock<Action<KeywordFolder>>();

            _service = new FolderOperationsService(
                _mockTreeList.Object,
                _queryList,
                _mockFindNodeByDataRecord.Object,
                _mockRefreshAllNodeStates.Object,
                _mockEnsureFolderPathExpanded.Object);
        }

        [TestMethod]
        public void CreateRootFolder_ShouldCreateFolderAtRootLevel()
        {
            // Arrange
            var mockNode = new Mock<TreeListNode>();
            _mockFindNodeByDataRecord.Setup(f => f(It.IsAny<KeywordFolder>())).Returns(mockNode.Object);

            // Act
            var result = _service.CreateRootFolder();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNull(result.ParentFolder);
            Assert.AreEqual("New Folder", result.Name);
            Assert.AreEqual(1, _queryList.Folders.Count);
            Assert.AreEqual(result, _queryList.Folders[0]);

            // Verify service calls
            _mockRefreshAllNodeStates.Verify(r => r(), Times.Once);
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never); // Root folders don't need expansion
        }

        [TestMethod]
        public void CreateRootFolder_ShouldGenerateUniqueName_WhenDuplicateExists()
        {
            // Arrange
            var existingFolder = new KeywordFolder { Name = "New Folder" };
            _queryList.Folders.Add(existingFolder);

            var mockNode = new Mock<TreeListNode>();
            _mockFindNodeByDataRecord.Setup(f => f(It.IsAny<KeywordFolder>())).Returns(mockNode.Object);

            // Act
            var result = _service.CreateRootFolder();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("New Folder (2)", result.Name);
            Assert.AreEqual(2, _queryList.Folders.Count);
        }

        [TestMethod]
        public void CreateSubFolder_WithParentFolder_ShouldCreateUnderParent()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            _queryList.Folders.Add(parentFolder);

            var mockFocusedNode = new Mock<TreeListNode>();
            _mockTreeList.Setup(t => t.FocusedNode).Returns(mockFocusedNode.Object);
            _mockTreeList.Setup(t => t.GetDataRecordByNode(mockFocusedNode.Object)).Returns(parentFolder);

            var mockNewNode = new Mock<TreeListNode>();
            _mockFindNodeByDataRecord.Setup(f => f(It.IsAny<KeywordFolder>())).Returns(mockNewNode.Object);

            // Act
            var result = _service.CreateSubFolder();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(parentFolder, result.ParentFolder);
            Assert.AreEqual("New Folder", result.Name);
            Assert.AreEqual(1, parentFolder.Children.Count);
            Assert.AreEqual(result, parentFolder.Children[0]);

            // Verify expansion was called for subfolder
            _mockEnsureFolderPathExpanded.Verify(e => e(result), Times.Once);
        }

        [TestMethod]
        public void CreateSameLevelFolder_WithKeywordSelected_ShouldCreateAtSameLevel()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = parentFolder };
            parentFolder.Keywords.Add(keyword);
            _queryList.Folders.Add(parentFolder);

            var mockFocusedNode = new Mock<TreeListNode>();
            _mockTreeList.Setup(t => t.FocusedNode).Returns(mockFocusedNode.Object);
            _mockTreeList.Setup(t => t.GetDataRecordByNode(mockFocusedNode.Object)).Returns(keyword);

            var mockNewNode = new Mock<TreeListNode>();
            _mockFindNodeByDataRecord.Setup(f => f(It.IsAny<KeywordFolder>())).Returns(mockNewNode.Object);

            // Act
            var result = _service.CreateSameLevelFolder();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(parentFolder, result.ParentFolder);
            Assert.AreEqual("New Folder", result.Name);
            Assert.AreEqual(1, parentFolder.Children.Count);
            Assert.AreEqual(result, parentFolder.Children[0]);

            // Verify expansion was called
            _mockEnsureFolderPathExpanded.Verify(e => e(result), Times.Once);
        }

        [TestMethod]
        public void Constructor_WithNullParameters_ShouldThrowArgumentNullException()
        {
            // Test each parameter for null
            Assert.ThrowsException<ArgumentNullException>(() =>
                new FolderOperationsService(null, _queryList, _mockFindNodeByDataRecord.Object, _mockRefreshAllNodeStates.Object, _mockEnsureFolderPathExpanded.Object));

            Assert.ThrowsException<ArgumentNullException>(() =>
                new FolderOperationsService(_mockTreeList.Object, null, _mockFindNodeByDataRecord.Object, _mockRefreshAllNodeStates.Object, _mockEnsureFolderPathExpanded.Object));

            Assert.ThrowsException<ArgumentNullException>(() =>
                new FolderOperationsService(_mockTreeList.Object, _queryList, null, _mockRefreshAllNodeStates.Object, _mockEnsureFolderPathExpanded.Object));

            Assert.ThrowsException<ArgumentNullException>(() =>
                new FolderOperationsService(_mockTreeList.Object, _queryList, _mockFindNodeByDataRecord.Object, null, _mockEnsureFolderPathExpanded.Object));

            Assert.ThrowsException<ArgumentNullException>(() =>
                new FolderOperationsService(_mockTreeList.Object, _queryList, _mockFindNodeByDataRecord.Object, _mockRefreshAllNodeStates.Object, null));
        }

        [TestMethod]
        public void AllCreateMethods_ShouldCallRefreshAllNodeStates()
        {
            // Arrange
            var mockNode = new Mock<TreeListNode>();
            _mockFindNodeByDataRecord.Setup(f => f(It.IsAny<KeywordFolder>())).Returns(mockNode.Object);

            // Act & Assert for each method
            _service.CreateRootFolder();
            _mockRefreshAllNodeStates.Verify(r => r(), Times.Once);

            _mockRefreshAllNodeStates.Reset();
            _service.CreateSubFolder();
            _mockRefreshAllNodeStates.Verify(r => r(), Times.Once);

            _mockRefreshAllNodeStates.Reset();
            _service.CreateSameLevelFolder();
            _mockRefreshAllNodeStates.Verify(r => r(), Times.Once);
        }

        [TestMethod]
        public void EnsureItemVisible_WithKeywordFolder_ShouldCallEnsureFolderPathExpanded()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Test Folder" };

            // Act
            _service.EnsureItemVisible(folder);

            // Assert
            _mockEnsureFolderPathExpanded.Verify(e => e(folder), Times.Once);
        }

        [TestMethod]
        public void EnsureItemVisible_WithKeywordInFolder_ShouldExpandParentFolder()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = parentFolder };
            var mockFolderNode = new Mock<TreeListNode>();
            mockFolderNode.SetupProperty(n => n.Expanded, false);

            _mockFindNodeByDataRecord.Setup(f => f(parentFolder)).Returns(mockFolderNode.Object);

            // Act
            _service.EnsureItemVisible(keyword);

            // Assert
            _mockFindNodeByDataRecord.Verify(f => f(parentFolder), Times.Once);
            Assert.IsTrue(mockFolderNode.Object.Expanded, "Parent folder node should be expanded");
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EnsureItemVisible_WithKeywordWithoutFolder_ShouldNotCallExpansion()
        {
            // Arrange
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = null };

            // Act
            _service.EnsureItemVisible(keyword);

            // Assert
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EnsureItemVisible_WithChildTerm_ShouldExpandParentKeyword()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var parentKeyword = new Keyword2Find { Alias = "Parent Keyword", ParentFolder = parentFolder };
            var childTerm = new ChildTerm(parentKeyword, "Test Child Term");
            var mockKeywordNode = new Mock<TreeListNode>();
            mockKeywordNode.SetupProperty(n => n.Expanded, false);

            _mockFindNodeByDataRecord.Setup(f => f(parentKeyword)).Returns(mockKeywordNode.Object);

            // Act
            _service.EnsureItemVisible(childTerm);

            // Assert
            _mockFindNodeByDataRecord.Verify(f => f(parentKeyword), Times.Once);
            Assert.IsTrue(mockKeywordNode.Object.Expanded, "Parent keyword node should be expanded");
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EnsureItemVisible_WithChildTermWithoutParent_ShouldNotCallExpansion()
        {
            // Arrange - Create a child term with no parent keyword
            var childTerm = new ChildTerm(null, "Test Child Term");

            // Act
            _service.EnsureItemVisible(childTerm);

            // Assert
            _mockFindNodeByDataRecord.Verify(f => f(It.IsAny<object>()), Times.Never);
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }

        [TestMethod]
        public void EnsureItemVisible_WithKeywordInAlreadyExpandedFolder_ShouldNotChangeExpansion()
        {
            // Arrange
            var parentFolder = new KeywordFolder { Name = "Parent Folder" };
            var keyword = new Keyword2Find { Alias = "Test Keyword", ParentFolder = parentFolder };
            var mockFolderNode = new Mock<TreeListNode>();
            mockFolderNode.SetupProperty(n => n.Expanded, true); // Already expanded

            _mockFindNodeByDataRecord.Setup(f => f(parentFolder)).Returns(mockFolderNode.Object);

            // Act
            _service.EnsureItemVisible(keyword);

            // Assert
            _mockFindNodeByDataRecord.Verify(f => f(parentFolder), Times.Once);
            Assert.IsTrue(mockFolderNode.Object.Expanded, "Folder should remain expanded");
        }

        [TestMethod]
        public void EnsureItemVisible_WithUnknownItemType_ShouldNotCallExpansion()
        {
            // Arrange
            var unknownItem = "Unknown Item Type";

            // Act
            _service.EnsureItemVisible(unknownItem);

            // Assert
            _mockFindNodeByDataRecord.Verify(f => f(It.IsAny<object>()), Times.Never);
            _mockEnsureFolderPathExpanded.Verify(e => e(It.IsAny<KeywordFolder>()), Times.Never);
        }
    }
}
