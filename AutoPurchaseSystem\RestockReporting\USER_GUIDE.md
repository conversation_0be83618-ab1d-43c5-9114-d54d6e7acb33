# Restock Item History Logging - User Guide

## 🎯 **Overview**

The Restock Item History Logging system automatically captures detailed information about every item processed by your restock filters. This provides comprehensive historical data for analysis, troubleshooting, and optimization of your restock operations.

## 🚀 **Key Features**

### **Automatic Logging**
- **Every item processed** by restock filters is automatically logged
- **Complete context** including item details, keyword settings, filter rules, and purchase results
- **All outcomes** captured: successful purchases, filtered out items, errors, and skipped items
- **Real-time logging** that doesn't slow down your restock operations

### **Organized Storage**
- **Daily folders** organize files by date (e.g., `2024-12-19/`)
- **Individual JSON files** for each item processed
- **Permanent storage** - files are never automatically deleted
- **Error logging** for troubleshooting any issues

### **CSV Export**
- **Export any date range** to CSV format for analysis
- **All data included** - over 30 columns of detailed information
- **Excel compatible** for easy analysis and reporting
- **Large dataset support** - handles thousands of records

## 📁 **File Locations**

### **Default Storage Location**
```
[Application Folder]/Reports/ItemHistory/
├── 2024-12-19/
│   ├── Item_394857392_kw123_143022.json
│   ├── Item_394857393_kw124_143025.json
│   └── ...
├── 2024-12-20/
└── errors/
    ├── 2024-12-19_errors.log
    └── ...
```

### **File Naming Convention**
- **Format**: `Item_{ItemID}_{KeywordID}_{Time}.json`
- **Example**: `Item_394857392_kw123_143022.json`
- **Conflicts**: Automatic numbering (e.g., `_1.json`, `_2.json`)

## 📊 **Understanding the Data**

### **Outcome Types**
- **`purchased`** - Item was successfully purchased
- **`filtered_out`** - Item didn't match filter criteria
- **`quantity_fulfilled`** - Required quantity already reached, no purchase needed
- **`purchase_failed`** - Purchase attempt failed
- **`skipped`** - Purchase was skipped (e.g., purchasing disabled)
- **`no_action`** - No purchase attempt was made

### **Key Information Captured**
- **Item Details**: Title, price, condition, seller, shipping, etc.
- **Keyword Settings**: Required quantity, purchased quantity, price limits, etc.
- **Filter Rules**: Which filter matched, filter expression, evaluation result
- **Transaction Results**: Purchase success/failure, transaction ID, error messages

## 📈 **CSV Export Instructions**

### **How to Export Data**
1. **Access Export Function** (implementation depends on UI integration)
2. **Select Date Range** - Choose start and end dates
3. **Choose Output File** - Select where to save the CSV file
4. **Click Export** - System will process all JSON files in the date range

### **CSV Columns Included**
- **Timestamp, Outcome, Reason**
- **Item Data**: ItemId, Title, CurrentPrice, Condition, Seller, ShippingCost, Location, etc.
- **Keyword State**: KeywordId, Alias, Keywords, RequiredQuantity, PurchasedQuantity, PriceMin, PriceMax, etc.
- **Filter Rule**: FilterAlias, Expression, Matched, EvaluationResult
- **Transaction**: TransactionAttempted, TransactionSuccess, TransactionId, ErrorMessage, PurchasePrice, Quantity

### **Analyzing the Data**
- **Open in Excel** or any spreadsheet application
- **Filter by Outcome** to see only purchases, errors, etc.
- **Sort by Timestamp** to see chronological order
- **Group by Keyword** to analyze specific search terms
- **Calculate totals** for purchased quantities and amounts

## 🔧 **Configuration**

### **Enabling/Disabling Logging**
The logging system is enabled by default when restock filters are active. If you need to disable it:
- Contact support for configuration options
- Logging can be disabled without affecting restock functionality

### **Storage Management**
- **Disk Space**: Each item uses approximately 2-5KB of storage
- **Daily Volume**: ~3,000 items = ~6-15MB per day
- **Annual Storage**: Approximately 2-5GB per year
- **Cleanup**: Files are never automatically deleted - manual cleanup if needed

## 🚨 **Troubleshooting**

### **Common Issues**

#### **No JSON Files Created**
- Check if restock filters are enabled and running
- Verify write permissions to the Reports folder
- Check error logs in the `errors/` folder

#### **Missing Data in CSV Export**
- Ensure the date range includes the dates you want
- Check that JSON files exist for those dates
- Verify the export completed without errors

#### **Large Export Takes Long Time**
- Exporting thousands of records may take several minutes
- Consider smaller date ranges for faster exports
- Ensure sufficient disk space for the CSV file

### **Error Logs**
- **Location**: `Reports/ItemHistory/errors/`
- **Format**: `YYYY-MM-DD_errors.log`
- **Content**: Detailed error messages and stack traces
- **Action**: Review errors and contact support if persistent issues

## 📞 **Support**

### **Getting Help**
- **Error Messages**: Check the daily error logs first
- **Missing Data**: Verify restock filters are running and processing items
- **Performance Issues**: Monitor disk space and system resources
- **Feature Requests**: Contact development team for enhancements

### **Best Practices**
- **Regular Monitoring**: Check error logs weekly
- **Disk Space**: Monitor storage usage monthly
- **Data Analysis**: Export and analyze data regularly to optimize restock settings
- **Backup**: Consider backing up historical data periodically

---

**This system provides comprehensive visibility into your restock operations, enabling data-driven optimization and troubleshooting.**
