using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Manages validation warnings for TreeList cells, providing non-blocking validation feedback
    /// </summary>
    public class TreeListValidationWarningManager
    {
        private readonly Dictionary<string, Dictionary<string, string>> _warnings;

        public TreeListValidationWarningManager()
        {
            _warnings = new Dictionary<string, Dictionary<string, string>>();
        }

        /// <summary>
        /// Sets a warning message for a specific node and column
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <param name="columnName">Column field name</param>
        /// <param name="warningMessage">Warning message to display</param>
        public void SetWarning(string nodeId, string columnName, string warningMessage)
        {
            if (!_warnings.ContainsKey(nodeId))
            {
                _warnings[nodeId] = new Dictionary<string, string>();
            }

            _warnings[nodeId][columnName] = warningMessage;
        }

        /// <summary>
        /// Clears a specific warning for a node and column
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <param name="columnName">Column field name</param>
        public void ClearWarning(string nodeId, string columnName)
        {
            if (_warnings.ContainsKey(nodeId))
            {
                _warnings[nodeId].Remove(columnName);
                if (_warnings[nodeId].Count == 0)
                {
                    _warnings.Remove(nodeId);
                }
            }
        }

        /// <summary>
        /// Clears all warnings for a specific node
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        public void ClearNodeWarnings(string nodeId)
        {
            _warnings.Remove(nodeId);
        }

        /// <summary>
        /// Clears all warnings
        /// </summary>
        public void ClearAllWarnings()
        {
            _warnings.Clear();
        }

        /// <summary>
        /// Gets the warning message for a specific node and column
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <param name="columnName">Column field name</param>
        /// <returns>Warning message or null if no warning exists</returns>
        public string GetWarning(string nodeId, string columnName)
        {
            if (_warnings.ContainsKey(nodeId) && _warnings[nodeId].ContainsKey(columnName))
            {
                return _warnings[nodeId][columnName];
            }
            return null;
        }

        /// <summary>
        /// Checks if a specific node and column has a warning
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <param name="columnName">Column field name</param>
        /// <returns>True if warning exists</returns>
        public bool HasWarning(string nodeId, string columnName)
        {
            return _warnings.ContainsKey(nodeId) && _warnings[nodeId].ContainsKey(columnName);
        }

        /// <summary>
        /// Checks if any warnings exist in the system
        /// </summary>
        /// <returns>True if any warnings exist</returns>
        public bool HasAnyWarnings()
        {
            return _warnings.Any(kvp => kvp.Value.Any());
        }

        /// <summary>
        /// Gets all warnings for a specific node
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <returns>Dictionary of column names and warning messages</returns>
        public Dictionary<string, string> GetNodeWarnings(string nodeId)
        {
            if (_warnings.ContainsKey(nodeId))
            {
                return new Dictionary<string, string>(_warnings[nodeId]);
            }
            return new Dictionary<string, string>();
        }

        /// <summary>
        /// Gets the total count of warnings across all nodes
        /// </summary>
        /// <returns>Total warning count</returns>
        public int GetWarningCount()
        {
            return _warnings.Sum(kvp => kvp.Value.Count);
        }

        /// <summary>
        /// Sets warnings from a ValidationResult for a specific node
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <param name="validationResult">Validation result containing warnings</param>
        public void SetWarningsFromResult(string nodeId, ValidationResult validationResult)
        {
            if (validationResult == null || string.IsNullOrEmpty(nodeId))
                return;

            // Clear existing warnings for this node first
            ClearNodeWarnings(nodeId);

            // Add new warnings from the validation result
            foreach (var warning in validationResult.Warnings)
            {
                SetWarning(nodeId, warning.ColumnName, warning.Message);
            }
        }

        /// <summary>
        /// Gets all warnings as a ValidationResult for a specific node
        /// </summary>
        /// <param name="nodeId">Unique identifier for the node</param>
        /// <returns>ValidationResult containing all warnings for the node</returns>
        public ValidationResult GetWarningsAsResult(string nodeId)
        {
            var result = new ValidationResult();
            
            if (_warnings.ContainsKey(nodeId))
            {
                foreach (var kvp in _warnings[nodeId])
                {
                    result.AddWarning(kvp.Key, kvp.Value);
                }
            }

            return result;
        }
    }
}
