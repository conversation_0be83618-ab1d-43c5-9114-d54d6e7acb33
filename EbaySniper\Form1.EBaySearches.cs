﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Filters;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.SearchTerms;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class Form1
    {
        #region eBay Search Panel

        private void SetTreeListCheckboxesState()
        {
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                for (var j = 0; j < treeList1.Nodes[i].Nodes.Count; j++)
                {
                    SetNodeChecked(treeList1.Nodes[i].Nodes[j]);
                }
            }
        }

        private void SetNodeChecked(TreeListNode node)
        {
            try
            {
                var dataRecord = treeList1.GetDataRecordByNode(node);
                switch (dataRecord)
                {
                    case Keyword2Find kw:
                        node.CheckState = kw.KeywordEnabled;
                        break;
                    case ChildTerm childTerm:
                        node.Checked = childTerm.Enabled;
                        break;
                    case Search.KeywordFolder folder:
                        // Set folder check state based on its keywords and child folders
                        node.CheckState = CalculateFolderCheckState(folder);
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log error for debugging but don't show user messageboxes
                System.Diagnostics.Debug.WriteLine($"SetNodeChecked Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculates the check state for a folder based on ALL descendants (keywords and child terms) recursively
        /// This implements the user's requested behavior: parent folder disabled only when ALL children recursively are deselected
        /// Only folders can have indeterminate state
        /// </summary>
        private CheckState CalculateFolderCheckState(Search.KeywordFolder folder)
        {
            // Use the new recursive logic from the KeywordFolder class
            return folder.CalculateRecursiveFolderCheckState();
        }

        private void btnRemoveSearch_Click(object sender, EventArgs e)
        {
            RemoveSearchItem();
        }

        private void RemoveSearchItem()
        {
            if (treeList1.Selection.Count == 0)
                return;

            // Check if any selected items are folders - if so, use enhanced folder deletion logic
            var selectedFolders = treeList1.Selection
                .Where(node => treeList1.GetDataRecordByNode(node) is Search.KeywordFolder)
                .ToList();

            if (selectedFolders.Any())
            {
                // Handle folder deletion with enhanced confirmation showing all child items
                HandleFolderDeletion(selectedFolders);
                return;
            }

            // Handle non-folder items with existing logic
            HandleNonFolderDeletion();
        }

        private void HandleFolderDeletion(List<TreeListNode> folderNodes)
        {
            if (folderNodes.Count == 1)
            {
                // Single folder - show detailed contents
                var folder = (Search.KeywordFolder)treeList1.GetDataRecordByNode(folderNodes[0]);
                var allItemsToDelete = GetAllItemsInFolder(folder);
                var confirmationMessage = BuildFolderDeletionConfirmationMessage(folder, allItemsToDelete);

                var result = XtraMessageBox.Show(defaultLookAndFeel1.LookAndFeel, this, confirmationMessage, "Delete Folder", MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);

                if (result == DialogResult.Yes)
                {
                    RemoveFolderFromStructure(folder);
                    treeList1.DeleteSelectedNodes();
                    treeList1.RefreshDataSource(); // Refresh to update folder counts
                    SaveSettings();
                }
            }
            else
            {
                // Multiple folders - show summary
                var totalItemCount = 0;
                var folderNames = new List<string>();

                foreach (var node in folderNodes)
                {
                    var folder = (Search.KeywordFolder)treeList1.GetDataRecordByNode(node);
                    var allItems = GetAllItemsInFolder(folder);
                    totalItemCount += allItems.Count - 1; // Subtract 1 to exclude the folder itself from child count
                    folderNames.Add(folder.Name);
                }

                var confirmationMessage = $"Delete {folderNodes.Count} folders?\n\n";
                confirmationMessage += string.Join("\n", folderNames.Select(name => $"📁 {name}"));
                confirmationMessage += $"\n\nThis will delete {totalItemCount} total item(s) including all nested content.";

                var result = XtraMessageBox.Show(defaultLookAndFeel1.LookAndFeel, this, confirmationMessage, "Delete Folders", MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);

                if (result == DialogResult.Yes)
                {
                    foreach (var node in folderNodes)
                    {
                        var folder = (Search.KeywordFolder)treeList1.GetDataRecordByNode(node);
                        RemoveFolderFromStructure(folder);
                    }
                    treeList1.DeleteSelectedNodes();
                    treeList1.RefreshDataSource(); // Refresh to update folder counts
                    SaveSettings();
                }
            }
        }

        private void HandleNonFolderDeletion()
        {
            // Build confirmation message based on number of items
            string confirmationMessage;
            if (treeList1.Selection.Count <= 10)
            {
                // Show item names for 10 or fewer items
                var itemNames = new List<string>();
                foreach (var node in treeList1.Selection)
                {
                    var dataRecord = treeList1.GetDataRecordByNode(node);
                    string itemName = dataRecord switch
                    {
                        Keyword2Find keyword => $"Keyword: {keyword.Alias}",
                        ChildTerm childTerm => $"Sub Search: {childTerm.Alias}",
                        _ => "Unknown item"
                    };
                    itemNames.Add(itemName);
                }

                confirmationMessage = $"Remove the following {treeList1.Selection.Count} item(s)?\n\n" +
                                    string.Join("\n", itemNames);
            }
            else
            {
                // Show count only for more than 10 items
                confirmationMessage = $"Remove {treeList1.Selection.Count} selected items?";
            }

            var result = XtraMessageBox.Show(defaultLookAndFeel1.LookAndFeel, this, confirmationMessage, "Confirm Deletion", MessageBoxButtons.YesNo,
                MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);

            if (result == DialogResult.Yes)
            {
                // First, remove the data objects from the underlying structure
                var selectedNodes = treeList1.Selection.ToArray(); // Create a copy to avoid collection modification

                foreach (var node in selectedNodes)
                {
                    var dataRecord = treeList1.GetDataRecordByNode(node);

                    switch (dataRecord)
                    {
                        case Keyword2Find keyword:
                            // Remove keyword from its parent folder
                            if (keyword.ParentFolder != null)
                            {
                                keyword.ParentFolder.Keywords.Remove(keyword);
                            }
                            break;

                        case ChildTerm childTerm:
                            // Remove child term from its parent keyword
                            var parentKeyword = childTerm.GetParent();
                            if (parentKeyword != null)
                            {
                                parentKeyword.ChildrenCore.Remove(childTerm);
                            }
                            break;
                    }
                }

                // Clear selection to prevent any conflicts during refresh
                treeList1.Selection.Clear();

                // Refresh the TreeList to reflect the data changes
                // Note: Removed redundant DeleteSelectedNodes() call as RefreshDataSource() handles visual updates
                treeList1.RefreshDataSource(); // Refresh to update folder counts
                SaveSettings();
            }
        }

        private void RemoveFolderFromStructure(Search.KeywordFolder folder)
        {
            // Remove folder from its parent or root
            if (folder.ParentFolder != null)
            {
                folder.ParentFolder.Children.Remove(folder);
            }
            else
            {
                _ebaySearches.Folders.Remove(folder);
            }
        }

        /// <summary>
        /// Gets all items (folders, keywords, and child terms) that would be deleted when deleting a folder
        /// </summary>
        private List<object> GetAllItemsInFolder(Search.KeywordFolder folder)
        {
            var allItems = new List<object>();

            // Add the folder itself
            allItems.Add(folder);

            // Add all child folders recursively
            foreach (var childFolder in folder.Children)
            {
                allItems.AddRange(GetAllItemsInFolder(childFolder));
            }

            // Add all keywords in this folder
            foreach (var keyword in folder.Keywords)
            {
                allItems.Add(keyword);

                // Add all child terms of each keyword
                foreach (var childTerm in keyword.ChildrenCore)
                {
                    allItems.Add(childTerm);
                }
            }

            return allItems;
        }

        /// <summary>
        /// Builds a confirmation message for folder deletion showing up to 15 child items
        /// </summary>
        private string BuildFolderDeletionConfirmationMessage(Search.KeywordFolder folder, List<object> allItems)
        {
            var message = $"Are you sure you want to delete the folder '{folder.Name}'?\n\n";

            // Remove the folder itself from the count since we're showing what's inside it
            var childItems = allItems.Where(item => item != folder).ToList();

            if (childItems.Count == 0)
            {
                message += "This folder is empty.";
            }
            else
            {
                message += $"This will delete {childItems.Count} item(s):\n\n";

                // Show up to 15 items
                var itemsToShow = childItems.Take(15).ToList();

                foreach (var item in itemsToShow)
                {
                    string itemDescription = item switch
                    {
                        Search.KeywordFolder childFolder => $"📁 Folder: {childFolder.Name}",
                        Keyword2Find keyword => $"🔍 Keyword: {keyword.Alias}",
                        ChildTerm childTerm => $"   ↳ Sub Search: {childTerm.Alias}",
                        _ => $"❓ Unknown item: {item.GetType().Name}"
                    };
                    message += itemDescription + "\n";
                }

                // Show count of additional items if there are more than 15
                if (childItems.Count > 15)
                {
                    var additionalCount = childItems.Count - 15;
                    message += $"\n... and {additionalCount} more item(s)";
                }
            }

            return message;
        }

        private void btnNewChildTerm_Click(object sender, EventArgs e)
        {
            NewChildTerm();
        }

        /// <summary>
        /// Sets up parent relationships for a new keyword, preventing duplication
        /// </summary>
        /// <param name="keyword">The keyword to set up</param>
        /// <param name="targetFolder">The folder to place the keyword in (null for default folder)</param>
        private void SetupNewKeywordParentRelationships(Keyword2Find keyword, Search.KeywordFolder targetFolder = null)
        {
            // IMPORTANT: Set ParentFolder BEFORE ParentCore to prevent duplicate addition
            // The ParentCore setter automatically adds keywords to folders if ParentFolder is null
            if (targetFolder != null)
            {
                keyword.ParentFolder = targetFolder;
                if (!targetFolder.Keywords.Contains(keyword))
                {
                    targetFolder.Keywords.Add(keyword);
                }
            }

            keyword.ParentCore = _ebaySearches;
        }

        /// <summary>
        /// Common post-creation setup for new keywords (refresh, focus, edit)
        /// </summary>
        /// <param name="keyword">The newly created keyword</param>
        /// <param name="editColumn">The column to start editing (default: "Alias")</param>
        private void SetupNewKeywordUI(Keyword2Find keyword, string editColumn = "Alias")
        {
            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
            }

            // Ensure the new keyword is visible by expanding its parent folder path
            _folderOperationsService.EnsureItemVisible(keyword);

            UserSettings.CanShowEbaySearchEditor = true;

            while (treeList1.Selection.Count > 0)
            {
                treeList1.Selection.First().Selected = false;
            }

            // Focus the newly created keyword and start editing
            var newNode = FindNodeByDataRecord(keyword);
            if (newNode != null)
            {
                treeList1.FocusedNode = newNode;
                treeList1.FocusedColumn = treeList1.Columns[editColumn];
                treeList1.ShowEditor();
            }
        }

        /// <summary>
        /// Common post-creation setup for new child terms (refresh, expand parent, focus, edit)
        /// </summary>
        /// <param name="childTerm">The newly created child term</param>
        /// <param name="editColumn">The column to start editing (default: "Alias")</param>
        private void SetupNewChildTermUI(ChildTerm childTerm, string editColumn = "Alias")
        {
            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
            }

            // Ensure the new child term is visible by expanding its parent folder path
            _folderOperationsService.EnsureItemVisible(childTerm);

            UserSettings.CanShowEbaySearchEditor = true;

            while (treeList1.Selection.Count > 0)
            {
                treeList1.Selection.First().Selected = false;
            }

            // Expand the parent keyword node
            var parentKeyword = childTerm.GetParent();
            if (parentKeyword != null)
            {
                var parentNode = FindNodeByDataRecord(parentKeyword);
                if (parentNode != null)
                {
                    parentNode.Expanded = true;
                }
            }

            // Focus the newly created child term and start editing
            var newNode = FindNodeByDataRecord(childTerm);
            if (newNode != null)
            {
                treeList1.FocusedNode = newNode;
                treeList1.FocusedColumn = treeList1.Columns[editColumn];
                treeList1.ShowEditor();
            }
        }

        private void NewChildTerm()
        {
            if (treeList1.FocusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(treeList1.FocusedNode);
                Keyword2Find? parentSearchTerm = null;

                // Use type detection instead of node level
                if (dataRecord is Keyword2Find keyword)
                {
                    // If focused on a keyword, use it as the parent
                    parentSearchTerm = keyword;
                }
                else if (dataRecord is ChildTerm childTerm)
                {
                    // If focused on a child term, use its parent as the parent for the new child term
                    parentSearchTerm = childTerm.GetParent();
                }

                if (parentSearchTerm != null)
                {
                    if (parentSearchTerm.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                    {
                        MessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                            LicenseUtility.CurrentLimits.SearchTermsCount));

                        return;
                    }

                    var term = new ChildTerm(parentSearchTerm, "New Sub Search")
                    {
                        PriceMin = parentSearchTerm.PriceMin,
                        PriceMax = parentSearchTerm.PriceMax
                    };

                    term.SubSearch = new XFilterClassChild
                    {
                        Action = "Keep rows",
                        Alias = term.Alias,
                        Enabled = false
                    };

                    // Set up UI for the new child term (refresh, expand parent, focus, edit)
                    SetupNewChildTermUI(term, "Alias");
                }
            }
        }

        private void NewEbaySearch(string alias, string keyword = "", string categoryId = "")
        {
            if (_ebaySearches.ChildrenCore.Count >= LicenseUtility?.CurrentLimits.SearchTermsCount)
            {
                XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                    LicenseUtility.CurrentLimits.SearchTermsCount));

                return;
            }

            var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
            alias = Helpers.MakeUniqAliasOnAdd(alias, uniqueAliasesDict);
            var newKw = new Keyword2Find
            {
                Alias = alias,
                Categories4Api = categoryId,
                PriceMin = 1,
                PriceMax = 10000,
                SellerType = "",
                Frequency = new TimeSpan(),
                Threads = 1,
                ViewName = "Results"
            };
            if (!ResultsView.ViewsDict.ContainsKey("Results"))
                ResultsView.CreateView("Results");

            newKw.GridControl = ResultsView.ViewsDict["Results"];

            if (!string.IsNullOrEmpty(keyword))
            {
                newKw.Kws = keyword;
                newKw.LocatedIn = "US";
                newKw.AvailableTo = "US";
                newKw.Zip = "90210";
                newKw.KeywordEnabled = CheckState.Checked;
            }

            if (treeList1.GetDataRecordByNode(treeList1.Nodes.LastNode) is Keyword2Find lastQuery)
            {
                newKw.LocatedIn = lastQuery.LocatedIn;
                newKw.AvailableTo = lastQuery.AvailableTo;
                newKw.Zip = lastQuery.Zip;
            }

            // Determine the target folder based on the focused node (same pattern as CreateNewFolder)
            Search.KeywordFolder targetFolder = null;
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(focusedNode);
                if (dataRecord is Search.KeywordFolder folder)
                {
                    // If focused on a folder, place keyword in that folder
                    targetFolder = folder;
                }
                else if (dataRecord is Keyword2Find focusedKeyword)
                {
                    // If focused on a keyword, place new keyword in the same folder as the focused keyword
                    targetFolder = focusedKeyword.ParentFolder;
                }
            }

            // Set up parent relationships and UI
            SetupNewKeywordParentRelationships(newKw, targetFolder);
            SetupNewKeywordUI(newKw, "Alias");

            var lastId = 0;
            TreeListNode lastNode = null;
            foreach (TreeListNode node in treeList1.Nodes)
            {
                if (node.Id > lastId)
                {
                    lastId = node.Id;
                    lastNode = node;
                }
            }

            treeList1.FocusedNode = lastNode;
            if (lastNode != null)
                treeList1.SelectNode(lastNode);
            // treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cAlias");
            //   treeList1.ShowEditor();

            treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cKeywords");
            treeList1.ShowEditor();
            // treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cAlias");
            //  treeList1.ShowEditor();
        }

        private void NewNodeCopy()
        {
            if (treeList1.FocusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(treeList1.FocusedNode);

                // Handle keyword duplication
                if (dataRecord is Keyword2Find kw)
                {
                    if (_ebaySearches.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                    {
                        XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                            LicenseUtility.CurrentLimits.SearchTermsCount));

                        return;
                    }

                    // Use EXACTLY the same pattern as NewEbaySearch
                    var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
                    var duplicateAlias = Helpers.MakeUniqAliasOnAdd(kw.Alias, uniqueAliasesDict);

                    var kw2 = new Keyword2Find
                    {
                        Alias = duplicateAlias,
                        Categories4Api = kw.Categories4Api,
                        PriceMin = kw.PriceMin,
                        PriceMax = kw.PriceMax,
                        SellerType = kw.SellerType,
                        Frequency = kw.Frequency,
                        Threads = kw.Threads,
                        ViewName = kw.ViewName
                    };

                    // Ensure the view exists (same as NewEbaySearch)
                    if (!ResultsView.ViewsDict.ContainsKey(kw2.ViewName))
                        ResultsView.CreateView(kw2.ViewName);

                    kw2.GridControl = ResultsView.ViewsDict[kw2.ViewName];

                    // Copy all the other properties from original
                    kw2.Kws = kw.Kws;
                    kw2.LocatedIn = kw.LocatedIn;
                    kw2.AvailableTo = kw.AvailableTo;
                    kw2.Zip = kw.Zip;
                    kw2.KeywordEnabled = kw.KeywordEnabled;
                    kw2.SearchInDescription = kw.SearchInDescription;
                    kw2.Condition = kw.Condition;
                    kw2.EBaySite = kw.EBaySite;
                    kw2.EbaySiteName = kw.EbaySiteName;
                    kw2.IgnorePostalCodeError = kw.IgnorePostalCodeError;
                    kw2.Sellers = kw.Sellers;
                    kw2.ListingType = kw.ListingType;
                    kw2.JobId = kw.JobId;
                    kw2.RequiredQuantity = kw.RequiredQuantity;
                    kw2.PurchasedQuantity = kw.PurchasedQuantity;

                    // Set up parent relationships and UI using helper methods
                    SetupNewKeywordParentRelationships(kw2, kw.ParentFolder);
                    SetupNewKeywordUI(kw2, "Alias");
                }
                // Handle child term duplication
                else if (dataRecord is ChildTerm termSource)
                {
                    var parentSource = termSource?.GetParent();
                    if (parentSource != null)
                    {
                        if (parentSource.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                        {
                            XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                                LicenseUtility.CurrentLimits.SearchTermsCount));

                            return;
                        }

                        var term = new ChildTerm(parentSource, termSource.Alias);
                        term.PriceMin = termSource.PriceMin;
                        term.PriceMax = termSource.PriceMax;
                        term.SubSearch = new XFilterClassChild
                        {
                            Action = "Keep rows",
                            Alias = termSource.SubSearch.Alias,
                            Enabled = termSource.SubSearch.Enabled,
                            FilterCriteria = termSource.SubSearch.FilterCriteria
                        };
                        term.Condition = termSource.Condition;
                        var uniqueAliasesDict = Helpers.CountStrings(parentSource.ChildrenCore.Select(subSearch => subSearch.Alias).ToList());
                        term.Alias = Helpers.MakeUniqAliasOnAdd(termSource.Alias, uniqueAliasesDict);

                        term.CategoryIDs = termSource.CategoryIDs;
                        term.Keywords = termSource.Keywords;

                        treeList1.RefreshDataSource();
                        for (var i = 0; i < treeList1.Nodes.Count; i++)
                        {
                            SetNodeChecked(treeList1.Nodes[i]);
                        }

                        // Ensure the new child term is visible by expanding its parent folder path
                        _folderOperationsService.EnsureItemVisible(term);

                        // Focus the newly created child term and start editing
                        var newNode = FindNodeByDataRecord(term);
                        if (newNode != null)
                        {
                            treeList1.FocusedNode = newNode;
                            treeList1.FocusedColumn = treeList1.Columns["Alias"];
                            treeList1.ShowEditor();
                        }
                    }
                }
            }
        }

        private void btnExportSearches_Click(object sender, EventArgs e)
        {
            SearchTermManager.ExportSearchesToFile(_ebaySearches.ChildrenCore);
        }

        private void btnImportKeywords_Click(object sender, EventArgs e)
        {
            var filePath = AskUserForFileLocation();
            ImportKeywordsFromFile(filePath);
        }

        public void ImportKeywordsFromFile(string fileLocation)
        {
            if (string.IsNullOrEmpty(fileLocation))
                return;

            treeList1.BeginUpdate();

            SearchTermManager.ImportSearchTermsFromFile(fileLocation, _ebaySearches);

            treeList1.EndUpdate();

            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                for (var j = 0; j < treeList1.Nodes[i].Nodes.Count; j++)
                {
                    SetNodeChecked(treeList1.Nodes[i].Nodes[j]);
                }
            }
            SaveSettings();
        }

        private string AskUserForFileLocation()
        {
            openFileDialog1.Filter = @"Comma separated values (*.csv)|*.csv";
            openFileDialog1.InitialDirectory = Folders.Logs;
            openFileDialog1.FileName = "";
            var fileLocation = "";
            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                fileLocation = openFileDialog1.FileName;
            }

            return fileLocation;
        }

        #endregion
    }
}
