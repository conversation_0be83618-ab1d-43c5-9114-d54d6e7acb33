using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Search;
using DevExpress.XtraTreeList;

namespace uBuyFirst.Tests.SearchTerms
{
    [TestClass]
    public class QueryListFolderTests
    {
        private QueryList _queryList;

        [TestInitialize]
        public void Setup()
        {
            _queryList = new QueryList();
        }

        [TestMethod]
        public void QueryList_Constructor_InitializesFoldersCollection()
        {
            // Assert
            Assert.IsNotNull(_queryList.Folders);
            Assert.AreEqual(0, _queryList.Folders.Count);
        }

        [TestMethod]
        public void QueryList_ChildrenCore_EmptyFolders_ReturnsEmptyList()
        {
            // Act
            var children = _queryList.ChildrenCore;

            // Assert
            Assert.IsNotNull(children);
            Assert.AreEqual(0, children.Count);
        }

        [TestMethod]
        public void QueryList_ChildrenCore_WithKeywordsInFolders_ReturnsAllKeywords()
        {
            // Arrange
            var folder1 = new KeywordFolder { Name = "Folder 1" };
            var folder2 = new KeywordFolder { Name = "Folder 2" };

            var keyword1 = new Keyword2Find { Alias = "Keyword 1", ParentFolder = folder1 };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2", ParentFolder = folder1 };
            var keyword3 = new Keyword2Find { Alias = "Keyword 3", ParentFolder = folder2 };

            folder1.Keywords.Add(keyword1);
            folder1.Keywords.Add(keyword2);
            folder2.Keywords.Add(keyword3);

            _queryList.Folders.Add(folder1);
            _queryList.Folders.Add(folder2);

            // Act
            var children = _queryList.ChildrenCore;

            // Assert
            Assert.AreEqual(3, children.Count);
            Assert.IsTrue(children.Contains(keyword1));
            Assert.IsTrue(children.Contains(keyword2));
            Assert.IsTrue(children.Contains(keyword3));
        }

        [TestMethod]
        public void QueryList_MigrateFromFlatStructure_EmptyList_DoesNothing()
        {
            // Arrange
            var emptyKeywords = new List<Keyword2Find>();

            // Act
            _queryList.MigrateFromFlatStructure(emptyKeywords);

            // Assert
            Assert.AreEqual(0, _queryList.Folders.Count);
        }

        [TestMethod]
        public void QueryList_MigrateFromFlatStructure_OrphanedKeywords_CreatesDefaultFolder()
        {
            // Arrange
            var keywords = new List<Keyword2Find>
            {
                new Keyword2Find { Alias = "Keyword 1" },
                new Keyword2Find { Alias = "Keyword 2" }
            };

            // Act
            _queryList.MigrateFromFlatStructure(keywords);

            // Assert
            Assert.AreEqual(1, _queryList.Folders.Count);
            var defaultFolder = _queryList.Folders[0];
            Assert.AreEqual("Default", defaultFolder.Name);
            Assert.AreEqual(2, defaultFolder.Keywords.Count);

            foreach (var keyword in keywords)
            {
                Assert.AreEqual(defaultFolder, keyword.ParentFolder);
                Assert.IsTrue(defaultFolder.Keywords.Contains(keyword));
            }
        }

        [TestMethod]
        public void QueryList_MigrateFromFlatStructure_KeywordsAlreadyInFolders_DoesNotMigrate()
        {
            // Arrange
            var existingFolder = new KeywordFolder { Name = "Existing Folder" };
            var keyword1 = new Keyword2Find { Alias = "Keyword 1", ParentFolder = existingFolder };
            var keyword2 = new Keyword2Find { Alias = "Keyword 2" }; // Orphaned

            existingFolder.Keywords.Add(keyword1);
            _queryList.Folders.Add(existingFolder);

            var keywords = new List<Keyword2Find> { keyword1, keyword2 };

            // Act
            _queryList.MigrateFromFlatStructure(keywords);

            // Assert
            Assert.AreEqual(2, _queryList.Folders.Count); // Existing + Default

            // keyword1 should stay in existing folder
            Assert.AreEqual(existingFolder, keyword1.ParentFolder);
            Assert.IsTrue(existingFolder.Keywords.Contains(keyword1));

            // keyword2 should be in default folder
            var defaultFolder = _queryList.Folders.FirstOrDefault(f => f.Name == "Default");
            Assert.IsNotNull(defaultFolder);
            Assert.AreEqual(defaultFolder, keyword2.ParentFolder);
            Assert.IsTrue(defaultFolder.Keywords.Contains(keyword2));
        }

        [TestMethod]
        public void QueryList_MigrateFromFlatStructure_DefaultFolderExists_UsesExisting()
        {
            // Arrange
            var existingDefaultFolder = new KeywordFolder { Name = "Default" };
            _queryList.Folders.Add(existingDefaultFolder);

            var keywords = new List<Keyword2Find>
            {
                new Keyword2Find { Alias = "Keyword 1" }
            };

            // Act
            _queryList.MigrateFromFlatStructure(keywords);

            // Assert
            Assert.AreEqual(1, _queryList.Folders.Count); // Should use existing default folder
            Assert.AreEqual(1, existingDefaultFolder.Keywords.Count);
            Assert.AreEqual(existingDefaultFolder, keywords[0].ParentFolder);
        }

        [TestMethod]
        public void QueryList_FindFolderByPath_EmptyPath_ReturnsNull()
        {
            // Act
            var result = _queryList.FindFolderByPath("");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void QueryList_FindFolderByPath_SingleLevel_ReturnsFolder()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Electronics" };
            _queryList.Folders.Add(folder);

            // Act
            var result = _queryList.FindFolderByPath("Electronics");

            // Assert
            Assert.AreEqual(folder, result);
        }

        [TestMethod]
        public void QueryList_FindFolderByPath_MultiLevel_ReturnsNestedFolder()
        {
            // Arrange
            var rootFolder = new KeywordFolder { Name = "Electronics" };
            var subFolder = new KeywordFolder { Name = "Mobile Phones", ParentFolder = rootFolder };
            rootFolder.Children.Add(subFolder);
            _queryList.Folders.Add(rootFolder);

            // Act
            var result = _queryList.FindFolderByPath("Electronics > Mobile Phones");

            // Assert
            Assert.AreEqual(subFolder, result);
        }

        [TestMethod]
        public void QueryList_FindFolderByPath_NonExistentPath_ReturnsNull()
        {
            // Arrange
            var folder = new KeywordFolder { Name = "Electronics" };
            _queryList.Folders.Add(folder);

            // Act
            var result = _queryList.FindFolderByPath("NonExistent > Path");

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void QueryList_VirtualTreeGetChildNodes_ReturnsFolders()
        {
            // Arrange
            var folder1 = new KeywordFolder { Name = "Folder 1" };
            var folder2 = new KeywordFolder { Name = "Folder 2" };
            _queryList.Folders.Add(folder1);
            _queryList.Folders.Add(folder2);

            var info = new VirtualTreeGetChildNodesInfo(_queryList);

            // Act
            _queryList.VirtualTreeGetChildNodes(info);

            // Assert
            Assert.IsNotNull(info.Children);
            var children = info.Children as List<KeywordFolder>;
            Assert.IsNotNull(children);
            Assert.AreEqual(2, children.Count);
            Assert.IsTrue(children.Contains(folder1));
            Assert.IsTrue(children.Contains(folder2));
        }
    }
}
