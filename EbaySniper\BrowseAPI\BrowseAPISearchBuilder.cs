﻿using System;
using System.Collections.Generic;
using System.Linq;
using uBuyFirst.Tools;

namespace uBuyFirst.BrowseAPI
{
    public enum BrowseAPISortOrder
    {
        Default,
        NewlyListed,
        EndingSoonest,
        Price
    }

    public class SearchTermBrowseAPI
    {
        public string Kws { get; set; }
        public bool SearchInDescription { get; set; }
        public double PriceMin { get; set; }
        public double PriceMax { get; set; }
        public string[] Condition { get; set; }
        public ListingType[] ListingType { get; set; }
        public string LocatedIn { get; set; }
        public string AvailableTo { get; set; }
        public string SellerType { get; set; }
        public string[] Sellers { get; set; }
        public string CategoryID { get; set; }
        public int ResultsLimit { get; set; }
    }

    public static class BrowseAPISearchBuilder
    {
        public static string CreateBrowseAPISearchRequestUrl(SearchTermBrowseAPI searchTerm, bool isEndingNow, BrowseAPISortOrder sortOrder = BrowseAPISortOrder.Default)
        {
            var paramFieldGroups = "fieldgroups=" + Uri.EscapeDataString("BUYING_OPTION_REFINEMENTS,CATEGORY_REFINEMENTS,CONDITION_REFINEMENTS,EXTENDED,MATCHING_ITEMS");

            var paramLimit = "limit=" + searchTerm.ResultsLimit;

            var paramKeywords = GetParamKeywords(searchTerm.Kws);

            var paramCategoryIDs = GetParamCategoryID(searchTerm.CategoryID);

            var paramFilters = GetFiltersUrlParam(searchTerm, isEndingNow);

            var paramList = new List<string>();

            paramList.Add(paramFieldGroups);
            paramList.Add(paramLimit);
            paramList.Add(paramFilters);

            if (!string.IsNullOrWhiteSpace(paramKeywords))
                paramList.Add(paramKeywords);

            if (!string.IsNullOrWhiteSpace(paramCategoryIDs))
                paramList.Add(paramCategoryIDs);

            var url = "item_summary/search?";
            url += string.Join("&", paramList);

            string paramSortOrder = GetSortOrderParam(isEndingNow, sortOrder);
            url += "&" + paramSortOrder;
            return url;
        }

        private static string GetSortOrderParam(bool isEndingNow, BrowseAPISortOrder sortOrder)
        {
            // Handle explicit sort order first
            switch (sortOrder)
            {
                case BrowseAPISortOrder.Price:
                    return "sort=price";
                case BrowseAPISortOrder.NewlyListed:
                    return "sort=newlyListed";
                case BrowseAPISortOrder.EndingSoonest:
                    return "sort=endingSoonest";
                case BrowseAPISortOrder.Default:
                default:
                    // Use legacy logic for default behavior
                    return isEndingNow ? "sort=endingSoonest" : "sort=newlyListed";
            }
        }

        private static string GetParamCategoryID(string categoryID)
        {
            var paramCategoryIDs = "";
            if (!string.IsNullOrWhiteSpace(categoryID.Trim()))
                paramCategoryIDs = "category_ids=" + Uri.EscapeDataString(categoryID);
            return paramCategoryIDs;
        }

        private static string GetParamKeywords(string keywords)
        {

            var paramKeywords = "";
            if (!string.IsNullOrWhiteSpace(keywords.Trim()))
            {
                keywords = KeywordHelpers.StripOuterParensSafely(keywords);
                paramKeywords = "q=" + Uri.EscapeDataString(keywords);
            }

            return paramKeywords;
        }

        private static string GetFiltersUrlParam(SearchTermBrowseAPI searchTerm, bool isEndingNow)
        {
            var filters = GetFilterList(searchTerm);
            var listingTypeList = new List<string>();
            if (!isEndingNow)
                if (searchTerm.ListingType.Contains(ListingType.BuyItNow))
                    listingTypeList.AddRange(new[] { "FIXED_PRICE" });

            if (searchTerm.ListingType.Contains(ListingType.AuctionsStartedNow))
                listingTypeList.AddRange(new[] { "AUCTION" });
            else if (searchTerm.ListingType.Contains(ListingType.AuctionsEndingNow) && isEndingNow)
                listingTypeList.AddRange(new[] { "AUCTION" });

            var filterBuyingOptions = "buyingOptions:{" + string.Join("|", listingTypeList) + "}";

            var filterItemEndDate = "";
            if (isEndingNow)
            {
                filterItemEndDate = $"itemEndDate:[..{DateTime.UtcNow.AddMinutes(15):yyyy-MM-dd\\THH:mm:ss.fff\\Z}]";
            }

            if (!string.IsNullOrWhiteSpace(filterItemEndDate))
                filters.Add(filterItemEndDate);

            if (!string.IsNullOrWhiteSpace(filterBuyingOptions))
                filters.Add(filterBuyingOptions);

            var filtersStr = string.Join(",", filters);
            var paramFilters = "filter=" + Uri.EscapeDataString(filtersStr);
            return paramFilters;
        }

        private static List<string> GetFilterList(SearchTermBrowseAPI searchTerm)
        {
            //Filters
            var filterSearchInDescription = "";
            if (searchTerm.SearchInDescription)
                filterSearchInDescription = "searchInDescription:" + searchTerm.SearchInDescription.ToString().ToLower();

            var filterPrice = $"priceCurrency:USD,price:[{searchTerm.PriceMin}..{searchTerm.PriceMax}]";

            var filterConditionIDs = "";

            var conditions = searchTerm.Condition.ToList();
            conditions.Remove("Unspecified");

            if (conditions.Count > 0)
            {
                if (conditions.Count != 1 || !string.IsNullOrEmpty(conditions[0]))
                    filterConditionIDs = $"conditionIds:{{{string.Join("|", conditions)}}}";
            }

            var filterSellers = "";
            if (searchTerm.Sellers.Any())
            {
                var validSellers = searchTerm.Sellers.Where(seller => !string.IsNullOrEmpty(seller)).ToList();
                if (validSellers.Count > 0 && !string.IsNullOrEmpty(searchTerm.SellerType))
                {
                    var sellerType = searchTerm.SellerType == "Include" ? "sellers" : "excludeSellers";
                    filterSellers = $"{sellerType}:{{{string.Join("|", validSellers)}}}";
                }
            }

            var filterItemLocationCountry = "";

            if (searchTerm.LocatedIn != "Any")
            {
                filterItemLocationCountry = "itemLocationCountry:" + searchTerm.LocatedIn;
            }

            var filterDeliveryCountry = "";
            if (searchTerm.AvailableTo != "Any")
            {
                filterDeliveryCountry = "deliveryCountry:" + searchTerm.AvailableTo;
            }

            var filters = new List<string>();

            if (!string.IsNullOrWhiteSpace(filterSearchInDescription))
                filters.Add(filterSearchInDescription);

            if (!string.IsNullOrWhiteSpace(filterPrice))
                filters.Add(filterPrice);

            if (!string.IsNullOrWhiteSpace(filterConditionIDs))
                filters.Add(filterConditionIDs);

            if (!string.IsNullOrWhiteSpace(filterSellers))
                filters.Add(filterSellers);

            if (!string.IsNullOrWhiteSpace(filterItemLocationCountry))
                filters.Add(filterItemLocationCountry);

            if (!string.IsNullOrWhiteSpace(filterDeliveryCountry))
                filters.Add(filterDeliveryCountry);
            return filters;
        }
    }
}
