﻿using System;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace uBuyFirst.PicToText
{
    public class PicToTextException : Exception
    {
        public int ErrorId { get; }

        public PicToTextException(int errorId, string message) : base(message)
        {
            ErrorId = errorId;
        }

        public PicToTextException(int errorId, string message, Exception innerException) : base(message, innerException)
        {
            ErrorId = errorId;
        }
    }

    public class CaptchaSolvingTimeoutException : TimeoutException
    {
        public int TaskId { get; }

        public CaptchaSolvingTimeoutException(int taskId, string message) : base(message)
        {
            TaskId = taskId;
        }

        public CaptchaSolvingTimeoutException(int taskId, string message, Exception innerException) : base(message, innerException)
        {
            TaskId = taskId;
        }
    }

    public class InvalidCaptchaImageException : ArgumentException
    {
        public InvalidCaptchaImageException(string message) : base(message)
        {
        }

        public InvalidCaptchaImageException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    public class PicToText
    {
        private readonly string _apiKey;
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "https://api.anti-captcha.com";

        public PicToText(string apiKey, TimeSpan? httpTimeout = null)
        {
            if (string.IsNullOrEmpty(apiKey))
                throw new ArgumentException("API key cannot be null or empty", nameof(apiKey));

            _apiKey = apiKey;
            _httpClient = new HttpClient();
            _httpClient.Timeout = httpTimeout ?? TimeSpan.FromMinutes(2); // Default 2 minute timeout for HTTP requests
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }

        private async Task<T> PostJsonWithRetryAsync<T>(string endpoint, object data, int maxRetries = 3, CancellationToken cancellationToken = default)
        {
            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await PostJsonAsync<T>(endpoint, data, cancellationToken);
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex) && !cancellationToken.IsCancellationRequested)
                {
                    int delayMs = (int)Math.Pow(2, attempt) * 1000; // Exponential backoff: 1s, 2s, 4s
                    await Task.Delay(delayMs, cancellationToken);
                }
            }

            // This should never be reached due to the loop logic, but included for completeness
            throw new InvalidOperationException("All retry attempts failed");
        }

        private bool IsRetryableException(Exception ex)
        {
            return ex is HttpRequestException ||
                   ex is TimeoutException ||
                   ex is TaskCanceledException;
        }

        private async Task<T> PostJsonAsync<T>(string endpoint, object data, CancellationToken cancellationToken = default)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/{endpoint}", content, cancellationToken);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"HTTP {response.StatusCode}: {responseContent}");
                }

                try
                {
                    return JsonConvert.DeserializeObject<T>(responseContent);
                }
                catch (JsonException ex)
                {
                    throw new InvalidOperationException($"Failed to parse API response: {responseContent}", ex);
                }
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                throw new TimeoutException("HTTP request timed out", ex);
            }
            catch (HttpRequestException)
            {
                throw; // Re-throw HTTP exceptions as-is
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Unexpected error during API call to {endpoint}", ex);
            }
        }

        private async Task<int> CreateHCaptchaTaskAsync(string websiteUrl, string siteKey, CancellationToken cancellationToken = default)
        {
            var taskData = new
            {
                clientKey = _apiKey,
                task = new
                {
                    type = "HCaptchaTaskProxyless",
                    websiteURL = websiteUrl,
                    websiteKey = siteKey,
                    isInvisible = false
                }
            };

            var response = await PostJsonWithRetryAsync<dynamic>("createTask", taskData, 3, cancellationToken);

            if (response.errorId != 0)
            {
                throw new PicToTextException((int)response.errorId, $"API Error {response.errorId}: {response.errorDescription}");
            }

            return (int)response.taskId;
        }

        private async Task<string> GetTaskResultAsync(int taskId, int maxWaitTimeSeconds = 300, CancellationToken cancellationToken = default)
        {
            var requestData = new
            {
                clientKey = _apiKey,
                taskId = taskId
            };

            var startTime = DateTime.Now;
            var maxWaitTime = TimeSpan.FromSeconds(maxWaitTimeSeconds);

            while (DateTime.Now - startTime < maxWaitTime)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var response = await PostJsonWithRetryAsync<dynamic>("getTaskResult", requestData, 3, cancellationToken);

                if (response.errorId != 0)
                {
                    throw new PicToTextException((int)response.errorId, $"API Error {response.errorId}: {response.errorDescription}");
                }

                string status = response.status;

                if (status == "ready")
                {
                    // Try different possible response fields
                    if (response.solution.gRecaptchaResponse != null)
                    {
                        // hCaptcha/reCaptcha response
                        return response.solution.gRecaptchaResponse;
                    }
                    else if (response.solution.text != null)
                    {
                        // Image captcha response
                        return response.solution.text;
                    }
                    else if (response.solution.token != null)
                    {
                        // Alternative token field
                        return response.solution.token;
                    }
                    else
                    {
                        throw new Exception($"Unknown solution format in API response: {JsonConvert.SerializeObject(response.solution)}");
                    }
                }
                else if (status == "processing")
                {
                    // Wait 3 seconds before next poll
                    await Task.Delay(3000, cancellationToken);
                }
                else
                {
                    throw new Exception($"Unexpected task status: {status}");
                }
            }

            throw new CaptchaSolvingTimeoutException(taskId, $"Task {taskId} did not complete within {maxWaitTimeSeconds} seconds");
        }

        public async Task<string> SolveHCaptchaAsync(string websiteUrl, string siteKey, int maxWaitTimeSeconds = 300, CancellationToken cancellationToken = default)
        {
            using (var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(maxWaitTimeSeconds + 60))) // Add 60s buffer for HTTP operations
            using (var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token))
            {
                try
                {
                    int taskId = await CreateHCaptchaTaskAsync(websiteUrl, siteKey, combinedCts.Token);
                    string result = await GetTaskResultAsync(taskId, maxWaitTimeSeconds, combinedCts.Token);
                    return result;
                }
                catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
                {
                    throw new TimeoutException($"hCaptcha solving operation timed out after {maxWaitTimeSeconds + 60} seconds");
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
        }
    }

    internal class Program
    {
        static async Task Main(string[] args)
        {
            // API key provided by user
            const string apiKey = "6addf951917c6bf7afd054e5b8745679";

            Console.WriteLine("PicToText - eBay hCaptcha Solver");
            Console.WriteLine("=================================");
            Console.WriteLine("Specialized for solving eBay hCaptcha challenges");
            Console.WriteLine();

            var picToText = new PicToText(apiKey);

            try
            {
                // Directly solve eBay hCaptcha without asking for options
                Console.WriteLine("Solving eBay hCaptcha...");
                Console.WriteLine("Website URL: https://www.ebay.com/splashui/captcha");
                Console.WriteLine("Site Key: 195eeb9f-8f50-4a9c-abfc-a78ceaa3cdde");
                Console.WriteLine("This may take 30-60 seconds...");

                string hcaptchaToken = await picToText.SolveHCaptchaAsync(
                    "https://www.ebay.com/splashui/captcha",
                    "195eeb9f-8f50-4a9c-abfc-a78ceaa3cdde");

                Console.WriteLine($"\nSUCCESS: eBay hCaptcha solved!");
                Console.WriteLine($"Token: {hcaptchaToken}");
                Console.WriteLine("\nYou can now use this token to submit the eBay captcha form.");
            }
            catch (InvalidCaptchaImageException ex)
            {
                Console.WriteLine($"Image Error: {ex.Message}");
            }
            catch (PicToTextException ex)
            {
                Console.WriteLine($"Anti-Captcha API Error {ex.ErrorId}: {ex.Message}");
            }
            catch (CaptchaSolvingTimeoutException ex)
            {
                Console.WriteLine($"Timeout Error for Task {ex.TaskId}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
            finally
            {
                picToText.Dispose();
            }

            Console.WriteLine("\nProgram completed.");
        }
    }
}
