using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList;
using uBuyFirst.Search;
using uBuyFirst.SubSearch;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Service for generating unique identifiers for TreeList nodes
    /// </summary>
    public static class NodeIdentifierService
    {
        /// <summary>
        /// Generates a unique identifier for a TreeList node based on its data record
        /// </summary>
        /// <param name="treeList">The TreeList control</param>
        /// <param name="node">TreeList node</param>
        /// <returns>Unique node identifier</returns>
        public static string GetNodeId(TreeList treeList, TreeListNode node)
        {
            if (node == null || treeList == null) 
                return string.Empty;

            var dataRecord = treeList.GetDataRecordByNode(node);
            return GetNodeId(dataRecord);
        }

        /// <summary>
        /// Generates a unique identifier for a data record
        /// </summary>
        /// <param name="dataRecord">The data record</param>
        /// <returns>Unique node identifier</returns>
        public static string GetNodeId(object dataRecord)
        {
            if (dataRecord == null)
                return string.Empty;

            switch (dataRecord)
            {
                case Keyword2Find kw:
                    return $"keyword_{kw.Alias}_{kw.GetHashCode()}";
                case ChildTerm ct:
                    return $"childterm_{ct.Alias}_{ct.GetHashCode()}";
                case KeywordFolder folder:
                    return $"folder_{folder.Name}_{folder.GetHashCode()}";
                default:
                    return $"unknown_{dataRecord.GetHashCode()}";
            }
        }

        /// <summary>
        /// Extracts the node type from a node identifier
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <returns>The node type (keyword, childterm, folder, unknown)</returns>
        public static string GetNodeType(string nodeId)
        {
            if (string.IsNullOrEmpty(nodeId))
                return "unknown";

            var parts = nodeId.Split('_');
            return parts.Length > 0 ? parts[0] : "unknown";
        }

        /// <summary>
        /// Checks if a node identifier represents a specific type
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <param name="nodeType">The type to check for (keyword, childterm, folder)</param>
        /// <returns>True if the node is of the specified type</returns>
        public static bool IsNodeType(string nodeId, string nodeType)
        {
            return GetNodeType(nodeId).Equals(nodeType, System.StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if a node identifier represents a keyword node
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <returns>True if the node is a keyword</returns>
        public static bool IsKeywordNode(string nodeId)
        {
            return IsNodeType(nodeId, "keyword");
        }

        /// <summary>
        /// Checks if a node identifier represents a child term node
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <returns>True if the node is a child term</returns>
        public static bool IsChildTermNode(string nodeId)
        {
            return IsNodeType(nodeId, "childterm");
        }

        /// <summary>
        /// Checks if a node identifier represents a folder node
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <returns>True if the node is a folder</returns>
        public static bool IsFolderNode(string nodeId)
        {
            return IsNodeType(nodeId, "folder");
        }
    }
}
