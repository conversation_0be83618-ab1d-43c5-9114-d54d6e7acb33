﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Telegram;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Action for formatting individual cells
    /// </summary>
    public class FormatCellsAction : IFilterAction
    {
        public const string Identifier = "FORMAT_CELLS";

        public string DisplayName => "Format cells";
        public string ActionTypeIdentifier => Identifier;



        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context is { GridView: not null, FilterRule.GridFormatRule: not null })
                {
                    context.GridView.FormatRules.Add(context.FilterRule.GridFormatRule);
                    return Task.FromResult(FilterActionResult.CreateSuccess("Format cells rule applied"));
                }
                return Task.FromResult(FilterActionResult.CreateFailure("Missing grid view or format rule"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to apply format cells: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(filter.FormatColumn))
            {
                errorMessage = "Format column must be specified for cell formatting";
                return false;
            }
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for formatting entire rows
    /// </summary>
    public class FormatRowsAction : IFilterAction
    {
        public const string IDENTIFIER = "FORMAT_ROWS";

        public string DisplayName => "Format rows";
        public string ActionTypeIdentifier => IDENTIFIER;



        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context is { GridView: not null, FilterRule.GridFormatRule: not null })
                {
                    context.GridView.FormatRules.Add(context.FilterRule.GridFormatRule);
                    return Task.FromResult(FilterActionResult.CreateSuccess("Format rows rule applied"));
                }
                return Task.FromResult(FilterActionResult.CreateFailure("Missing grid view or format rule"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to apply format rows: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            return true; // Row formatting doesn't require additional validation
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for removing rows that match criteria
    /// </summary>
    public class RemoveRowsAction : IFilterAction
    {
        public const string IDENTIFIER = "REMOVE_ROWS";

        public string DisplayName => "Remove rows";
        public string ActionTypeIdentifier => IDENTIFIER;



        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context.SourceDataTable == null || context.FilterRule == null)
                    return Task.FromResult(FilterActionResult.CreateFailure("Missing data table or filter rule"));

                var removedCount = 0;
                var i = 0;
                while (context.SourceDataTable.Rows.Count > i)
                {
                    if (context.FilterRule.GetEvaluator().Fit(context.SourceDataTable.Rows[i]))
                    {
                        context.SourceDataTable.Rows[i].Delete();
                        removedCount++;
                    }
                    else
                    {
                        i++;
                    }
                }

                return Task.FromResult(FilterActionResult.CreateSuccess($"Removed {removedCount} rows", removedCount));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to remove rows: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            return true; // Row removal doesn't require additional validation
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    public class SendToTelegramAction : IFilterAction
    {
        public const string IDENTIFIER = "SEND_TO_TELEGRAM";
        public string DisplayName => "Send to Telegram";
        public string ActionTypeIdentifier => IDENTIFIER;
        private readonly ITelegramSender? _telegramSender;

        public SendToTelegramAction(ITelegramSender? telegramSender)
        {
            _telegramSender = telegramSender;
        }

        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            if (_telegramSender == null)
            {
                return Task.FromResult(FilterActionResult.CreateSuccess("Telegram service not configured or available; message not sent."));
            }

            if (!_telegramSender.Enabled)
                return Task.FromResult(FilterActionResult.CreateSuccess("Telegram sender is not enabled."));
            if (context.CurrentRow == null)
                return Task.FromResult(FilterActionResult.CreateFailure("CurrentRow is null. SendToTelegramAction requires a row context.", null));
            if (context.FilterRule == null)
                return Task.FromResult(FilterActionResult.CreateFailure("FilterRule is null in context.", null));

            try
            {
                if (context.FilterRule.GetEvaluator().Fit(context.CurrentRow))
                {
                    _telegramSender.PushItem(context.CurrentRow);
                    var itemId = context.CurrentRow.Table.Columns.Contains("ItemID") ? context.CurrentRow["ItemID"].ToString() : "Unknown Item";
                    return Task.FromResult(FilterActionResult.CreateSuccess($"Sent item {itemId} to Telegram via filter '{context.FilterRule.Alias}'."));
                }
                return Task.FromResult(FilterActionResult.CreateSuccess($"Item did not match Telegram filter '{context.FilterRule.Alias}'."));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to send to Telegram for filter '{context.FilterRule.Alias}': {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            // No specific configuration needed for the action itself.
            // Validation of the global TelegramSender setup is outside this scope.
            return true;
        }

        // This action does not store its own configuration data.
        // Telegram settings are managed by the injected ITelegramSender.
        public Dictionary<string, object> SerializeActionData() => new();
        public void DeserializeActionData(Dictionary<string, object> data) { /* No data to deserialize for this action */ }
    }


    /// <summary>
    /// Action for buying items with a specific eBay account
    /// </summary>
    public class BuyWithAccountAction : IFilterAction
    {
        public const string IDENTIFIER = "BUY_WITH_ACCOUNT";

        public string AccountUsername { get; set; }

        public string DisplayName => string.IsNullOrEmpty(AccountUsername)
            ? "Buy with account"
            : $"Buy with {AccountUsername}";

        public string ActionTypeIdentifier => IDENTIFIER;



        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context.SourceDataTable == null || context.FilterRule == null)
                    return Task.FromResult(FilterActionResult.CreateFailure("Missing data table or filter rule"));

                if (string.IsNullOrEmpty(AccountUsername))
                    return Task.FromResult(FilterActionResult.CreateFailure("No account username specified"));

                var eBayAccount = Form1.EBayAccountsList?.FirstOrDefault(account =>
                    account.UserName.Equals(AccountUsername, StringComparison.OrdinalIgnoreCase));

                if (eBayAccount == null)
                    return Task.FromResult(FilterActionResult.CreateFailure($"eBay account '{AccountUsername}' not found"));

                var updatedCount = 0;
                var i = 0;
                while (context.SourceDataTable.Rows.Count > i)
                {
                    var row = context.SourceDataTable.Rows[i];
                    if (context.FilterRule.GetEvaluator().Fit(row))
                    {
                        var d = (DataList)row["Blob"];
                        d.EbayAccount = eBayAccount;
                        row["Blob"] = d;
                        updatedCount++;
                    }
                    i++;
                }

                return Task.FromResult(FilterActionResult.CreateSuccess($"Updated {updatedCount} items with account {AccountUsername}", updatedCount));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to apply buy with account: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(AccountUsername))
            {
                errorMessage = "Account username must be specified";
                return false;
            }
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(AccountUsername))
            {
                data["AccountUsername"] = AccountUsername;
            }
            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data.TryGetValue("AccountUsername", out var username))
            {
                AccountUsername = username?.ToString();
            }
        }

        // Helper method to load data from filter
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter.ActionData.TryGetValue("AccountUsername", out var username))
            {
                AccountUsername = username?.ToString();
            }
        }

        // Helper method to save data to filter
        public void SaveToFilter(XFilterClass filter)
        {
            if (!string.IsNullOrEmpty(AccountUsername))
            {
                filter.ActionData["AccountUsername"] = AccountUsername;
            }
        }
    }

    /// <summary>
    /// Example of a new extensible action - Send to Webhook
    /// This demonstrates how easy it is to add new actions
    /// </summary>
    public class SendToWebhookAction : IFilterAction
    {
        public const string IDENTIFIER = "SEND_TO_WEBHOOK";

        public string WebhookUrl { get; set; }

        public string DisplayName => "Send to Webhook";
        public string ActionTypeIdentifier => IDENTIFIER;



        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return Task.FromResult(FilterActionResult.CreateFailure("Missing row data or filter rule"));

                if (string.IsNullOrEmpty(WebhookUrl))
                    return Task.FromResult(FilterActionResult.CreateFailure("Webhook URL not configured"));

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    // TODO: Implement actual webhook sending logic here
                    // For now, just return success to indicate the match
                    return Task.FromResult(FilterActionResult.CreateSuccess($"Would send to webhook: {WebhookUrl}"));
                }

                return Task.FromResult(FilterActionResult.CreateSuccess("No match for webhook"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to process webhook action: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            if (string.IsNullOrEmpty(WebhookUrl))
            {
                errorMessage = "Webhook URL must be configured";
                return false;
            }

            // TODO: Add URL validation
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            var data = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(WebhookUrl))
            {
                data["WebhookUrl"] = WebhookUrl;
            }
            return data;
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            if (data.TryGetValue("WebhookUrl", out var url))
            {
                WebhookUrl = url?.ToString();
            }
        }

        // Helper method to load data from filter
        public void LoadFromFilter(XFilterClass filter)
        {
            if (filter.ActionData.TryGetValue("WebhookUrl", out var url))
            {
                WebhookUrl = url?.ToString();
            }
        }

        // Helper method to save data to filter
        public void SaveToFilter(XFilterClass filter)
        {
            if (!string.IsNullOrEmpty(WebhookUrl))
            {
                filter.ActionData["WebhookUrl"] = WebhookUrl;
            }
        }
    }

    /// <summary>
    /// Action for opening item pages in browser for items matching filter criteria
    /// </summary>
    public class OpenItemPageAction : IFilterAction
    {
        public const string IDENTIFIER = "OPEN_ITEM_PAGE";

        public string DisplayName => "Open item page";
        public string ActionTypeIdentifier => IDENTIFIER;

        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return Task.FromResult(FilterActionResult.CreateFailure("Missing row data or filter rule"));

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    var dataList = (DataList)context.CurrentRow["Blob"];
                    if (dataList != null)
                    {
                        Browser.OpenAffiliateLink(dataList);
                        return Task.FromResult(FilterActionResult.CreateSuccess($"Opened item page for item {dataList.ItemID}"));
                    }
                    return Task.FromResult(FilterActionResult.CreateFailure("DataList not found in row"));
                }

                return Task.FromResult(FilterActionResult.CreateSuccess("Item did not match filter criteria"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to open item page: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            return true; // No additional configuration needed
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }

    /// <summary>
    /// Action for opening checkout pages in browser for items matching filter criteria
    /// </summary>
    public class OpenCheckoutPageAction : IFilterAction
    {
        public const string IDENTIFIER = "OPEN_CHECKOUT_PAGE";

        public string DisplayName => "Open checkout page";
        public string ActionTypeIdentifier => IDENTIFIER;

        public Task<FilterActionResult> ExecuteAsync(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return Task.FromResult(FilterActionResult.CreateFailure("Missing row data or filter rule"));

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    var dataList = (DataList)context.CurrentRow["Blob"];
                    if (dataList != null)
                    {
                        Browser.OpenCheckoutLink(dataList);
                        return Task.FromResult(FilterActionResult.CreateSuccess($"Opened checkout page for item {dataList.ItemID}"));
                    }
                    return Task.FromResult(FilterActionResult.CreateFailure("DataList not found in row"));
                }

                return Task.FromResult(FilterActionResult.CreateSuccess("Item did not match filter criteria"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(FilterActionResult.CreateFailure($"Failed to open checkout page: {ex.Message}", ex));
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string? errorMessage)
        {
            errorMessage = null;
            return true; // No additional configuration needed
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data to deserialize for this action
        }
    }
}
