using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Pricing;
using uBuyFirst.Prefs;
using uBuyFirst.Restocker.Services;
using uBuyFirst.Tools;

namespace uBuyFirst.Tests.Integration
{
    [TestClass]
    [TestCategory("Integration")]
    [TestCategory("DailySpend")]
    public class DailySpendLimitIntegrationTests
    {
        private DailySpendService _dailySpendService;
        private PurchaseExecutionService _purchaseService;

        [TestInitialize]
        public void Setup()
        {
            // Reset UserSettings for clean test state
            UserSettings.DailySpendLimit = 1000m;
            UserSettings.DailySpendHistory = new Dictionary<DateTime, decimal>();

            _dailySpendService = new DailySpendService();
            _purchaseService = new PurchaseExecutionService(_dailySpendService);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _purchaseService?.Dispose();
            UserSettings.DailySpendHistory?.Clear();
        }

        [TestMethod]
        public async Task EndToEnd_DailySpendLimit_ShouldBlockPurchaseWhenLimitExceeded()
        {
            // Arrange: Set up a scenario where we're close to the daily limit
            UserSettings.DailySpendLimit = 100m;
            _dailySpendService.RecordPurchase(90m); // Already spent $90

            var expensiveItem = CreateTestDataList("expensive-item", 25.00, "USD");
            var keyword = CreateTestKeyword("test-keyword", "JOB-001", 1, 0);

            // Act: Try to purchase an item that would exceed the limit
            var result = await _purchaseService.TryPurchaseItemAsync(expensiveItem, keyword, "test-filter");

            // Assert: Purchase should be blocked due to daily limit
            Assert.IsNotNull(result);
            Assert.IsFalse(result.Success);
            Assert.IsTrue(result.IsDailyLimitExceeded);
            Assert.IsTrue(result.Message.Contains("Daily spend limit exceeded"));
        }

        [TestMethod]
        public async Task EndToEnd_DailySpendLimit_ShouldAllowPurchaseWithinLimit()
        {
            // Arrange: Set up a scenario within the daily limit
            UserSettings.DailySpendLimit = 1000m;
            _dailySpendService.RecordPurchase(50m); // Already spent $50

            var affordableItem = CreateTestDataList("affordable-item", 25.00, "USD");
            var keyword = CreateTestKeyword("test-keyword", "JOB-002", 2, 0);

            // Act: Try to purchase an item within the limit
            var result = await _purchaseService.TryPurchaseItemAsync(affordableItem, keyword, "test-filter");

            // Assert: Purchase should proceed (though it may fail at checkout stage in tests)
            Assert.IsNotNull(result);
            Assert.IsFalse(result.IsDailyLimitExceeded);
        }

        [TestMethod]
        public void Settings_Persistence_ShouldSaveDailySpendData()
        {
            // Arrange: Record some purchases
            _dailySpendService.RecordPurchase(100m);
            _dailySpendService.RecordPurchase(50m);
            _dailySpendService.SetDailyLimit(2000m);

            // Act: Save settings through Form1 instance (if available in test environment)
            // Note: In unit tests, we'll verify the data is properly set in UserSettings
            // The actual saving would be done by Form1.Instance.SaveSettings() in production

            // Assert: Verify the data is in UserSettings (settings are saved)
            Assert.AreEqual(2000m, UserSettings.DailySpendLimit);
            Assert.AreEqual(150m, _dailySpendService.GetTodaySpent());

            // Verify that DailySpendHistory contains today's data
            Assert.IsNotNull(UserSettings.DailySpendHistory);
            Assert.IsTrue(UserSettings.DailySpendHistory.ContainsKey(DateTime.Today));
            Assert.AreEqual(150m, UserSettings.DailySpendHistory[DateTime.Today]);
        }

        [TestMethod]
        public void UI_DisplayUpdate_DataValidation()
        {
            // Arrange: Set up spend data
            UserSettings.DailySpendLimit = 500m;
            _dailySpendService.RecordPurchase(150m);

            // Act & Assert: Verify the data that would be displayed in UI
            var todaySpent = _dailySpendService.GetTodaySpent();
            var remainingBudget = _dailySpendService.GetRemainingBudget();
            var dailyLimit = _dailySpendService.GetDailyLimit();

            // Verify the calculations that would be used in UI display
            Assert.AreEqual(150m, todaySpent, "Today spent should be $150");
            Assert.AreEqual(350m, remainingBudget, "Remaining budget should be $350");
            Assert.AreEqual(500m, dailyLimit, "Daily limit should be $500");

            // Verify percentage calculation for potential progress bar
            var percentageUsed = (todaySpent / dailyLimit) * 100;
            Assert.AreEqual(30m, percentageUsed, "Should be 30% of daily limit used");
        }

        [TestMethod]
        public void DataCleanup_ShouldRemoveOldRecords()
        {
            // Arrange: Add old records
            var oldDate = DateTime.Today.AddDays(-35);
            var recentDate = DateTime.Today.AddDays(-5);

            UserSettings.DailySpendHistory[oldDate] = 100m;
            UserSettings.DailySpendHistory[recentDate] = 200m;
            UserSettings.DailySpendHistory[DateTime.Today] = 50m;

            // Act: Trigger cleanup
            _dailySpendService.CleanupOldRecords();

            // Assert: Old records should be removed, recent ones kept
            Assert.IsFalse(UserSettings.DailySpendHistory.ContainsKey(oldDate));
            Assert.IsTrue(UserSettings.DailySpendHistory.ContainsKey(recentDate));
            Assert.IsTrue(UserSettings.DailySpendHistory.ContainsKey(DateTime.Today));
        }

        [TestMethod]
        public void CurrencyConversion_ShouldConvertToUSD()
        {
            // Arrange: Create item with non-USD pricing
            var gbpItem = CreateTestDataList("gbp-item", 40.00, "GBP");
            var keyword = CreateTestKeyword("test-keyword", "JOB-003", 1, 0);

            // Act: Check if the service can handle currency conversion
            var todaySpentBefore = _dailySpendService.GetTodaySpent();

            // Note: In a real test, we would mock the purchase execution
            // For now, we test the currency conversion logic directly
            var effectivePrice = gbpItem.ItemPricing.GetEffectivePurchasePrice();
            var convertedAmount = CurrencyConverter.ConvertToUSD(effectivePrice.Value, effectivePrice.Currency);

            // Assert: Conversion should produce a reasonable USD amount
            Assert.IsTrue(convertedAmount > 0, "Currency conversion should produce positive USD amount");
            Assert.AreNotEqual(effectivePrice.Value, convertedAmount, "GBP should convert to different USD amount");
        }

        [TestMethod]
        public void MultipleQuantity_ShouldCalculateCorrectTotal()
        {
            // Arrange: Item with multiple quantity needed
            var item = CreateTestDataList("multi-item", 25.00, "USD");
            var keyword = CreateTestKeyword("test-keyword", "JOB-004", 5, 2); // Need 3 more

            // Act: Calculate expected purchase amount
            var quantityNeeded = Math.Max(0, keyword.RequiredQuantity - keyword.PurchasedQuantity);
            var expectedAmount = item.ItemPricing.ItemPrice.Value * quantityNeeded;

            // Assert: Should calculate for 3 items = $75
            Assert.AreEqual(3, quantityNeeded);
            Assert.AreEqual(75.00, expectedAmount);
        }

        private DataList CreateTestDataList(string itemId, double price, string currency)
        {
            return new DataList
            {
                ItemID = itemId,
                Title = $"Test Item {itemId}",
                QuantityAvailable = 10,
                ItemPricing = new ItemPricing
                {
                    ItemPrice = new CurrencyAmount(price, currency)
                }
            };
        }

        private Keyword2Find CreateTestKeyword(string id, string jobId, int required, int purchased)
        {
            return new Keyword2Find
            {
                Id = id,
                Alias = $"Test Keyword {id}",
                JobId = jobId,
                RequiredQuantity = required,
                PurchasedQuantity = purchased
            };
        }
    }
}
