﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using eBay.Service.Call;
using eBay.Service.Core.Soap;
using uBuyFirst.AI;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Images;
using uBuyFirst.Item;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing;
using uBuyFirst.Search;
using uBuyFirst.Search.Status;
using uBuyFirst.Seller;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using PopupMenuShowingEventArgs = DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs;

namespace uBuyFirst.Grid
{
    public static class GridViewEvents
    {
        public static GuiChanger GuiChanger;

        // Fields to track the hovered cell
        internal static int hoveredRowHandle = -1;
        private static string hoveredColumnFieldName = "";

        public static void gridControl1_Paint(object sender, PaintEventArgs e)
        {
            var grView = (AdvBandedGridView)((GridControl)sender).MainView;
            var viewInfo = grView.GetViewInfo() as GridViewInfo;

            foreach (var row in grView.GetSelectedRows())
            {
                if (!grView.IsDataRow(row))
                    return;

                var rowInfo = viewInfo?.GetGridRowInfo(row);

                if (rowInfo == null)
                    return;

                var lineStyle = DashStyle.Solid;
                if (!grView.GridControl.IsFocused)
                {
                    lineStyle = DashStyle.Dot;
                }

                if (!grView.GridControl.IsFocused && grView == FocusRouter.FocusedGridView)
                {
                    lineStyle = DashStyle.Solid;
                }

                var rect = new Rectangle(rowInfo.DataBounds.X, rowInfo.DataBounds.Y, rowInfo.DataBounds.Width, rowInfo.DataBounds.Height - 1);
                var pen = new Pen(Color.Black, 1);
                pen.DashStyle = lineStyle;
                try
                {
                    var graphicsCache = grView.GridControl.CreateGraphicsCache();
                    graphicsCache.DrawRectangle(pen, rect);
                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Top-2), new Point(rect.Right, rect.Top-2));
                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Bottom-2), new Point(rect.Right, rect.Bottom-2));
                }
                catch (Exception exception)
                {
                    Debug.WriteLine(exception);
                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "gridControl1_Paint Rect:" + rect + "\r\n" + exception.Message);
                }
            }
        }

        public static void gridView1_ShowBands(object sender, EventArgs e)
        {
            var grView = (AdvBandedGridView)sender;
            foreach (GridBand band in grView.Bands)
            {
                band.Visible = true;
            }
        }

        public static void gridView1_DoubleClick(object sender, EventArgs e)
        {
            var grView = (GridView)sender;
            var pt = grView.GridControl.PointToClient(Control.MousePosition);
            var info = grView.CalcHitInfo(pt);
            if (info.InRow || info.InRowCell)
            {
                if (info.Column != null)
                {
                    var d = (DataList)grView.GetDataRow(info.RowHandle)["Blob"];
                    Form1.Instance.TopMost = false;
                    var isNeedToRedirect = DateTimeOffset.UtcNow.AddDays(-1).ToUnixTimeSeconds() > ProgramState.UBuyFirstRedirectTimestamp;
                    if (isNeedToRedirect && ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 10)
                    {
                        ProgramState.UBuyFirstRedirectTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                        Browser.OpenAffiliateLinkVia_uBuyFirstRedirect(d);
                    }
                    else
                    {
                        Browser.OpenAffiliateLink(d);
                    }
                }
            }
        }

        public static void gridView1_MouseLeave(object sender, EventArgs e)
        {
            var grView = (GridView)sender;
            grView.GridControl.Cursor = Cursors.Default;
            if (hoveredRowHandle >= 0 && hoveredColumnFieldName == "Seller Name")
            {
                hoveredRowHandle = -1;
                hoveredColumnFieldName = "";
            }
        }

        public static bool PauseScrolling = false;

        public static void gridView1_MouseMove(object sender, MouseEventArgs e)
        {
            if (!(sender is GridView view))
                return;

            var hitInfo = view.CalcHitInfo(new Point(e.X, e.Y));

            if (hitInfo.Column == null || !hitInfo.InRowCell)
            {
                view.GridControl.Cursor = Cursors.Default;
            }
            else
            {
                if (hitInfo.Column.Name.Contains("Price"))
                {
                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
                    {
                        view.GridControl.Cursor = Cursors.Default;
                    }
                    else
                        view.GridControl.Cursor = Cursors.Hand;
                }
                else if (hitInfo.Column.Name.Contains("EbayWebsite"))
                {
                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
                    {
                        view.GridControl.Cursor = Cursors.Default;
                    }
                    else
                        view.GridControl.Cursor = Cursors.Hand;
                }
                else if (hitInfo.Column.Name.Contains("FeedbackScore"))
                {
                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
                    {
                        view.GridControl.Cursor = Cursors.Default;
                    }
                    else
                        view.GridControl.Cursor = Cursors.Hand;
                }
                else if (hitInfo.Column.Name.Contains("FeedbackRating"))
                {
                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
                    {
                        view.GridControl.Cursor = Cursors.Default;
                    }
                    else
                        view.GridControl.Cursor = Cursors.Hand;
                }
                else if (hitInfo.Column.Name.Contains("Term") || hitInfo.Column.Name.Contains("Alias"))
                {
                    if (view.GetRowCellValue(hitInfo.RowHandle, "Alias") is DBNull || view.GetRowCellValue(hitInfo.RowHandle, "Term") is DBNull)
                    {
                        view.GridControl.Cursor = Cursors.Default;
                    }
                    else
                    {
                        var cellValue = view.GetRowCellValue(hitInfo.RowHandle, hitInfo.Column.FieldName)
                            ?.ToString();
                        if (!string.IsNullOrEmpty(cellValue))
                        {
                            // Regex pattern to match URLs in the text
                            var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
                            var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);

                            // Check if the text contains a URL
                            var match = regex.Match(cellValue);
                            if (match.Success)
                                view.GridControl.Cursor = Cursors.Hand;
                        }
                    }
                }
                else
                {
                    view.GridControl.Cursor = Cursors.Default;
                }
            }

            if (hitInfo.InRowCell && hitInfo.Column.FieldName == "Seller Name")
            {
                // Update previously hovered cell if necessary
                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
                {
                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
                }

                // Set new hovered cell
                hoveredRowHandle = hitInfo.RowHandle;
                hoveredColumnFieldName = hitInfo.Column.FieldName;
            }
            else
            {
                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
                {
                    // Reset only if the mouse moves out of any valid cell area
                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
                    hoveredRowHandle = -1;
                    hoveredColumnFieldName = null;
                }
            }
        }

        public static void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (sender is not ColumnView view)
                return;

            if (e.ListSourceRowIndex == GridControl.InvalidRowHandle)
                return;

            if (e.Column.FieldName == "Item Price")
            {
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                {
                    e.DisplayText = d.ItemPricing.ItemPrice.FormatPrice();
                    var itemPrice = d.ItemPricing.ItemPrice;
                    if (!itemPrice.Value.Equals(0.0))
                        e.DisplayText = itemPrice.FormatPrice();
                    else
                        e.DisplayText = "";
                }
            }

            if (e.Column.FieldName == "Auction Price")
            {
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                    if (d.ItemPricing.AuctionPrice.Value.Equals(0.0))
                    {
                        e.DisplayText = "";
                    }
                    else
                    {
                        e.DisplayText = d.ItemPricing.AuctionPrice?.FormatPrice();
                    }
            }

            if (e.Column.FieldName == "Shipping")
            {
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                {
                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
                    {
                        e.DisplayText = "*";
                    }
                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
                    {
                        e.DisplayText = "N/A";
                    }
                    else
                    {
                        e.DisplayText = d.ItemShipping.FullSingleShippingPrice.FormatPrice();
                    }
                }
            }

            if (e.Column.FieldName == "Ship Additional Item")
            {
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
                        e.DisplayText = "*";
                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
                        e.DisplayText = "N/A";
                    else
                        e.DisplayText = d.ItemShipping.ShipAdditionalItem.FormatPrice();
            }

            if (e.Column.FieldName == "Total Price")
            {
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                {
                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
                    else
                    {
                        var totalPrice = d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice);
                        if (totalPrice.Value.Equals(0.0))
                            e.DisplayText = "";
                        else
                            e.DisplayText = totalPrice.FormatPrice();
                    }
                }
            }

            //posted time
            if (e.Column.FieldName == "Posted Time")
            {
                e.Column.SortMode = ColumnSortMode.Value;
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                    e.DisplayText = d.StartTimeLocal?.ToString("HH:mm:ss dd-MMM-yyyy");
            }

            //found time
            if (e.Column.FieldName == "Found Time")
            {
                e.Column.SortMode = ColumnSortMode.Value;
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                    e.DisplayText = d.FoundTime.ToString();
            }

            //Sold time
            if (e.Column.FieldName == "Time Left")
            {
                if (e.Value != null && e.Value != DBNull.Value)
                {
                    var secondsLeft = (TimeSpan)e.Value;
                    if (secondsLeft.TotalSeconds < 0)
                    {
                        e.DisplayText = "0";
                    }
                    else
                    {
                        if (secondsLeft.TotalHours >= 24)
                            e.DisplayText = secondsLeft.ToString(@"d\.hh\:mm\m");
                        else if (secondsLeft.TotalMinutes >= 60)
                            e.DisplayText = secondsLeft.ToString(@"hh\:mm\:ss");
                        else if (secondsLeft.TotalSeconds >= 60)
                            e.DisplayText = secondsLeft.ToString(@"mm\:ss");
                        else
                            e.DisplayText = secondsLeft.ToString("ss");
                    }
                }
            }

            if (e.Column.FieldName == "Sold Time")
            {
                if (e.Value != DBNull.Value)
                {
                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
                    if (d?.SoldTime != null)
                        e.DisplayText = d.SoldTime.ToString();
                }
            }

            //seller registration
            if (e.Column.FieldName == "Seller Registration")
            {
                e.Column.SortMode = ColumnSortMode.Value;
                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
                    e.DisplayText = SellerHelper.FormatSellerRegistrationDate(d.SellerRegistration);
            }

            if (Debugger.IsAttached && e.Column.FieldName.Contains("Relist"))
            {
                if (e.Value != DBNull.Value)
                {
                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
                    e.DisplayText = d.LastStatusCheck.LocalTime.ToString("hh:mm:ss");
                }
            }

            if (e.Column.FieldName == "Returns")
            {
                if (e.Value != DBNull.Value)
                {
                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
                    if (d?.HighBidder != null && Form1._showHighBidder)
                    {
                        e.DisplayText = d.HighBidder;
                        e.Column.SortMode = ColumnSortMode.DisplayText;
                    }
                    else
                    {
                        if (e.Value != null)
                            e.DisplayText = e.Value.ToString();
                    }
                }
            }
        }

        internal static void gridView1_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            TopRowFocus.LastFocusedTime = DateTimeOffset.UtcNow;
        }

        private static void OnDeleteRowClick(object sender, EventArgs e)
        {
            var tagData = ((DXMenuItem)sender).Tag;
            if (tagData is ValueTuple<DataRow, GridView> tuple)
            {
                var grView = tuple.Item2;
                grView.DeleteSelectedRows();
            }
        }

        internal static async void HandleWatchlistDeletionRequest(AdvBandedGridView grView)
        {
            var selectedRowHandles = grView.GetSelectedRows();
            if (selectedRowHandles == null || selectedRowHandles.Length == 0)
            {
                XtraMessageBox.Show("No rows selected to remove from watchlist.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var itemIdsToRemove = new List<string>();
            Auth.EbayAccount? targetAccount = null;
            string? firstItemIdForLog = null;
            string? itemTitle = null;

            foreach (var handle in selectedRowHandles)
            {
                if (!grView.IsDataRow(handle)) continue;

                var row = grView.GetDataRow(handle);
                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached) continue;

                if (row["Blob"] is not DataList d || string.IsNullOrEmpty(d.ItemID) || d.EbayAccount == null)
                {
                    continue;
                }

                if (targetAccount == null)
                {
                    targetAccount = d.EbayAccount;
                    firstItemIdForLog = d.ItemID;
                    itemTitle = row["Title"]?.ToString();
                }
                else if (targetAccount.UserName != d.EbayAccount.UserName)
                {
                    XtraMessageBox.Show("Cannot remove items from watchlist belonging to different eBay accounts in a single operation.",
                        "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                itemIdsToRemove.Add(d.ItemID);
            }

            if (!itemIdsToRemove.Any())
            {
                XtraMessageBox.Show("No valid items found in selection to remove from watchlist.",
                    "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (targetAccount == null)
            {
                Form1.Log?.Error($"HandleWatchlistDeletionRequest: Target account is null despite having items to remove (first ItemID: {firstItemIdForLog}).");
                XtraMessageBox.Show("Could not determine the eBay account for the selected items.",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string itemIdentifier;
            if (itemIdsToRemove.Count == 1)
            {
                itemIdentifier = $"item '{itemTitle ?? firstItemIdForLog}'";
            }
            else
            {
                itemIdentifier = $"{itemIdsToRemove.Count} items";
            }

            var confirmResult = XtraMessageBox.Show(
                $"Are you sure you want to remove {itemIdentifier} from your eBay watchlist for account '{targetAccount.UserName}'?",
                "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (confirmResult != DialogResult.Yes)
            {
                return;
            }

            try
            {
                var success = await Watchlist.WatchlistManager.RemoveItemsFromEbayWatchlistAsync(targetAccount, itemIdsToRemove);
                if (success)
                {
                    grView.DeleteSelectedRows();
                }
            }
            catch (Exception ex)
            {
                Form1.Log?.Error($"Error removing items from watchlist: {ex.Message}");
                XtraMessageBox.Show($"Failed to remove items from watchlist: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static async void OnDeleteWatchlistItemClick(object sender, EventArgs e)
        {
            var menuItem = (DXMenuItem)sender;
            if (menuItem.Tag is not ValueTuple<DataRow, GridView> tuple)
            {
                return;
            }
            HandleWatchlistDeletionRequest((AdvBandedGridView)tuple.Item2);
        }


        private static async void OnAddToWatchlistClick(object sender, EventArgs e)
        {
            var menuItem = (DXMenuItem)sender;
            if (menuItem.Tag is not ValueTuple<DataRow, GridView> tuple)
            {
                Form1.Log?.Error("OnAddToWatchlistClick: Tag was not the expected ValueTuple<DataRow, GridView>.");
                return;
            }

            var (row, grView) = tuple; // grView might not be needed here, but extracted for consistency

            if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
            {
                return; // Row is gone or invalid
            }

            if (row["Blob"] is not DataList d || d.EbayAccount == null || string.IsNullOrEmpty(d.ItemID))
            {
                Form1.Log?.Error($"OnAddToWatchlistClick: Could not retrieve valid DataList, EbayAccount, or ItemID from DataRow for ItemID: {row["ItemID"]?.ToString() ?? "null"}");
                XtraMessageBox.Show("Could not get item details to add to watchlist.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                // Call the API method (to be implemented in WatchlistManager)
                var success = await Watchlist.WatchlistManager.AddItemsToEbayWatchlistAsync(d.EbayAccount, new List<string>() { d.ItemID });

                if (success)
                {
                    XtraMessageBox.Show($"Successfully added item '{d.ItemID}' to eBay watchlist.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    XtraMessageBox.Show("Item may not have been added due to an error.", "Import Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Form1.Log?.Error($"Error during watchlist item addition (ItemID: {d.ItemID}): {ex}");
                XtraMessageBox.Show($"An error occurred while adding the item to the watchlist: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void OnCheckoutClick(object sender, EventArgs e)
        {
            var row = (DataRow)((DXMenuItem)sender).Tag;
            var d = (DataList)row["Blob"];
            Browser.LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetCheckoutLink());
        }

        private static void OnMakeOfferClick(object sender, EventArgs e)
        {
            var row = (DataRow)((DXMenuItem)sender).Tag;
            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
        }

        private static void OnBuyItNowClick(object sender, EventArgs e)
        {
            var row = (DataRow)((DXMenuItem)sender).Tag;
            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
        }

        public static void gridView1_RowCellClick(object sender, RowCellClickEventArgs e)
        {
            ProgramState.Idlesw.Restart();
            var grView = (AdvBandedGridView)sender;
            var dataRow = grView.GetDataRow(e.RowHandle);
            if (dataRow?.RowState is not DataRowState.Detached and not DataRowState.Deleted)
                if (e.Column.FieldName == "ItemID")
                {
                    var d = (DataList)dataRow["Blob"];
                    if (!string.IsNullOrEmpty(d.GetAffiliateLink()))
                        Browser.OpenAffiliateLink(d);

                    //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
                }

            if (e.Column.FieldName.Contains("Price") && !string.IsNullOrEmpty(e.CellValue?.ToString()))
            {
                //if (!Config.IsAllowed(ProgramState.SerialNumber))
                //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
                Placeoffer.ShowPlaceOffer(dataRow, Placeoffer.OrderAction.CommitToBuy,
                    !UserSettings.ClickOnPriceOpensProductPage);
            }

            if (e.Column.FieldName == "Seller Name")
            {
                var hitInfo = grView.CalcHitInfo(e.Location);
                // Check if the click is on the correct column
                if (grView.GetViewInfo() is not GridViewInfo)
                {
                    return;
                }

                // Assuming the icon is drawn on the right side of the cell
                var cellValueRect = ((GridViewInfo)grView.GetViewInfo())
                    .GetGridCellInfo(hitInfo.RowHandle, hitInfo.Column).Bounds;
                var icon = Resources.Blocked; // Your icon resource
                var iconWidth = icon.Width;
                var iconHeight = icon.Height;

                // Define the icon's rectangle area within the cell
                var iconRect = new Rectangle(cellValueRect.Right - (int)iconWidth - 10, // 10 pixels from the right edge
                    cellValueRect.Y + (cellValueRect.Height - (int)iconHeight) / 2, (int)iconWidth, (int)iconHeight);

                // Check if the click was within the icon's bounds
                if (iconRect.Contains(e.Location))
                {
                    var sellerName = e.CellValue?.ToString();
                    if (!string.IsNullOrEmpty(sellerName))
                    {
                        // Call the SellerHelper method to block the seller
                        SellerHelper.BlockSellerAndRemoveFromGrids(sellerName);
                    }
                }
            }

            if (e.Column.FieldName is "Feedback Score" or "Feedback Rating")
            {
                if (grView.GetViewInfo() is not GridViewInfo)
                {
                    return;
                }

                var feedbackCall = new GetFeedbackCall(ConnectionConfig.GetGuestApiContext(SiteCodeType.US));
                feedbackCall.DetailLevelList = new DetailLevelCodeTypeCollection { DetailLevelCodeType.ReturnAll };
                feedbackCall.ApiRequest.EndUserIP = "201.201.201." + new Random().Next(1, 200);
                var sellerName = dataRow["Seller Name"].ToString();
                feedbackCall.UserID = sellerName;
                feedbackCall.Execute();
                var feedbackTable = GetFeedbackSummaryAsText(feedbackCall.FeedbackSummary);
                var feedbacks = "";
                foreach (FeedbackDetailType feedback in feedbackCall.FeedbackList)
                {
                    var feedbackStyle = "";
                    var timePassed = DateTime.Now.Subtract(feedback.CommentTime);

                    var timeTxt = "";
                    if (timePassed.Days > 365)
                    {
                        timeTxt = (timePassed.Days / 365).ToString() + "y";
                    }
                    else if (timePassed.Days > 30)
                    {
                        timeTxt = Math.Round((double)timePassed.Days / 30).ToString() + "mo";
                    }
                    else if (timePassed.Days > 7)
                    {
                        timeTxt = (timePassed.Days / 7).ToString() + "w";
                    }
                    else
                    {
                        timeTxt = timePassed.Days.ToString() + "d";
                    }

                    switch (feedback.CommentType)
                    {
                        case CommentTypeCodeType.Positive:
                            feedbacks += $"[{timeTxt}] {feedback.ItemTitle ?? "+"}\r\n";
                            break;
                        case CommentTypeCodeType.Neutral:
                            feedbacks += $"<backcolor='orange'>[{timeTxt}] {feedback.ItemTitle}</backcolor>\r\n";
                            break;
                        case CommentTypeCodeType.Negative:
                            feedbacks += $"<backcolor=255,102,102>[{timeTxt}] [{feedback.CommentText}] {"\t" + feedback.ItemTitle}</backcolor>\r\n";
                            break;
                    }
                }

                var summaryAsText = $"<b>\r\n{feedbackTable}</b><br>\r\n{feedbacks}";

                var toolTipItem = new ToolTipItem();
                toolTipItem.Text = summaryAsText;
                var superToolTip = new SuperToolTip();
                superToolTip.Items.Add(toolTipItem);
                superToolTip.AllowHtmlText = DefaultBoolean.True;
                superToolTip.MaxWidth = 1000;
                var args = new ToolTipControllerShowEventArgs
                {
                    SuperTip = superToolTip,
                    AllowHtmlText = DefaultBoolean.True,
                    ToolTipType = ToolTipType.SuperTip,
                    ToolTipLocation = ToolTipLocation.BottomRight
                };

                ToolTipController.DefaultController.KeepWhileHovered = true;
                ToolTipController.DefaultController.AutoPopDelay = 30 * 1000;
                ToolTipController.DefaultController.AllowHtmlText = true;
                ToolTipController.DefaultController.Appearance.Font = new Font("Courier New", 10);
                ToolTipController.DefaultController.ShowHint(args);
            }

            if (e.Column.FieldName == "Term" || e.Column.FieldName == "Alias")
            {
                var termText = e.CellValue?.ToString();
                if (!string.IsNullOrEmpty(termText))
                {
                    // Regex pattern to match URLs in the text
                    var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
                    var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);

                    // Check if the text contains a URL
                    var match = regex.Match(termText);
                    if (match.Success)
                    {
                        try
                        {
                            // Open the first matched URL in the browser
                            Process.Start(match.Value);
                        }
                        catch (Exception ex)
                        {
                            // Handle any exceptions, e.g., log the error or show a message to the user
                            XtraMessageBox.Show("Failed to open URL: " + ex.Message);
                        }
                    }
                }
            }
        }

        public static string GetFeedbackSummaryAsText(FeedbackSummaryType feedbackSummary)
        {
            // Set a fixed column width
            var columnWidth = 7;

            // Table Headers
            var header = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}", "[1M]", "[6M]", "[12M]");

            // Row for Positive Feedback
            var positiveRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
                feedbackSummary.PositiveFeedbackPeriodArray[0].Count,
                feedbackSummary.PositiveFeedbackPeriodArray[1].Count,
                feedbackSummary.PositiveFeedbackPeriodArray[2].Count);

            // Row for Neutral Feedback
            var neutralRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
                feedbackSummary.NeutralFeedbackPeriodArray[0].Count,
                feedbackSummary.NeutralFeedbackPeriodArray[1].Count,
                feedbackSummary.NeutralFeedbackPeriodArray[2].Count);

            // Row for Negative Feedback
            var negativeRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
                feedbackSummary.NegativeFeedbackPeriodArray[0].Count,
                feedbackSummary.NegativeFeedbackPeriodArray[1].Count,
                feedbackSummary.NegativeFeedbackPeriodArray[2].Count);

            // Combine all rows with line breaks
            return string.Join(Environment.NewLine, header, $"<color=grey>{positiveRow}</color>", $"<color=orange>{neutralRow}</color>", $"<color=255,102,102>{negativeRow}</color>");
        }

        private static string PadRight(string text, int length)
        {
            return text.PadRight(length);
        }

        public static string GetFeedbackSummaryAsText2(FeedbackSummaryType feedbackSummary)
        {
            var summaryText = new StringBuilder();

            // Negative Feedback Period Array
            foreach (var feedbackPeriod in feedbackSummary.NegativeFeedbackPeriodArray.ToArray())
            {
                summaryText.AppendLine($"Negative: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
            }

            // Neutral Feedback Period Array
            foreach (var feedbackPeriod in feedbackSummary.NeutralFeedbackPeriodArray.ToArray())
            {
                summaryText.AppendLine($"Neutral Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
            }

            // Positive Feedback Period Array
            foreach (var feedbackPeriod in feedbackSummary.PositiveFeedbackPeriodArray.ToArray())
            {
                summaryText.AppendLine($"Positive Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
            }

            // Seller Rating Summary Array
            foreach (var ratingSummary in feedbackSummary.SellerRatingSummaryArray.ToArray())
            {
                foreach (var ratingDetails in ratingSummary.AverageRatingDetails.ToArray())
                {
                    summaryText.AppendLine($"Seller Rating - {ratingDetails.RatingDetail}: {ratingDetails.Rating} ({ratingDetails.RatingCount} ratings) in {ratingSummary.FeedbackSummaryPeriod} period.");
                }
            }

            // Seller Role Metrics
            if (feedbackSummary.SellerRoleMetrics != null)
            {
                summaryText.AppendLine(
                    $"Seller Role Metrics - Positive Feedback Left: {feedbackSummary.SellerRoleMetrics.PositiveFeedbackLeftCount}, " +
                    $"Neutral Feedback Left: {feedbackSummary.SellerRoleMetrics.NeutralFeedbackLeftCount}, " +
                    $"Negative Feedback Left: {feedbackSummary.SellerRoleMetrics.NegativeFeedbackLeftCount}, " +
                    $"Feedback Left Percent: {feedbackSummary.SellerRoleMetrics.FeedbackLeftPercent}%, " +
                    $"Cross Border Transaction Count: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionCount}, " +
                    $"Cross Border Transaction Percent: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionPercent}%, " +
                    $"Repeat Buyer Count: {feedbackSummary.SellerRoleMetrics.RepeatBuyerCount}, " +
                    $"Repeat Buyer Percent: {feedbackSummary.SellerRoleMetrics.RepeatBuyerPercent}%, " +
                    $"Unique Buyer Count: {feedbackSummary.SellerRoleMetrics.UniqueBuyerCount}, " +
                    $"Transaction Percent: {feedbackSummary.SellerRoleMetrics.TransactionPercent}%.");
            }

            // Total Feedback Period Array
            foreach (var feedbackPeriod in feedbackSummary.TotalFeedbackPeriodArray.ToArray())
            {
                summaryText.AppendLine($"Total Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
            }

            // Unique Feedback Counts
            summaryText.AppendLine($"Unique Positive Feedback: {feedbackSummary.UniquePositiveFeedbackCount}, " +
                                   $"Unique Neutral Feedback: {feedbackSummary.UniqueNeutralFeedbackCount}, " +
                                   $"Unique Negative Feedback: {feedbackSummary.UniqueNegativeFeedbackCount}.");

            return summaryText.ToString();
        }

        public static void gridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            var grView = (GridView)sender;
            if (e.MenuType == GridMenuType.Row)
            {
                var rowHandle = e.HitInfo.RowHandle;
                var row = grView.GetDataRow(rowHandle);

                if (row == null)
                    return;
                var d = (DataList)row["Blob"];
                var menuItemBuyNow = new DXMenuItem("&Buy Now", OnBuyItNowClick);
                menuItemBuyNow.SvgImage = Resources.Buy1;
                var menuItemCheckout = new DXMenuItem("&Checkout page", OnCheckoutClick, Form1.Instance.barButtonBuy.Glyph);
                menuItemCheckout.SvgImage = Resources.Checkout;
                if (d is { CommitToBuy: true, Variation: false })
                {
                    var f = menuItemBuyNow.Appearance.Font;
                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Regular, f.Unit, f.GdiCharSet);
                    menuItemBuyNow.Appearance.ForeColor = Color.Black;
                }
                else
                {
                    var f = menuItemBuyNow.Appearance.Font;
                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Underline, f.Unit, f.GdiCharSet);
                    menuItemBuyNow.Appearance.ForeColor = Color.Blue;

                    if (e != null)
                    {
                        if (e.HitInfo != null)
                        {
                            if (e.HitInfo.Column != null)
                            {
                                if (e.HitInfo.Column.FieldName != null)
                                {
                                }
                            }
                        }
                    }
                }

                var font = menuItemCheckout.Appearance.Font;
                menuItemCheckout.Appearance.Font = new Font(font.ToString(), font.Size, FontStyle.Underline, font.Unit, font.GdiCharSet);
                menuItemCheckout.Appearance.ForeColor = Color.Blue;

                var menuItemBestOffer = new DXMenuItem("&Make Offer", OnMakeOfferClick);
                menuItemBestOffer.SvgImage = Resources.MakeOffer;
                if (row["Best Offer"].ToString() == "False")
                {
                    menuItemBestOffer.Enabled = false;
                }

                // --- Start Delete Item Logic ---
                string deleteMenuText = "&Delete Rows (Del)"; // Default text
                EventHandler deleteClickHandler = OnDeleteRowClick; // Default handler
                bool isWatchlistItem = d != null && d.Source == SearchSource.WAT; // Check if item is from watchlist

                if (isWatchlistItem)
                {
                    deleteMenuText = "&Delete from Watchlist";
                    deleteClickHandler = OnDeleteWatchlistItemClick; // Assign new handler for watchlist items
                }

                var menuItemDeleteRow = new DXMenuItem(deleteMenuText, deleteClickHandler, Form1.Instance.imageList16.Images[1]);
                // --- End Delete Item Logic ---

                var menuItemIgnoreSeller = new DXMenuItem("&Ignore Seller", OnIgnoreSellerClick);
                menuItemIgnoreSeller.SvgImage = Resources.Ignore_Seller1;
                var menuItemSearchOnAmazon = new DXMenuItem("Search on Amazon", OnSearchAmazonClick);
                var menuItemSearchSoldOnEbay = new DXMenuItem("Search sold on Ebay", OnSearchSoldEbayClick);
                menuItemSearchSoldOnEbay.SvgImage = Resources.SearchSoldOnEbay;
                menuItemSearchOnAmazon.SvgImage = Resources.amazon;
                menuItemBuyNow.Tag = row;
                menuItemCheckout.Tag = row;
                menuItemBestOffer.Tag = row;
                // Set Tag to a tuple containing both the row and the grid view
                menuItemDeleteRow.Tag = (row, grView);
                menuItemIgnoreSeller.Tag = row;
                menuItemSearchOnAmazon.Tag = row;
                menuItemSearchSoldOnEbay.Tag = row;
                // --- Add Menu Items ---
                e.Menu.Items.Add(menuItemBuyNow);
                e.Menu.Items.Add(menuItemCheckout);
                e.Menu.Items.Add(menuItemBestOffer);

                menuItemSearchOnAmazon.BeginGroup = true;

                e.Menu.Items.Add(menuItemSearchOnAmazon);
                e.Menu.Items.Add(menuItemSearchSoldOnEbay);

                // Conditionally add "Add to Watchlist" if not already a watchlist item
                if (!isWatchlistItem)
                {
                    var menuItemAddToWatchlist = new DXMenuItem("&Add to eBay Watchlist", OnAddToWatchlistClick);
                    // Consider adding an icon: menuItemAddToWatchlist.SvgImage = Resources.AddToWatchlist; // Assuming you have such a resource
                    menuItemAddToWatchlist.SvgImage = Resources.AddToWatchlist;
                    menuItemAddToWatchlist.Tag = (row, grView);
                    e.Menu.Items.Add(menuItemAddToWatchlist);
                }

                // Add Delete, Ignore
                menuItemIgnoreSeller.BeginGroup = true;
                e.Menu.Items.Add(menuItemIgnoreSeller);
                e.Menu.Items.Add(menuItemDeleteRow); // This item's text/handler is already conditional




                //var menuItemBuyUsingPaypal = new DXMenuItem("&Buy Using Paypal", OnBuyUsingPaypal);
                //e.Menu.Items.Add(menuItemBuyUsingPaypal);
            }

            if (e.MenuType == GridMenuType.Column)
            {
                var dxMenuItemExportToFile = new DXMenuItem("Export to Excel", OnExportToExcelClick);
                dxMenuItemExportToFile.SvgImage = Resources.Export;
                dxMenuItemExportToFile.Tag = grView;
                e.Menu.Items.Add(dxMenuItemExportToFile);

                var dxMenuItemResetLayout = new DXMenuItem("Reset Grid Layout", OnResetGridLayoutClick);
                dxMenuItemResetLayout.SvgImage = Resources.ResetLayout;
                dxMenuItemResetLayout.Tag = grView;
                e.Menu.Items.Add(dxMenuItemResetLayout);
                if (e.Menu.Items.Count > 7)
                {
                    e.Menu.Items.Insert(7, new DXMenuItem("Update all items status", UpdateAllItemsStatus));
                    e.Menu.Items.Insert(7, new DXMenuItem("Add custom columns", Form1.ShowCustomColumnsWindow));
                    var llmMenuItem = new DXMenuItem("Update LLM columns", AiAnalysis.UpdateAnalysis);
                    llmMenuItem.Tag = grView;
                    e.Menu.Items.Insert(7, llmMenuItem);
                }
            }

            //gridBand1.Visible = true;
            //gridBand2.Visible = true;
            //gridBand3.Visible = true;
        }

        private static void OnIgnoreSellerClick(object sender, EventArgs e)
        {
            var row = (DataRow)((DXMenuItem)sender).Tag;
            var sellerName = SellerHelper.GetSellerNameFromRow(row);

            if (!string.IsNullOrEmpty(sellerName))
            {
                SellerHelper.BlockSellerAndRemoveFromGrids(sellerName);
            }
        }

        internal static void RemoveSellerFromGridViews(string sellerName)
        {
            // Delegate to SellerHelper for consistency
            SellerHelper.BlockSellerAndRemoveFromGrids(sellerName);
        }

        private static async void UpdateAllItemsStatus(object sender, EventArgs e)
        {
            var itemDict = new Dictionary<string, DataRow>();
            foreach (var view in ResultsView.ViewsDict)
            {
                var datatable = (DataTable)view.Value.DataSource;
                var i = 0;
                while (i < datatable?.Rows.Count)
                {
                    var row = datatable.Rows[i];
                    var dataList = (DataList)row["Blob"];

                    if (dataList.ItemStatus != ItemStatus.Active && dataList.ItemStatus != ItemStatus.Unknown && dataList.ItemStatus != ItemStatus.Updated)
                    {
                        i++;

                        continue;
                    }

                    itemDict.Add(dataList.ItemID, row);
                    i++;

                    var maxRowsToUpdate = 500;
                    if (i > maxRowsToUpdate)
                        break;
                }
            }

            var batches = itemDict.Batch(20);

            foreach (var iEnumerable in batches)
            {
                var batch = iEnumerable.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                var updatedItems = await Task.Run(() => GetItemsStatus.FetchBatchStatusBrowseAPI(batch.Select(pair => pair.Key).ToList()));
                if (updatedItems != null)
                {
                    GetItemsStatus.UpdateGrids(batch, updatedItems);
                }
            }
        }

        public static void gridView1_RowDeleted(object sender, RowDeletedEventArgs e)
        {
            if (e.RowHandle == 0)
            {
                UpdateDataOnRowChange((AdvBandedGridView)sender, e.RowHandle);
            }

            if (((AdvBandedGridView)sender).RowCount == 0)
            {
                GuiChanger.ClearItemInfoOnZeroRows();
            }
        }

        public static void gridView1_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            if (e.FocusedRowHandle == GridControl.InvalidRowHandle)
                return;
            var gridView = ((AdvBandedGridView)sender);
            var firstVisibleRow = gridView.GetVisibleRowHandle(0);
            if (rowCount < gridView.RowCount)
            {
                if (e.FocusedRowHandle - e.PrevFocusedRowHandle == 1)
                {
                    if (gridView.GridControl.Cursor == Cursors.Hand)
                    {
                        gridView.TopRowIndex++;


                        var dataRow = FocusRouter.FocusedGridView.GetDataRow(0);

                        if (dataRow == null)
                            return;
                        var args = gridView.GridControl.ToolTipController.CreateShowArgs();
                        args.ToolTip = dataRow["Title"].ToString();
                        args.IconType = ToolTipIconType.None;
                        args.ImageIndex = -1;
                        args.IconSize = ToolTipIconSize.Small;
                        args.ToolTipAnchor = ToolTipAnchor.Object;

                        GridView view = gridView;
                        var info = view.GetViewInfo() as GridViewInfo;
                        if (info != null)
                        {
                            var colInfo = info.ColumnsInfo;

                            var titleColumn = view.Columns["Title"];
                            if (titleColumn != null && colInfo != null)
                            {
                                if (gridView.GridControl?.Parent?.Parent?.Parent == null)
                                    return;

                                var isFloatingPanel = gridView.GridControl.Parent.Parent.Parent.Parent == null;
                                var gridColumnInfoArgs = colInfo[titleColumn];
                                if (gridColumnInfoArgs == null)
                                {
                                    return;
                                }

                                var rect = gridColumnInfoArgs.Bounds;

                                var xFormLocation = 0;
                                var xPanelLocation = 0;
                                var xColumn = rect.X;
                                var yFormLocation = 0;
                                var yPanelLocation = 0;
                                var yGridLocation = 0;

                                if (isFloatingPanel)
                                {
                                    if (gridView.GridControl.Parent.Parent is DockPanel dockPanel)
                                    {
                                        xPanelLocation = dockPanel.FloatLocation.X;
                                        yPanelLocation = dockPanel.FloatLocation.Y + 5;
                                    }
                                }
                                else
                                {
                                    xFormLocation = Form1.Instance.Location.X;
                                    xPanelLocation = gridView.GridControl.Parent.Parent.Parent.Location.X;
                                    yFormLocation = Form1.Instance.Location.Y;
                                    yPanelLocation = gridView.GridControl.Parent.Parent.Parent.Parent.Location.Y;
                                    yGridLocation = gridView.GridControl.Location.Y;
                                }

                                var cursorPosition = new Point(xFormLocation + xPanelLocation + 5 + xColumn, yFormLocation + yPanelLocation + yGridLocation);
                                gridView.GridControl.ToolTipController.ShowHint(args, cursorPosition);
                            }
                        }
                    }
                }
            }

            UpdateDataOnRowChange((AdvBandedGridView)sender, e.FocusedRowHandle);
        }

        public static bool GridUpDownHold = false;
        public static bool FocusedRowChanged = false;

        public static void UpdateDataOnRowChange(AdvBandedGridView grView, int focusedRowHandle)
        {
            FocusedRowChanged = true;

            if (GridUpDownHold)
                return;

            if (grView != FocusRouter.FocusedGridView)
                return;

            var row = GetDataRow(grView, focusedRowHandle);

            if (row == null)
                return;

            var d = (DataList)row["Blob"];
            if (ProgramState.LastSelectedItemID != d.ItemID || string.IsNullOrEmpty(ProgramState.LastSelectedItemID))
            {
                try
                {
                    var ctRowChange = GuiChanger.ResetCancelToken();
                    ProgramState.LastSelectedItemID = d.ItemID;
                    Form1.Instance.layoutControl1.BeginUpdate();
                    try
                    {
                        GuiChanger.UpdateItemDetailsPanel(d);
                        ItemSpecifics.UpdateItemDetailsPanelSpecifics(grView, row, Form1.Instance.layoutControl1);
                    }
                    catch (Exception ex)
                    {
                        ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
                    }

                    Form1.Instance.layoutControl1.EndUpdate();
                    GuiChanger.UpdateButtons(d);

                    GuiChanger.ClearGalleryControl();
                    ImageCache.Inst.ClearCache();

                    var pics = new List<string> { d.GalleryUrl };
                    pics.AddRange(d.Pictures);
                    GuiChanger.UpdatePicPanel(pics, ctRowChange);

                    string urlForExternal = UserSettings.IsInternalEndpoint ? UserSettings.InternalEndpointUrl : UserSettings.ExternalEndpointUrl;
                    if (UserSettings.IsAIEndpoint)
                    {
                        urlForExternal = UserSettings.AiExternalEndpointUrl;
                    }
                    GuiChanger.UpdateExternalData(urlForExternal, d.Row);

                    // Update AI columns if they are empty and AI endpoint is enabled
                    var uiContext = SynchronizationContext.Current;
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await AiAnalysis.UpdateAiColumnsOnRowChange(row, uiContext);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error updating AI columns on row change: {ex.Message}");
                        }
                    });

                    if (Form1.Instance.dockDescription.Visibility != DockVisibility.Visible
                        || d.SellerName.Contains("kathe_laz")
                        || d.SellerName.Contains("wirelessmobilepc")
                        || d.SellerName.Contains("oklets1"))
                        return;

                    GuiChanger.UpdateBrowser(d.ItemID, row, GuiChanger.CancelTokenOnRowChange);
                }
                catch (TaskCanceledException)
                {
                }
                catch (OperationCanceledException)
                {
                }
                catch (FileNotFoundException)
                {
                }
                catch (RowNotInTableException)
                {
                }
                catch (IOException ioex)
                {
                    if (Debugger.IsAttached)
                    {
                        MessageBox.Show(@"Update Avatar: " + ioex.Message);
                    }
                }
                catch (ArgumentNullException anex)
                {
                    if (anex.Message != "Additional information: Value cannot be nul")
                        ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
                }
                catch (Exception ex)
                {
                    ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
                }
            }
        }

        private static DataRow GetDataRow(AdvBandedGridView grView, int focusedRowHandle)
        {
            DataRow row = null;
            try
            {
                row = grView.GetDataRow(focusedRowHandle);

                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                    return null;
            }
            catch (ArgumentNullException anex)
            {
                if (anex.Message != "Value cannot be null.\r\nParameter name: key")
                    ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
            }
            catch (Exception)
            {
                // ignored
            }

            return row;
        }

        public static void GridView1_GotFocus(object sender, EventArgs e)
        {
            FocusRouter.FocusedGridView = (GridView)sender;
            if (FocusRouter.FocusedGridView.RowCount == 1)
            {
                if (FocusRouter.FocusedGridView.FocusedRowHandle == GridControl.InvalidRowHandle)
                    return;

                //UpdateDataOnRowChange((AdvBandedGridView)sender, FocusRouter.FocusedGridView.FocusedRowHandle);
            }
        }

        public static void GridView1_Click(object sender, EventArgs e)
        {
            if (FocusRouter.FocusedGridView != ((GridView)sender))
            {
                FocusRouter.FocusedGridView = ((GridView)sender);
                FocusRouter.FocusedGridView.InvalidateRow(FocusRouter.FocusedGridView.FocusedRowHandle);
            }
        }

        public static void OnResetGridLayoutClick(object sender, EventArgs e)
        {
            var result = XtraMessageBox.Show("Are you sure?\nGrid settings like columns size, order, visibility, etc. will be set to a default state", "", MessageBoxButtons.OKCancel);
            if (result == DialogResult.OK)
            {
                var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
                var datasource = grView.GridControl.DataSource;
                grView.GridControl.DataSource = null;
                grView.Columns.Clear();
                grView.GridControl.DataSource = datasource;
                GridBuilder.ResetGridViewLayout(grView);
            }
        }

        private static string _exportFolderLocation = "";

        private static void OnExportToExcelClick(object sender, EventArgs e)
        {
            var dialog = new FolderBrowserDialog();
            dialog.Description = "Export to folder";
            if (!string.IsNullOrWhiteSpace(_exportFolderLocation))
                dialog.SelectedPath = _exportFolderLocation;
            var result = dialog.ShowDialog();
            if (result != DialogResult.OK)
                return;

            _exportFolderLocation = dialog.SelectedPath;
            var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
            var fileName = $"uBuyFirst Export {DateTime.Now.ToString("u").Replace(":", ".").Replace("Z", "")}.csv";
            var exportPath = Path.Combine(_exportFolderLocation, fileName);
            var exportRows = new List<String>();
            var headers = grView.VisibleColumns.Select(c => c.FieldName.ToString()).ToList();
            var headerRow = Helpers.CreateCSVRow(headers);
            exportRows.Add(headerRow);
            for (var i = 0; i < grView.RowCount; i++)
            {
                var exportCells = new List<String>();
                foreach (var header in headers)
                {
                    var cellText = grView.GetRowCellDisplayText(i, header);
                    exportCells.Add(cellText);
                }

                var properRow = Helpers.CreateCSVRow(exportCells);
                exportRows.Add(properRow);
            }

            var fileContents = string.Join("\r\n", exportRows);
            File.WriteAllText(exportPath, fileContents);
            Process.Start(exportPath);
        }

        private static int rowCount;

        public static void gridView1_RowCountChanged(object sender, EventArgs e)
        {
            var grView = (AdvBandedGridView)sender;
            if (grView.RowCount == 1)
            {
                TopRowFocus.FocusTopRow(grView, 0);
            }

            if (rowCount < grView.RowCount)
            {
                //grView.TopRowIndex++;//= grView.RowCount - rowCount;
            }

            //rowCount = grView.RowCount;
        }

        public static void gridView1_TopRowChanged(object sender, EventArgs e)
        {
            return;

            var gridView = ((AdvBandedGridView)sender);
            if (rowCount < gridView.RowCount)
            {
                gridView.TopRowIndex++; //= grView.RowCount - rowCount;
            }

            rowCount = gridView.RowCount;
        }

        public static void GridView1_FormatRuleDataUpdateCustomTrigger(object sender, FormatRuleGridDataUpdateTriggerEventArgs e)
        {
            switch (e.Rule.Name)
            {
                case "Quantity":
                    if (int.TryParse(e.NewValue.ToString(), out var newQuantity))
                        if (int.TryParse(e.OldValue.ToString(), out var oldQuantity))
                            if (newQuantity > oldQuantity)
                            {
                                e.Trigger = true;
                                return;
                            }
                    break;

                case "Item Price":
                    if (double.TryParse(e.NewValue.ToString(), out var newPrice))
                        if (double.TryParse(e.OldValue.ToString(), out var oldPrice))
                            if (newPrice < oldPrice)
                            {
                                e.Trigger = true;
                                return;
                            }
                    break;
            }

            e.Trigger = false;
        }

        private static void OnSearchAmazonClick(object sender, EventArgs e)
        {
            try
            {
                var row = (DataRow)((DXMenuItem)sender).Tag;
                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                {
                    return;
                }

                var d = (DataList)row["Blob"];
                if (d?.Title == null)
                {
                    return;
                }

                var searchQuery = CleanSearchQuery(d.Title);
                if (string.IsNullOrWhiteSpace(searchQuery))
                {
                    MessageBox.Show("Cannot search with empty title.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var amazonUrl = $"{Config.AmazonSearchAffiliateUrl}?k={Uri.EscapeDataString(searchQuery)}";
                try
                {
                    Process.Start(amazonUrl);
                }
                catch (System.ComponentModel.Win32Exception)
                {

                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error searching on Amazon: {ex.Message}");
                MessageBox.Show("Failed to open Amazon search. Please try again.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private static void OnSearchSoldEbayClick(object sender, EventArgs e)
        {
            try
            {
                var row = (DataRow)((DXMenuItem)sender).Tag;
                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
                {
                    return;
                }

                var d = (DataList)row["Blob"];
                if (d?.Title == null)
                {
                    return;
                }

                var searchQuery = CleanSearchQuery(d.Title);
                if (string.IsNullOrWhiteSpace(searchQuery))
                {
                    MessageBox.Show("Cannot search with empty title.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var ebayUrl = $"https://www.ebay.com/sch/i.html?_nkw={Uri.EscapeDataString(searchQuery)}&_sacat=0&_from=R40&rt=nc&LH_Sold=1&LH_Complete=1";
                try
                {
                    Process.Start(ebayUrl);
                }
                catch (System.ComponentModel.Win32Exception)
                {

                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error searching on Ebay: {ex.Message}");
                MessageBox.Show("Failed to open Ebay search. Please try again.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private static string CleanSearchQuery(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            var phrasesToRemove = new[] {
                "free shipping",
                "fast shipping",
                "worldwide shipping",
                "new with tags",
                "nwt",
                "new without tags",
                "nwot",
                "with box",
                "no box",
                "authentic",
                "genuine"
            };

            var cleanTitle = title.ToLower();
            foreach (var phrase in phrasesToRemove)
            {
                cleanTitle = cleanTitle.Replace(phrase, "");
            }

            cleanTitle = System.Text.RegularExpressions.Regex.Replace(cleanTitle, @"[^\w\s-]", " ");
            cleanTitle = System.Text.RegularExpressions.Regex.Replace(cleanTitle, @"\s+", " ").Trim();

            return cleanTitle;
        }
    }
}
