﻿using System;
using System.IO;
using System.Net;
using System.Net.Cache;
using System.Reflection;
using System.Threading.Tasks;
using System.Xml;
using AutoUpdaterDotNET;
using uBuyFirst.Prefs;

namespace uBuyFirst.Update
{
    internal class Updater
    {
        private XmlDocument _receivedXmlDocument;
        private static int _sharedCounter;

        public Updater()
        {
            // Both auto and manual should check the same host, so increment counter once for both
            AutoUpdater.AppCastURL = ManualUpdateUrl;
            AutoUpdater.Start();
            CheckProgramUpdateAvailable();

            // Increment shared counter after both checks are initiated
            IncrementSharedCounter();
        }


        private static string AutoUpdateUrl
        {
            get
            {
                var urlIndex = (_sharedCounter > 0 && _sharedCounter % 10 == 0) ? 1 : 0; // First check and every 10th subsequent check logic
                var updateUrl = $"{Config.UpdateUrls[urlIndex]}?ver={ProgramState.UBFVersion}&license={ProgramState.SerialNumber}&hwid={ProgramState.HWID}&cb={new Random().Next(10000)}&type=auto";
                return updateUrl;
            }
        }

        private static string ManualUpdateUrl
        {
            get
            {
                var urlIndex = (_sharedCounter > 0 && _sharedCounter % 10 == 0) ? 1 : 0; // First check and every 10th subsequent check logic
                var updateUrl = $"{Config.UpdateUrls[urlIndex]}?ver={ProgramState.UBFVersion}&license={ProgramState.SerialNumber}&hwid={ProgramState.HWID}&cb={new Random().Next(10000)}&type=manual";
                return updateUrl;
            }
        }

        private static string GetUpdateUrl(bool usePrimary, string type = "auto")
        {
            var urlIndex = usePrimary ? 0 : 1; // Index 0 = primary (data.ubuyfirst.net), Index 1 = fallback (ubuyfirst.com)
            var updateUrl = $"{Config.UpdateUrls[urlIndex]}?ver={ProgramState.UBFVersion}&license={ProgramState.SerialNumber}&hwid={ProgramState.HWID}&cb={new Random().Next(10000)}&type={type}";
            return updateUrl;
        }

        private static void IncrementSharedCounter()
        {
            _sharedCounter++;
        }

        public void CheckProgramUpdateAvailable()
        {
            Task.Run(() =>
            {
                var isUpdateDownloaded = CheckUpdate();
                if (!isUpdateDownloaded)
                {
                    //CheckUpdate();
                }
            });
        }

        private bool CheckUpdate()
        {
            try
            {
                var appCastStream = DownloadUpdateXml();
                if (appCastStream == null)
                    return false;

                LoadUpdateXml(appCastStream);
                var downloadUrl = ReadXmlProperty("url");
                var newVersionString = ReadXmlProperty("version");
                var newVersion = new Version(newVersionString);
                if (newVersion <= Assembly.GetEntryAssembly()?.GetName().Version)
                    return false;

                DownloadSetup(downloadUrl);
                var downloadedUpgradeFiles = Upgrader.GetDownloadedUpgradeFiles();
                var latestUpgradePath = Upgrader.FindLatestUpgradeFilePath(downloadedUpgradeFiles);
                Upgrader.ImmediateUpgradeFile = Upgrader.UpgradeFile.Parse(latestUpgradePath);

                if (downloadUrl.Contains("immediate"))
                {
                    ScheduleImmediateUpgrade();
                }

                Form1.Instance.ShowUpdateNotification();
                return true;
            }
            catch (Exception)
            {
                // ignored
            }

            return false;
        }

        private static void ScheduleImmediateUpgrade()
        {
            Upgrader.ImmediateUpgradeAvailable = true;
        }

        private static void DownloadSetup(string downloadUrl)
        {
            var setupFilePath = Path.Combine(Path.GetTempPath(), ParseFileName(downloadUrl));
            var setupFileTmpPath = setupFilePath + ".tmp";
            if (!File.Exists(setupFilePath))
            {
                using (var webClient = new WebClient())
                {
                    webClient.DownloadFile(new Uri(downloadUrl), setupFileTmpPath);
                    File.Copy(setupFileTmpPath, setupFilePath);
                    File.Delete(setupFileTmpPath);
                }
            }
        }

        private string ReadXmlProperty(string propertyName)
        {
            return _receivedXmlDocument.SelectSingleNode("item")?.SelectSingleNode(propertyName)?.InnerText;
        }

        private void LoadUpdateXml(Stream appCastStream)
        {
            _receivedXmlDocument = new XmlDocument();
            _receivedXmlDocument.Load(appCastStream);
        }

        private static Stream DownloadUpdateXml()
        {
            Stream appCastStream = null;

            // Try primary URL first (data.ubuyfirst.net)
            try
            {
                var primaryUrl = GetUpdateUrl(usePrimary: true, type: "auto");
                var webRequest = WebRequest.Create(primaryUrl);
                webRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                webRequest.Timeout = 10 * 1000;
                var webResponse = webRequest.GetResponse();
                appCastStream = webResponse.GetResponseStream();
                return appCastStream;
            }
            catch (Exception)
            {
                // Primary URL failed, try fallback URL (ubuyfirst.com)
            }

            // Try fallback URL if primary failed
            try
            {
                var fallbackUrl = GetUpdateUrl(usePrimary: false, type: "auto");
                var webRequest = WebRequest.Create(fallbackUrl);
                webRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                webRequest.Timeout = 10 * 1000;
                var webResponse = webRequest.GetResponse();
                appCastStream = webResponse.GetResponseStream();
            }
            catch (Exception)
            {
                // Both URLs failed
            }

            return appCastStream;
        }

        private static string ParseFileName(string url)
        {
            var fileName = string.Empty;
            var uri = new Uri(url);
            if (uri.Scheme.Equals(Uri.UriSchemeHttp) || uri.Scheme.Equals(Uri.UriSchemeHttps))
            {
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(uri);
                httpWebRequest.CachePolicy = new HttpRequestCachePolicy(HttpRequestCacheLevel.NoCacheNoStore);
                httpWebRequest.Method = "HEAD";
                httpWebRequest.AllowAutoRedirect = false;
                var httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                if (httpWebResponse.StatusCode.Equals(HttpStatusCode.Redirect) ||
                    httpWebResponse.StatusCode.Equals(HttpStatusCode.Moved) ||
                    httpWebResponse.StatusCode.Equals(HttpStatusCode.MovedPermanently))
                {
                    if (httpWebResponse.Headers["Location"] != null)
                    {
                        var location = httpWebResponse.Headers["Location"];
                        fileName = ParseFileName(location);
                        return fileName;
                    }
                }

                var contentDisposition = httpWebResponse.Headers["content-disposition"];
                if (!string.IsNullOrEmpty(contentDisposition))
                {
                    const string lookForFileName = "filename=";
                    var index = contentDisposition.IndexOf(lookForFileName, StringComparison.CurrentCultureIgnoreCase);
                    if (index >= 0)
                        fileName = contentDisposition.Substring(index + lookForFileName.Length);

                    if (fileName.StartsWith("\"") && fileName.EndsWith("\""))
                    {
                        fileName = fileName.Substring(1, fileName.Length - 2);
                    }
                }
            }

            if (string.IsNullOrEmpty(fileName))
            {
                fileName = Path.GetFileName(uri.LocalPath);
            }

            return fileName;
        }
    }
}
