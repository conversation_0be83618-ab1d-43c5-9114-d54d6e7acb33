using System;
using System.Linq;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a complete snapshot of a Keyword2Find object at a specific point in time
    /// This preserves all keyword properties even if the original keyword is later modified or deleted
    /// </summary>
    public class KeywordSnapshot
    {
        // Core identification
        public string KeywordId { get; set; } = string.Empty;
        public string Alias { get; set; } = string.Empty;
        public string JobId { get; set; } = string.Empty;

        // Search criteria
        public string Keywords { get; set; } = string.Empty;
        public bool SearchInDescription { get; set; }

        // Price constraints
        public double PriceMin { get; set; }
        public double PriceMax { get; set; }

        // Category and condition filters
        public string Categories { get; set; } = string.Empty;
        public string[] Condition { get; set; } = new string[0];

        // Location and shipping
        public string EbaySiteName { get; set; } = string.Empty;
        public string LocatedIn { get; set; } = string.Empty;
        public string AvailableTo { get; set; } = string.Empty;
        public string Zip { get; set; } = string.Empty;

        // Seller filters
        public string Sellers { get; set; } = string.Empty;
        public string SellerType { get; set; } = string.Empty;

        // Search configuration
        public string ViewName { get; set; } = string.Empty;
        public string ListingType { get; set; } = string.Empty;

        // Purchase requirements
        public int RequiredQuantity { get; set; }
        public int PurchasedQuantity { get; set; }

        // Snapshot metadata
        public DateTime CapturedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Creates a KeywordSnapshot from a Keyword2Find object
        /// </summary>
        /// <param name="keyword">The keyword to create a snapshot from</param>
        /// <returns>A complete snapshot of the keyword's current state</returns>
        /// <exception cref="ArgumentNullException">Thrown when keyword is null</exception>
        public static KeywordSnapshot FromKeyword2Find(Keyword2Find keyword)
        {
            if (keyword == null)
                throw new ArgumentNullException(nameof(keyword));

            return new KeywordSnapshot
            {
                KeywordId = keyword.Id ?? string.Empty,
                Alias = keyword.Alias ?? string.Empty,
                JobId = keyword.JobId ?? string.Empty,
                Keywords = keyword.Kws ?? string.Empty,
                SearchInDescription = keyword.SearchInDescription,
                PriceMin = keyword.PriceMin,
                PriceMax = keyword.PriceMax,
                Categories = keyword.Categories4Api ?? string.Empty,
                Condition = keyword.Condition ?? new string[0],
                EbaySiteName = keyword.EbaySiteName ?? string.Empty,
                LocatedIn = keyword.LocatedIn ?? string.Empty,
                AvailableTo = keyword.AvailableTo ?? string.Empty,
                Zip = keyword.Zip ?? string.Empty,
                Sellers = keyword.Sellers != null ? string.Join(";", keyword.Sellers) : string.Empty,
                SellerType = keyword.SellerType ?? string.Empty,
                ViewName = keyword.ViewName ?? string.Empty,
                ListingType = keyword.ListingType != null ? string.Join(";", keyword.ListingType.Select(lt => lt.ToString())) : string.Empty,
                RequiredQuantity = keyword.RequiredQuantity,
                PurchasedQuantity = keyword.PurchasedQuantity,
                CapturedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Gets the remaining quantity needed (RequiredQuantity - PurchasedQuantity)
        /// </summary>
        public int RemainingQuantity => Math.Max(0, RequiredQuantity - PurchasedQuantity);

        /// <summary>
        /// Gets the completion percentage (PurchasedQuantity / RequiredQuantity * 100)
        /// </summary>
        public double CompletionPercentage => RequiredQuantity > 0 ? (double)PurchasedQuantity / RequiredQuantity * 100 : 0;

        /// <summary>
        /// Gets whether the purchase requirement is completed
        /// </summary>
        public bool IsCompleted => PurchasedQuantity >= RequiredQuantity;


    }
}
