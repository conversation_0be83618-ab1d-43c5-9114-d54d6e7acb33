﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using PushbulletSharp;
using PushbulletSharp.Models.Responses;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class FormPushbullet : RibbonForm
    {
        public List<Keyword2Find> ebaySearches;
        public PushbulletSender PushbulletTmp;
        private readonly SynchronizationContext _synchronizationContext;

        public FormPushbullet()
        {
            InitializeComponent();
            _synchronizationContext = SynchronizationContext.Current;
        }

        private async void FormPushbullet_Shown(object sender, EventArgs e)
        {
            this.Enabled = false;
            if (PushbulletTmp != null)
            {
                radioGroup1.SelectedIndex = PushbulletTmp.CombinePushes;
                if (!string.IsNullOrEmpty(PushbulletTmp.Token))
                {
                    if (await GetPushbulletDevices())
                    {
                        txtToken.Text = PushbulletTmp.Token;
                        btnSave.Enabled = true;
                        btnTestMessage.Enabled = true;
                        for (var i = 0; i < boxDeviceList.Properties.Items.Count; i++)
                        {
                            var device = boxDeviceList.Properties.Items[i] as Device;
                            if (device?.Iden == PushbulletTmp?.ActiveIden)
                            {
                                boxDeviceList.SelectedIndex = i;
                            }
                        }
                    }
                    else
                    {
                        btnSave.Enabled = false;
                        btnTestMessage.Enabled = false;
                    }
                }

                if (PushbulletTmp.TitleTemplate != null)
                    txtTitleTemplate.Text = PushbulletTmp.TitleTemplate;
                if (PushbulletTmp.BodyTemplate != null)
                    memoBodyTemplate.Text = PushbulletTmp.BodyTemplate;
                chkSendNotifications.Checked = PushbulletTmp.Enabled;
                chkCheckoutUrl.Checked = PushbulletTmp.SendCheckoutUrl;
                PushbulletTmp.PushReceivedEvent += PushbulletTmp_PushReceivedEvent;
                PushbulletTmp.PushExceptionEvent += PushbulletTmp_PushExceptionEvent;
            }

            this.Enabled = true;
            CreateMenuColumnsNames();
        }

        private void PushbulletTmp_PushExceptionEvent(object sender, PushbulletSender.PushExceptionEventHandlerArgs args)
        {
            _synchronizationContext.Post(PostExceptionMessage, args);
        }

        private void PostExceptionMessage(object e)
        {
            if (!(e is PushbulletSender.PushExceptionEventHandlerArgs args))
                return;

            lblStatusValue.Text = args.Message;
        }

        private void PushbulletTmp_PushReceivedEvent(object sender, PushbulletSender.PushReceivedEventHandlerArgs args)
        {
            _synchronizationContext.Post(PostLimitData, args);
        }

        private void PostLimitData(object e)
        {
            if (!(e is PushbulletSender.PushReceivedEventHandlerArgs args))
                return;

            long limit = args.XRatelimitLimit;
            long remaining = args.XRatelimitRemaining;
            long reset = args.XRatelimitReset;
            var limitResetDatetime = TimeZoneInfo.ConvertTimeFromUtc(Helpers.ConvertFromUnixTimestamp(reset), UserSettings.CurrentTimeZoneInfo);
            lblStatusValue.Text = $"Remaining {Math.Round((double)100 * remaining / limit, 2)}%. Limit resets at: {limitResetDatetime:T}";
        }

        private async void btnVerify_Click(object sender, EventArgs e)
        {
            try
            {
                txtToken.Text = txtToken.Text.Trim();
                if (!string.IsNullOrEmpty(txtToken.Text))
                {
                    PushbulletTmp.PbClient = new PushbulletClient(txtToken.Text);
                    PushbulletTmp.Token = txtToken.Text;
                    if (await GetPushbulletDevices())
                    {
                        btnSave.Enabled = true;
                        btnTestMessage.Enabled = true;
                        for (var i = 0; i < boxDeviceList.Properties.Items.Count; i++)
                        {
                            var device = boxDeviceList.Properties.Items[i] as Device;
                            if (device?.Iden == PushbulletTmp?.ActiveIden)
                            {
                                boxDeviceList.SelectedIndex = i;
                            }
                        }

                        XtraMessageBox.Show("Token verified successfully");
                    }
                    else
                    {
                        btnSave.Enabled = false;
                        btnTestMessage.Enabled = false;
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                btnSave.Enabled = false;
                btnTestMessage.Enabled = false;
            }
        }

        private void simpleTestMessage_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtTitleTemplate.Text) && radioGroup1.SelectedIndex == 1)
            {
                MessageBox.Show("Title template should not be empty");
                return;
            }

            if (string.IsNullOrWhiteSpace(memoBodyTemplate.Text))
            {
                MessageBox.Show("Body template should not be empty");
                return;
            }

            if (SetIden())
                return;

            if (!string.IsNullOrEmpty(PushbulletTmp.Token))
            {
                PushbulletTmp.PbClient = new PushbulletClient(PushbulletTmp.Token);
                var columnsOk = PushbulletTmp.SetTitleBody(txtTitleTemplate.Text, memoBodyTemplate.Text);
                if (columnsOk)
                {
                    var (row, _) = Helpers.GetRandomRow(ebaySearches);
                    if (row == null)
                    {
                        XtraMessageBox.Show("To send a test message, please, get some results first. Click \"Start\" button");
                        return;
                    }
                }
                else
                {
                    return;
                }

                if (PushbulletTmp.CombinePushes == 0)
                {
                    for (var i = 0; i < new Random().Next(5, 10); i++)
                    {
                        var (row, _) = Helpers.GetRandomRow(ebaySearches);
                        if (row != null)
                            PushbulletTmp.PushLinksNote(row);
                    }
                }
                else
                {
                    var (row, _) = Helpers.GetRandomRow(ebaySearches);
                    if (row != null)
                        PushbulletTmp.PushLink(row);
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            PushbulletTmp.Enabled = chkSendNotifications.Checked;
            PushbulletTmp.SendCheckoutUrl = chkCheckoutUrl.Checked;
            PushbulletTmp.Token = txtToken.Text;
            if (SetIden())
                return;

            var columnsOk = PushbulletTmp.SetTitleBody(txtTitleTemplate.Text, memoBodyTemplate.Text);
            if (columnsOk && !string.IsNullOrEmpty(PushbulletTmp.Token))
            {
                PushbulletTmp.PbClient = new PushbulletClient(PushbulletTmp.Token);
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private bool SetIden()
        {
            if (!(boxDeviceList.SelectedItem is Device device))
            {
                XtraMessageBox.Show("Please, choose your device from device list");
                return true;
            }

            PushbulletTmp.ActiveIden = device.Iden;
            PushbulletTmp.ActiveNickname = device.Nickname;
            return false;
        }

        private async Task<bool> GetPushbulletDevices()
        {
            try
            {
                boxDeviceList.Properties.Items.Clear();
                boxDeviceList.Text = "Loading...";
                try
                {
                    var deviceDict = await GetActiveDevices();
                    boxDeviceList.Properties.Items.AddRange(deviceDict.Values);
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message);
                }

                boxDeviceList.Text = "Select a device";
            }
            catch (Exception ex)
            {
                txtToken.Text = "";
                XtraMessageBox.Show(this, ex.Message);
                return false;
            }

            if (boxDeviceList.Properties.Items.Count == 0)
                return false;

            return true;
        }

        private async Task<Dictionary<string, Device>> GetActiveDevices()
        {
            var deviceDict = new Dictionary<string, Device>();
            if (PushbulletTmp.PbClient == null)
                return deviceDict;

            var devicesList = await Task.Run(() => PushbulletTmp.PbClient.CurrentUsersDevices(true));
            if (devicesList?.Devices?.Any(d => d.Manufacturer == "uBuyFirst") == false)
            {
                PushbulletSender.CreateDevice(PushbulletTmp.Token);
                devicesList = await Task.Run(() => PushbulletTmp.PbClient.CurrentUsersDevices(true));
            }

            if (devicesList?.Devices != null)
            {
                foreach (var device in devicesList.Devices)
                {
                    if (device.Manufacturer == "uBuyFirst")
                        PushbulletTmp.uBuyFirstIden = device.Iden;
                    else
                        deviceDict.Add(device.Iden, device);
                }
            }

            return deviceDict;
        }

        private void radioGroup1_SelectedIndexChanged(object sender, EventArgs e)
        {
            PushbulletTmp.CombinePushes = radioGroup1.SelectedIndex;
            if (PushbulletTmp.CombinePushes == 0)
            {
                lblTitleTemplate.Visible = false;
                txtTitleTemplate.Visible = false;
            }
            else
            {
                lblTitleTemplate.Visible = true;
                txtTitleTemplate.Visible = true;
            }
        }

        private async void boxDeviceList_MouseDown(object sender, MouseEventArgs e)
        {
            txtToken.Text = txtToken.Text.Trim();
            if (!string.IsNullOrEmpty(txtToken.Text))
            {
                await GetPushbulletDevices();
                boxDeviceList.ShowPopup();
            }
        }

        private void CreateMenuColumnsNames()
        {
            popupMenuColumnNames.ItemLinks.Clear();

            var allowedColumns = ColumnsManager.GetAllowedColumns();
            allowedColumns.Remove("Term");
            foreach (string s in allowedColumns)
            {
                var item = new BarButtonItem();
                item.Caption = $"{{{s}}}";
                item.ItemClick += OnItemClick;
                popupMenuColumnNames.ItemLinks.Add(item);
            }
        }

        private void OnItemClick(object sender, ItemClickEventArgs e)
        {
            if (!(sender is RibbonBarManager))
                return;

            BarButtonItem item = e.Item as BarButtonItem;
            if (item != null && !string.IsNullOrEmpty(txtTitleTemplate.Tag as string))
            {
                var inserText = item.Caption + " ";
                var selectionIndex = txtTitleTemplate.SelectionStart;
                txtTitleTemplate.Text = txtTitleTemplate.Text.Insert(selectionIndex, inserText);
                txtTitleTemplate.SelectionStart = selectionIndex + inserText.Length;
            }

            if (item != null && !string.IsNullOrEmpty(memoBodyTemplate.Tag as string))
            {
                var inserText = item.Caption + " ";
                var selectionIndex = memoBodyTemplate.SelectionStart;
                memoBodyTemplate.Focus();
                memoBodyTemplate.Text = memoBodyTemplate.Text.Insert(selectionIndex, inserText);
                memoBodyTemplate.SelectionStart = selectionIndex + inserText.Length;
            }
        }

        private void popupMenuColumnNames_BeforePopup(object sender, System.ComponentModel.CancelEventArgs e)
        {
            var menu = sender as PopupMenu;
            if (menu?.Activator is Control control)
            {
                if (control.Focused)
                {
                    if (txtTitleTemplate.Text == control.Text)
                    {
                        memoBodyTemplate.Tag = null;
                        txtTitleTemplate.Tag = "Focused";
                    }
                    else
                    {
                        memoBodyTemplate.Tag = "Focused";
                        txtTitleTemplate.Tag = null;
                    }
                }
                else
                {
                    e.Cancel = true;
                }
            }
        }

        private void lnkPushbulletAuth_Click(object sender, EventArgs e)
        {
            Process.Start("https://www.pushbullet.com/");
        }

        private void lnkWhyDoINeedThis_Click(object sender, EventArgs e)
        {
            Process.Start("https://ubuyfirst.com/receive-new-ebay-listings-on-mobile-with-pushbullet/");
        }

        private void chkCheckoutUrl_CheckedChanged(object sender, EventArgs e)
        {
            PushbulletTmp.SendCheckoutUrl = chkCheckoutUrl.Checked;
        }
    }
}
