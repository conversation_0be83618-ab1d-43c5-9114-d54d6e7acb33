﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Data;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Purchasing;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class PurchaseExecutionServiceLiveModeTests
    {
        private RestockModeEnum _originalRestockMode;

        [TestInitialize]
        public void TestInitialize()
        {
            // Save the original restock mode to restore after tests
            _originalRestockMode = UserSettings.RestockMode;
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Restore the original restock mode
            UserSettings.RestockMode = _originalRestockMode;
        }

        /// <summary>
        /// Creates a test EBaySite for testing purposes
        /// </summary>
        private static EBaySite CreateTestEBaySite()
        {
            // Use the US eBay site format from CountryProvider
            return new EBaySite("EBAY-US\tEBAY_US\ten-US\tUS\teBay US\t0\tUS\tebay.com\tpicsuffix\t1\thttps://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466");
        }

        [TestMethod]
        public void LiveMode_PaymentFailed_ShouldNotBeConsideredSuccessful()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.LiveMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.PaymentFailed;
            dataList.Order.FailureReasonMessage = "Payment failed due to insufficient funds";

            // Act & Assert
            // The purchase should be considered failed, not successful
            // This test verifies that PaymentFailed status in Live Mode is not treated as success
            Assert.AreEqual(BuyingService.Order.CheckoutState.PaymentFailed, dataList.Order.CheckoutStatus);
            Assert.IsFalse(dataList.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess);
        }

        [TestMethod]
        public void LiveMode_TestPurchaseStatus_ShouldNotBeConsideredSuccessful()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.LiveMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.TestPurchase;

            // Act & Assert
            // In Live Mode, TestPurchase status should NOT be considered successful
            // Only PaymentSuccess should be considered successful in Live Mode
            Assert.AreEqual(BuyingService.Order.CheckoutState.TestPurchase, dataList.Order.CheckoutStatus);
            Assert.IsFalse(dataList.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess);
        }

        [TestMethod]
        public void TestMode_TestPurchaseStatus_ShouldBeConsideredSuccessful()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.TestMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.TestPurchase;

            // Act & Assert
            // In Test Mode, TestPurchase status should be considered successful
            Assert.AreEqual(BuyingService.Order.CheckoutState.TestPurchase, dataList.Order.CheckoutStatus);
            Assert.IsTrue(dataList.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase);
        }

        [TestMethod]
        public void LiveMode_PaymentSuccess_ShouldBeConsideredSuccessful()
        {
            // Arrange
            UserSettings.RestockMode = RestockModeEnum.LiveMode;

            var dataList = new DataList();
            dataList.Order = new BuyingService.BuyOrder("123456", "Test Item", CreateTestEBaySite(), 1, new CurrencyAmount(9.99, "USD"), Placeoffer.OrderAction.PayWithCreditCard);
            dataList.Order.CheckoutStatus = BuyingService.Order.CheckoutState.PaymentSuccess;

            // Act & Assert
            // In Live Mode, PaymentSuccess status should be considered successful
            Assert.AreEqual(BuyingService.Order.CheckoutState.PaymentSuccess, dataList.Order.CheckoutStatus);
            Assert.IsTrue(dataList.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess);
        }
    }
}
