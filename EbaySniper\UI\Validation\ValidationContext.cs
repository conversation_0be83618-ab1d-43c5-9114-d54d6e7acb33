using System.Collections.Generic;

namespace uBuyFirst.UI.Validation
{
    /// <summary>
    /// Provides context information for validation operations
    /// </summary>
    public class ValidationContext
    {
        private readonly Dictionary<string, object> _properties;

        /// <summary>
        /// Initializes a new instance of ValidationContext
        /// </summary>
        public ValidationContext()
        {
            _properties = new Dictionary<string, object>();
        }

        /// <summary>
        /// Gets or sets the unique identifier for the node being validated
        /// </summary>
        public string NodeId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets additional context properties
        /// </summary>
        public IReadOnlyDictionary<string, object> Properties => _properties;

        /// <summary>
        /// Sets a context property
        /// </summary>
        /// <param name="key">The property key</param>
        /// <param name="value">The property value</param>
        public void SetProperty(string key, object value)
        {
            if (!string.IsNullOrEmpty(key))
            {
                _properties[key] = value;
            }
        }

        /// <summary>
        /// Gets a context property
        /// </summary>
        /// <typeparam name="T">The expected type of the property</typeparam>
        /// <param name="key">The property key</param>
        /// <returns>The property value, or default(T) if not found</returns>
        public T GetProperty<T>(string key)
        {
            if (string.IsNullOrEmpty(key) || !_properties.ContainsKey(key))
                return default(T);

            var value = _properties[key];
            if (value is T typedValue)
                return typedValue;

            return default(T);
        }

        /// <summary>
        /// Checks if a property exists
        /// </summary>
        /// <param name="key">The property key</param>
        /// <returns>True if the property exists</returns>
        public bool HasProperty(string key)
        {
            return !string.IsNullOrEmpty(key) && _properties.ContainsKey(key);
        }

        /// <summary>
        /// Removes a property
        /// </summary>
        /// <param name="key">The property key</param>
        /// <returns>True if the property was removed</returns>
        public bool RemoveProperty(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            return _properties.Remove(key);
        }

        /// <summary>
        /// Clears all properties
        /// </summary>
        public void ClearProperties()
        {
            _properties.Clear();
        }

        /// <summary>
        /// Creates a validation context with a node ID
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <returns>A new validation context</returns>
        public static ValidationContext ForNode(string nodeId)
        {
            return new ValidationContext { NodeId = nodeId };
        }

        /// <summary>
        /// Creates a validation context with a node ID and properties
        /// </summary>
        /// <param name="nodeId">The node identifier</param>
        /// <param name="properties">Initial properties</param>
        /// <returns>A new validation context</returns>
        public static ValidationContext ForNode(string nodeId, Dictionary<string, object> properties)
        {
            var context = new ValidationContext { NodeId = nodeId };
            
            if (properties != null)
            {
                foreach (var kvp in properties)
                {
                    context.SetProperty(kvp.Key, kvp.Value);
                }
            }

            return context;
        }
    }
}
