using System;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.RestockReporting
{
    /// <summary>
    /// Integration test to verify the new RestockReporting system works with FormRestockReport
    /// </summary>
    public class IntegrationTest
    {
        public static async Task<bool> TestFormRestockReportIntegration()
        {
            try
            {
                Console.WriteLine("=== RestockReporting Integration Test ===");

                // Setup test environment
                var testDir = Path.Combine(Path.GetTempPath(), "RestockReportingTest", DateTime.Now.ToString("yyyyMMdd_HHmmss"));
                Directory.CreateDirectory(testDir);

                var options = new ItemHistoryOptions
                {
                    BasePath = testDir,
                    ErrorLogPath = Path.Combine(testDir, "errors"),
                    EnableLogging = true,
                    CreateDailyFolders = true
                };

                // Create services
                var logger = new FileItemHistoryLogger(options);
                var exporter = new ItemHistoryExporter(options);

                Console.WriteLine($"Test directory: {testDir}");

                // Create sample data to simulate what FormRestockReport would export
                await CreateSampleData(logger);

                // Test export functionality (what FormRestockReport.btnExport_Click would do)
                var csvPath = Path.Combine(testDir, "test_export.csv");
                var startDate = DateTime.Now.AddDays(-1);
                var endDate = DateTime.Now;

                Console.WriteLine("Testing CSV export...");
                await exporter.ExportToCsvAsync(startDate, endDate, csvPath);

                // Verify the CSV file
                if (!File.Exists(csvPath))
                {
                    Console.WriteLine("❌ CSV file was not created");
                    return false;
                }

                var csvContent = File.ReadAllText(csvPath);
                var lines = csvContent.Split('\n');

                Console.WriteLine($"✅ CSV file created with {lines.Length} lines");

                // Check for comprehensive headers (47 columns)
                var headerLine = lines[0];
                var headers = headerLine.Split(',');

                Console.WriteLine($"✅ CSV has {headers.Length} columns");

                // Verify key headers are present
                var expectedHeaders = new[] { "Timestamp", "Outcome", "Reason", "ItemId", "Title", "CurrentPrice",
                    "Condition", "Seller", "KeywordId", "Alias", "Keywords", "RequiredQuantity", "PurchasedQuantity",
                    "FilterAlias", "Expression", "Matched", "TransactionAttempted", "TransactionSuccess" };

                foreach (var expectedHeader in expectedHeaders)
                {
                    if (!headerLine.Contains(expectedHeader))
                    {
                        Console.WriteLine($"❌ Missing expected header: {expectedHeader}");
                        return false;
                    }
                }

                Console.WriteLine("✅ All expected headers found");

                // Get item count
                var itemCount = await exporter.GetHistoryCountAsync(startDate, endDate);
                Console.WriteLine($"✅ Found {itemCount} processed items");

                // Show sample of CSV content
                Console.WriteLine("\n=== Sample CSV Content ===");
                for (int i = 0; i < Math.Min(3, lines.Length); i++)
                {
                    Console.WriteLine($"Line {i + 1}: {lines[i].Substring(0, Math.Min(100, lines[i].Length))}...");
                }

                Console.WriteLine("\n=== Integration Test Results ===");
                Console.WriteLine($"✅ CSV Export: SUCCESS");
                Console.WriteLine($"✅ Column Count: {headers.Length} (vs old system's 34)");
                Console.WriteLine($"✅ Data Items: {itemCount}");
                Console.WriteLine($"✅ File Location: {csvPath}");

                // Cleanup
                try
                {
                    Directory.Delete(testDir, true);
                    Console.WriteLine("✅ Test cleanup completed");
                }
                catch
                {
                    Console.WriteLine($"⚠️ Could not cleanup test directory: {testDir}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Integration test failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        private static async Task CreateSampleData(FileItemHistoryLogger logger)
        {
            Console.WriteLine("Creating sample data...");

            // Create sample item processing contexts
            for (int i = 1; i <= 5; i++)
            {
                var context = new ItemProcessingContext
                {
                    Timestamp = DateTime.UtcNow.AddMinutes(-i * 10),
                    Outcome = i % 2 == 0 ? "Purchased" : "Filtered Out",
                    Reason = i % 2 == 0 ? "Successfully purchased item" : "Item did not match filter criteria",
                    ItemData = new ItemHistoryData
                    {
                        ItemId = $"ITEM{i:D3}",
                        Title = $"Test Product {i}",
                        CurrentPrice = 25.99m + i,
                        Condition = "New",
                        Seller = $"TestSeller{i}",
                        ShippingCost = 5.99m,
                        Location = "United States",
                        QuantityAvailable = 10 + i,
                        ListingType = "FixedPrice",
                        ItemUrl = $"https://ebay.com/item{i}",
                        Site = "eBay.com"
                    },
                    KeywordState = new KeywordSnapshot
                    {
                        KeywordId = $"KW{i:D3}",
                        Alias = $"TestKeyword{i}",
                        Keywords = $"test,product,keyword{i}",
                        RequiredQuantity = 5,
                        PurchasedQuantity = i % 2 == 0 ? 1 : 0,
                        PriceMin = 20.00,
                        PriceMax = 50.00,
                        Condition = new[] { "New", "Used" },
                        Sellers = $"TestSeller{i}",
                        SellerType = "Individual",
                        EbaySiteName = "eBay.com",
                        SearchInDescription = true
                    },
                    FilterRule = new FilterRuleContext
                    {
                        FilterAlias = $"TestFilter{i}",
                        Expression = "price < 30 AND condition = 'New'",
                        Matched = i % 2 == 0,
                        EvaluationResult = i % 2 == 0 ? "Filter matched" : "Price too high",
                        EvaluatedAt = DateTime.UtcNow.AddMinutes(-i * 10)
                    },
                    TransactionResult = i % 2 == 0 ? new TransactionResult
                    {
                        Attempted = true,
                        Success = true,
                        PurchasePrice = 25.99m + i,
                        Quantity = 1,
                        CompletedAt = DateTime.UtcNow.AddMinutes(-i * 10)
                    } : new TransactionResult
                    {
                        Attempted = false,
                        Success = false
                    }
                };

                await logger.LogItemProcessingAsync(context);
            }

            Console.WriteLine("✅ Sample data created");
        }
    }
}
