﻿using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls; // Added for ButtonStyle

namespace uBuyFirst.GUI
{
    public enum FlyoutStyle
    {
        Information,
        Success,
        Failure
    }

    public enum TopmostStrategy
    {
        /// <summary>Use SetChildIndex within current parent container</summary>
        SetChildIndex,
        /// <summary>Use recursive parent z-order management</summary>
        RecursiveParents,
        /// <summary>Reparent to main form for true topmost behavior</summary>
        MainFormParent,
        /// <summary>Combine all strategies for maximum effectiveness</summary>
        Combined
    }

    public class FlyoutPanelSnackBar : IDisposable
    {
        public FlyoutPanel Panel;
        public FlyoutPanelControl PanelControl;

        private LabelControl messageLabel;
        private PictureEdit iconPictureEdit;
        private SimpleButton okButton;
        private TableLayoutPanel layoutPanel;
        private System.Windows.Forms.Timer dismissTimer;
        private Control parentControl;

        public FlyoutPanelSnackBar(Control parent)
        {
            this.parentControl = parent;

            // Instantiate core controls
            this.Panel = new DevExpress.Utils.FlyoutPanel();
            this.PanelControl = new DevExpress.Utils.FlyoutPanelControl();
            this.layoutPanel = new TableLayoutPanel();
            this.iconPictureEdit = new PictureEdit();
            this.messageLabel = new LabelControl();
            this.okButton = new SimpleButton();
            this.dismissTimer = new System.Windows.Forms.Timer();

            // --- Begin Configuration ---

            // Configure Layout Panel
            this.layoutPanel.SuspendLayout();
            this.layoutPanel.Dock = DockStyle.Fill;
            //this.layoutPanel.BackColor = Color.Transparent; // Make transparent
            this.layoutPanel.ColumnCount = 3;
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 0F)); // Icon column starts hidden
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F)); // Label column
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Button column
            this.layoutPanel.RowCount = 1;
            this.layoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.layoutPanel.Name = "layoutPanel";
            this.layoutPanel.Padding = new Padding(5); // Add padding inside layout

            // Configure Icon PictureEdit
            ((System.ComponentModel.ISupportInitialize)(this.iconPictureEdit.Properties)).BeginInit();
            this.iconPictureEdit.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Auto;
            this.iconPictureEdit.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Squeeze;
            this.iconPictureEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.iconPictureEdit.Properties.AllowFocused = false;
            this.iconPictureEdit.Properties.ReadOnly = true;
            this.iconPictureEdit.Dock = DockStyle.Fill;
            this.iconPictureEdit.Name = "iconPictureEdit";
            this.iconPictureEdit.Margin = new Padding(3);
            this.iconPictureEdit.Visible = false; // Start hidden
            ((System.ComponentModel.ISupportInitialize)(this.iconPictureEdit.Properties)).EndInit();

            // Configure Message Label
            //this.messageLabel.Appearance.ForeColor = SnackbarForeColor; // Set text color
            this.messageLabel.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.messageLabel.AutoSizeMode = LabelAutoSizeMode.Vertical;
            this.messageLabel.Font = new Font(this.messageLabel.Appearance.Font.FontFamily, this.okButton.Appearance.Font.Size + 1, FontStyle.Bold);
            this.messageLabel.Dock = DockStyle.Fill;
            this.messageLabel.Name = "messageLabel";
            // Padding removed, handled by layoutPanel now

            // Configure OK Button
            this.okButton.Text = "OK";
            this.okButton.Name = "okButton";
            this.okButton.Appearance.Font = new Font(this.okButton.Appearance.Font.FontFamily, this.okButton.Appearance.Font.Size + 1, FontStyle.Bold);
            //this.okButton.Appearance.ForeColor = ActionButtonColor; // Set accent color
            this.okButton.Anchor = AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;
            this.okButton.AutoSize = false;
            var preferredSize = this.okButton.PreferredSize;
            this.okButton.Size = new Size(preferredSize.Width * 2, preferredSize.Height * 2);
            this.okButton.Margin = new Padding(5, 5, 5, 5); // Standard margin
            this.okButton.Padding = new Padding(10, 0, 10, 0);
            this.okButton.ButtonStyle = BorderStyles.Simple; // Use a flatter style
            this.okButton.Click += OkButton_Click;

            // Add controls to Layout Panel
            this.layoutPanel.Controls.Add(this.iconPictureEdit, 0, 0);
            this.layoutPanel.Controls.Add(this.messageLabel, 1, 0);
            this.layoutPanel.Controls.Add(this.okButton, 2, 0);
            this.layoutPanel.ResumeLayout(false);
            this.layoutPanel.PerformLayout();


            // Configure PanelControl
            ((System.ComponentModel.ISupportInitialize)(this.PanelControl)).BeginInit();
            this.PanelControl.SuspendLayout();
            //this.PanelControl.Appearance.BackColor = SnackbarBackColor; // Set background here
            this.PanelControl.Controls.Add(this.layoutPanel);
            this.PanelControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PanelControl.FlyoutPanel = this.Panel;
            this.PanelControl.Location = new System.Drawing.Point(0, 0);
            this.PanelControl.Name = "flyoutPanelManagerControl";
            //this.PanelControl.Size = new System.Drawing.Size(340, 40);
            this.PanelControl.TabIndex = 0;
            this.PanelControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.PanelControl)).EndInit();

            // Configure Panel
            ((System.ComponentModel.ISupportInitialize)(this.Panel)).BeginInit();
            this.Panel.SuspendLayout();
            this.Panel.Appearance.BackColor = Color.Transparent; // Make outer panel transparent
            this.Panel.Appearance.Options.UseBackColor = true;
            this.Panel.Controls.Add(this.PanelControl);
            this.Panel.Name = "flyoutPanelManager";
            this.Panel.OptionsButtonPanel.ShowButtonPanel = false;
            this.Panel.Size = new System.Drawing.Size(350, 45);
            this.Panel.Padding = new System.Windows.Forms.Padding(5); // Padding around the PanelControl
            //this.Panel.AutoSize = true; // Let Panel size to PanelControl + Padding
            //this.Panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            //this.Panel.MaximumSize = new Size(600, 0); // Set a max width (0 means no height limit)
            this.Panel.TabIndex = 105;
            this.Panel.Options.AnimationType = DevExpress.Utils.Win.PopupToolWindowAnimation.Fade;
            this.Panel.Options.CloseOnOuterClick = false;
            this.Panel.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Bottom;

            // Timer Configuration
            this.dismissTimer.Interval = (int)TimeSpan.FromSeconds(15).TotalMilliseconds;
            this.dismissTimer.Tick += DismissTimer_Tick;

            this.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Panel)).EndInit();

            // --- End Configuration ---

            parentControl?.Controls.Add(this.Panel);
            //this.Panel.BringToFront();
            this.Panel.Hide();
        }

        public void ShowPopup(Control ownerControl, string message, FlyoutStyle style = FlyoutStyle.Information)
        {
            if (Panel.IsDisposed) return;

            Panel.HidePopup();

            messageLabel.Text = message;
            Panel.OwnerControl = ownerControl ?? this.parentControl;

            // --- Icon Logic Temporarily Removed ---
            /*
            SvgImage svgImage = null;
            try
            {
                switch (style)
                {
                    case FlyoutStyle.Success:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Completed.svg"); // Replace with correct loading method
                        break;
                    case FlyoutStyle.Failure:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Error.svg"); // Replace with correct loading method
                        break;
                    case FlyoutStyle.Information:
                    default:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Info.svg"); // Replace with correct loading method
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading SVG image for FlyoutPanelSnackBar: {ex.Message}");
                svgImage = null;
            }
            iconPictureEdit.EditValue = svgImage;
            */
            iconPictureEdit.Visible = false;
            layoutPanel.ColumnStyles[0].Width = 0F;
            switch (style)
            {
                case FlyoutStyle.Success:
                    okButton.Appearance.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap Success Green
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(40, 167, 69);
                    messageLabel.ForeColor = Color.Black; // Darker success message color
                    PanelControl.Appearance.BackColor = Color.FromArgb(23, 200, 16); // Lighter green background
                    break;

                case FlyoutStyle.Information:
                    okButton.Appearance.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap Primary Blue
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(0, 123, 255);
                    messageLabel.ForeColor = Color.FromArgb(0, 123, 255);
                    PanelControl.Appearance.BackColor = Color.FromArgb(217, 237, 247); // Lighter blue background
                    break;

                case FlyoutStyle.Failure:
                    okButton.Appearance.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap Danger Red
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(220, 53, 69);
                    messageLabel.ForeColor = Color.FromArgb(220, 53, 69);
                    PanelControl.Appearance.BackColor = Color.FromArgb(248, 215, 218); // Lighter red background
                    break;

            }
            PanelControl.Appearance.Options.UseBackColor = true; // Ensure background color is used

            dismissTimer.Stop();

            dismissTimer.Start();

            // Ensure the panel appears topmost within its parent container without bringing entire program to front
            //EnsurePanelTopmost();
            if (!Debugger.IsAttached)
                Panel.ShowPopup(false);
        }

        public void ShowSuccess(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Success);
        public void ShowInformation(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Information);
        public void ShowFailure(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Failure);

        /// <summary>
        /// Shows popup with configurable topmost strategy for maximum control over z-order positioning
        /// </summary>
        public void ShowPopupWithStrategy(Control ownerControl, string message, FlyoutStyle style = FlyoutStyle.Information, TopmostStrategy strategy = TopmostStrategy.Combined)
        {
            try
            {
                switch (strategy)
                {
                    case TopmostStrategy.SetChildIndex:
                        ShowPopup(ownerControl, message, style);
                        break;
                    case TopmostStrategy.RecursiveParents:
                        ShowPopup(ownerControl, message, style);
                        // EnsurePanelTopmost already includes recursive logic
                        break;
                    case TopmostStrategy.MainFormParent:
                        ShowPopupTopmost(ownerControl, message, style);
                        break;
                    case TopmostStrategy.Combined:
                    default:
                        // Use main form approach for maximum effectiveness
                        ShowPopupTopmost(ownerControl, message, style);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing popup with strategy {strategy}: {ex.Message}");
                // Fallback to basic ShowPopup
                ShowPopup(ownerControl, message, style);
            }
        }

        /// <summary>
        /// Alternative method to show popup with main form as direct parent for true topmost behavior
        /// </summary>
        public void ShowPopupTopmost(Control ownerControl, string message, FlyoutStyle style = FlyoutStyle.Information)
        {
            try
            {
                // Find the main form to use as direct parent
                var mainForm = FindMainForm(ownerControl ?? parentControl);
                if (mainForm != null && mainForm != parentControl)
                {
                    // Temporarily reparent to main form for true topmost behavior
                    ReparentToMainForm(mainForm);
                }

                // Use regular ShowPopup method
                ShowPopup(ownerControl, message, style);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing topmost popup: {ex.Message}");
                // Fallback to regular ShowPopup
                ShowPopup(ownerControl, message, style);
            }
        }

        /// <summary>
        /// Finds the main form in the control hierarchy
        /// </summary>
        private Form FindMainForm(Control control)
        {
            if (control == null) return null;

            var current = control;
            while (current.Parent != null)
            {
                current = current.Parent;
            }

            return current as Form;
        }

        /// <summary>
        /// Temporarily reparents the panel to the main form for true topmost behavior
        /// </summary>
        private void ReparentToMainForm(Form mainForm)
        {
            try
            {
                if (parentControl != null && parentControl.Controls.Contains(Panel))
                {
                    parentControl.Controls.Remove(Panel);
                }

                if (!mainForm.Controls.Contains(Panel))
                {
                    mainForm.Controls.Add(Panel);
                    mainForm.Controls.SetChildIndex(Panel, 0);
                }

                // Update parent reference
                parentControl = mainForm;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reparenting to main form: {ex.Message}");
            }
        }

        /// <summary>
        /// Ensures the FlyoutPanel appears topmost within its parent container without bringing the entire program to front
        /// </summary>
        private void EnsurePanelTopmost()
        {
            try
            {
                // Method 1: Use SetChildIndex to move panel to front of z-order within parent container
                if (parentControl != null && parentControl.Controls.Contains(Panel))
                {
                    parentControl.Controls.SetChildIndex(Panel, 0);
                }

                // Method 2: Set TabIndex to ensure proper tab order positioning (lowest value = topmost)
                Panel.TabIndex = 0;

                // Method 3: DevExpress-specific options for better layering control
                // Ensure the panel doesn't close when clicking outside, which helps maintain topmost behavior
                Panel.Options.CloseOnOuterClick = false;

                // Use Manual anchor type for more precise control over positioning
                if (Panel.Options.AnchorType != DevExpress.Utils.Win.PopupToolWindowAnchor.Manual)
                {
                    Panel.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Bottom;
                }

                // Method 4: Ensure panel is visible and enabled
                Panel.Visible = true;
                Panel.Enabled = true;

                // Method 5: Recursive parent z-order management for nested containers
                EnsureParentContainersTopmost(parentControl);
            }
            catch (Exception ex)
            {
                // Log error but don't let it prevent the popup from showing
                System.Diagnostics.Debug.WriteLine($"Error ensuring panel topmost: {ex.Message}");
            }
        }

        /// <summary>
        /// Recursively ensures parent containers are also positioned properly in their respective z-orders
        /// </summary>
        private void EnsureParentContainersTopmost(Control control)
        {
            try
            {
                if (control?.Parent != null)
                {
                    // Move the current control to front within its parent
                    control.Parent.Controls.SetChildIndex(control, 0);

                    // Recursively apply to parent containers (but stop at Form level to avoid bringing entire app to front)
                    if (!(control.Parent is Form))
                    {
                        EnsureParentContainersTopmost(control.Parent);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error ensuring parent containers topmost: {ex.Message}");
            }
        }

        private void DismissTimer_Tick(object sender, EventArgs e)
        {
            dismissTimer.Stop();
            if (!Panel.IsDisposed)
            {
                Panel.HidePopup();
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            dismissTimer.Stop();
            if (!Panel.IsDisposed)
            {
                Panel.HidePopup();
            }
        }

        private bool disposedValue = false;
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    dismissTimer?.Stop();
                    dismissTimer?.Dispose();
                    okButton?.Dispose();
                    iconPictureEdit?.Dispose();
                    messageLabel?.Dispose();
                    layoutPanel?.Dispose();
                    Panel?.Dispose();
                }
                disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
